(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/@core/components/mui/TextField.jsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
// React Imports
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
// MUI Imports
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$styles$2f$styled$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__$3c$export__default__as__styled$3e$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/styles/styled.js [app-client] (ecmascript) <locals> <export default as styled>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$TextField$2f$TextField$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/TextField/TextField.js [app-client] (ecmascript)");
'use client';
;
;
;
;
const TextFieldStyled = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$styles$2f$styled$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__$3c$export__default__as__styled$3e$__["styled"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$TextField$2f$TextField$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(({ theme })=>({
        '& .MuiInputLabel-root': {
            transform: 'none',
            width: 'fit-content',
            maxWidth: '100%',
            lineHeight: 1.153,
            position: 'relative',
            fontSize: theme.typography.body2.fontSize,
            marginBottom: theme.spacing(1),
            color: 'var(--mui-palette-text-primary)',
            '&:not(.Mui-error).MuiFormLabel-colorPrimary.Mui-focused': {
                color: 'var(--mui-palette-primary-main) !important'
            },
            '&.Mui-disabled': {
                color: 'var(--mui-palette-text-disabled)'
            },
            '&.Mui-error': {
                color: 'var(--mui-palette-error-main)'
            }
        },
        '& .MuiInputBase-root': {
            backgroundColor: 'transparent !important',
            border: `1px solid var(--mui-palette-customColors-inputBorder)`,
            '&:not(.Mui-focused):not(.Mui-disabled):not(.Mui-error):hover': {
                borderColor: 'var(--mui-palette-action-active)'
            },
            '&:before, &:after': {
                display: 'none'
            },
            '&.MuiInputBase-sizeSmall': {
                borderRadius: 'var(--mui-shape-borderRadius)'
            },
            '&.Mui-error': {
                borderColor: 'var(--mui-palette-error-main)'
            },
            '&.Mui-focused': {
                borderWidth: 2,
                '& .MuiInputBase-input:not(.MuiInputBase-readOnly):not([readonly])::placeholder': {
                    transform: 'translateX(4px)'
                },
                '& :not(textarea).MuiFilledInput-input': {
                    padding: '6.25px 13px'
                },
                '&:not(.Mui-error).MuiInputBase-colorPrimary': {
                    borderColor: 'var(--mui-palette-primary-main)',
                    boxShadow: 'var(--mui-customShadows-primary-sm)'
                },
                '&.MuiInputBase-colorSecondary': {
                    borderColor: 'var(--mui-palette-secondary-main)'
                },
                '&.MuiInputBase-colorInfo': {
                    borderColor: 'var(--mui-palette-info-main)'
                },
                '&.MuiInputBase-colorSuccess': {
                    borderColor: 'var(--mui-palette-success-main)'
                },
                '&.MuiInputBase-colorWarning': {
                    borderColor: 'var(--mui-palette-warning-main)'
                },
                '&.MuiInputBase-colorError': {
                    borderColor: 'var(--mui-palette-error-main)'
                },
                '&.Mui-error': {
                    borderColor: 'var(--mui-palette-error-main)'
                }
            },
            '&.Mui-disabled': {
                backgroundColor: 'var(--mui-palette-action-hover) !important'
            }
        },
        // Adornments
        '& .MuiInputAdornment-root': {
            marginBlockStart: '0px !important',
            '&.MuiInputAdornment-positionStart + .MuiInputBase-input:not(textarea)': {
                paddingInlineStart: '0px !important'
            }
        },
        '& .MuiInputBase-inputAdornedEnd.MuiInputBase-input': {
            paddingInlineEnd: '0px !important'
        },
        '& .MuiInputBase-sizeSmall.MuiInputBase-adornedStart.Mui-focused': {
            paddingInlineStart: '13px',
            '& .MuiInputBase-input': {
                paddingInlineStart: '0px !important'
            }
        },
        '& .MuiInputBase-sizeSmall.MuiInputBase-adornedStart:not(.MuiAutocomplete-inputRoot)': {
            paddingInlineStart: '14px'
        },
        '& .MuiInputBase-sizeSmall.MuiInputBase-adornedEnd:not(.MuiAutocomplete-inputRoot)': {
            paddingInlineEnd: '14px'
        },
        '& .MuiInputBase-sizeSmall.MuiInputBase-adornedEnd.Mui-focused:not(.MuiAutocomplete-inputRoot)': {
            paddingInlineEnd: '13px',
            '& .MuiInputBase-input': {
                paddingInlineEnd: '0px !important'
            }
        },
        '& :not(.MuiInputBase-sizeSmall).MuiInputBase-adornedStart.Mui-focused': {
            paddingInlineStart: '15px',
            '& .MuiInputBase-input': {
                paddingInlineStart: '0px !important'
            }
        },
        '& :not(.MuiInputBase-sizeSmall).MuiInputBase-adornedStart': {
            paddingInlineStart: '16px'
        },
        '& :not(.MuiInputBase-sizeSmall).MuiInputBase-adornedEnd.Mui-focused': {
            paddingInlineEnd: '15px',
            '& .MuiInputBase-input': {
                paddingInlineEnd: '0px !important'
            }
        },
        '& :not(.MuiInputBase-sizeSmall).MuiInputBase-adornedEnd': {
            paddingInlineEnd: '16px'
        },
        '& .MuiInputAdornment-sizeMedium': {
            'i, svg': {
                fontSize: '1.25rem'
            }
        },
        '& .MuiInputBase-input': {
            '&:not(textarea).MuiInputBase-inputSizeSmall': {
                padding: '7.25px 14px'
            },
            '&:not(.MuiInputBase-readOnly):not([readonly])::placeholder': {
                transition: theme.transitions.create([
                    'opacity',
                    'transform'
                ], {
                    duration: theme.transitions.duration.shorter
                })
            }
        },
        '& :not(.MuiInputBase-sizeSmall).MuiInputBase-root': {
            borderRadius: '8px',
            fontSize: '17px',
            lineHeight: '1.41',
            '& .MuiInputBase-input': {
                padding: '10.8px 16px'
            },
            '&.Mui-focused': {
                '& .MuiInputBase-input': {
                    padding: '9.8px 15px'
                }
            }
        },
        '& .MuiFormHelperText-root': {
            lineHeight: 1.154,
            margin: theme.spacing(1, 0, 0),
            fontSize: theme.typography.body2.fontSize,
            '&.Mui-error': {
                color: 'var(--mui-palette-error-main)'
            },
            '&.Mui-disabled': {
                color: 'var(--mui-palette-text-disabled)'
            }
        },
        // For Select
        '& .MuiSelect-select.MuiInputBase-inputSizeSmall, & .MuiNativeSelect-select.MuiInputBase-inputSizeSmall': {
            '& ~ i, & ~ svg': {
                inlineSize: '1.125rem',
                blockSize: '1.125rem'
            }
        },
        '& .MuiSelect-select': {
            // lineHeight: 1.5,
            minHeight: 'unset !important',
            lineHeight: '1.4375em',
            '&.MuiInputBase-input': {
                paddingInlineEnd: '32px !important'
            }
        },
        '& .Mui-focused .MuiSelect-select': {
            '& ~ i, & ~ svg': {
                right: '0.9375rem'
            }
        },
        '& .MuiSelect-select:focus, & .MuiNativeSelect-select:focus': {
            backgroundColor: 'transparent'
        },
        // For Autocomplete
        '& :not(.MuiInputBase-sizeSmall).MuiAutocomplete-inputRoot': {
            paddingBlock: '5.55px',
            '& .MuiAutocomplete-input': {
                paddingInline: '8px !important',
                paddingBlock: '5.25px !important'
            },
            '&.Mui-focused .MuiAutocomplete-input': {
                paddingInlineStart: '7px !important'
            },
            '&.Mui-focused': {
                paddingBlock: '4.55px !important'
            },
            '& .MuiAutocomplete-endAdornment': {
                top: 'calc(50% - 12px)'
            }
        },
        '& .MuiAutocomplete-inputRoot.MuiInputBase-sizeSmall': {
            paddingBlock: '4.75px !important',
            paddingInlineStart: '10px',
            '&.Mui-focused': {
                paddingBlock: '3.75px !important',
                paddingInlineStart: '9px',
                '.MuiAutocomplete-input': {
                    paddingBlock: '2.5px',
                    paddingInline: '3px !important'
                }
            },
            '& .MuiAutocomplete-input': {
                paddingInline: '3px !important'
            }
        },
        '& .MuiAutocomplete-inputRoot': {
            display: 'flex',
            gap: '0.25rem',
            '& .MuiAutocomplete-tag': {
                margin: 0
            }
        },
        '& .MuiAutocomplete-inputRoot.Mui-focused .MuiAutocomplete-endAdornment': {
            right: '.9375rem'
        },
        // For Textarea
        '& .MuiInputBase-multiline': {
            '&.MuiInputBase-sizeSmall': {
                padding: '6px 14px',
                '&.Mui-focused': {
                    padding: '5px 13px'
                }
            },
            '& textarea.MuiInputBase-inputSizeSmall:placeholder-shown': {
                overflowX: 'hidden'
            }
        }
    }));
_c = TextFieldStyled;
const CustomTextField = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])(_c1 = (props, ref)=>{
    const { size = 'small', slotProps, ...rest } = props;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(TextFieldStyled, {
        size: size,
        inputRef: ref,
        ...rest,
        variant: "filled",
        slotProps: {
            ...slotProps,
            inputLabel: {
                ...slotProps?.inputLabel,
                shrink: true
            }
        }
    }, void 0, false, {
        fileName: "[project]/src/@core/components/mui/TextField.jsx",
        lineNumber: 252,
        columnNumber: 5
    }, this);
});
_c2 = CustomTextField;
const __TURBOPACK__default__export__ = CustomTextField;
var _c, _c1, _c2;
__turbopack_context__.k.register(_c, "TextFieldStyled");
__turbopack_context__.k.register(_c1, "CustomTextField$forwardRef");
__turbopack_context__.k.register(_c2, "CustomTextField");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/views/apps/user/list/TableFilters.jsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// React Imports
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
// MUI Imports
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$CardContent$2f$CardContent$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/CardContent/CardContent.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Grid2$2f$Grid2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/Grid2/Grid2.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$MenuItem$2f$MenuItem$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/MenuItem/MenuItem.js [app-client] (ecmascript)");
// Component Imports
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f40$core$2f$components$2f$mui$2f$TextField$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/@core/components/mui/TextField.jsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
;
;
;
;
;
const TableFilters = ({ setData, tableData })=>{
    _s();
    // States
    const [inquiryType, setInquiryType] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('');
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "TableFilters.useEffect": ()=>{
            const filteredData = tableData?.filter({
                "TableFilters.useEffect": (user)=>{
                    if (inquiryType && (user.inquiryType || user.type || 'General Inquiry') !== inquiryType) return false;
                    return true;
                }
            }["TableFilters.useEffect"]);
            setData(filteredData || []);
        }
    }["TableFilters.useEffect"], [
        inquiryType,
        tableData,
        setData
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$CardContent$2f$CardContent$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Grid2$2f$Grid2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
            container: true,
            spacing: 6,
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Grid2$2f$Grid2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                size: {
                    xs: 12,
                    sm: 6
                },
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f40$core$2f$components$2f$mui$2f$TextField$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    select: true,
                    fullWidth: true,
                    id: "select-inquiry-type",
                    value: inquiryType,
                    onChange: (e)=>setInquiryType(e.target.value),
                    slotProps: {
                        select: {
                            displayEmpty: true
                        }
                    },
                    label: "Inquiry Type",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$MenuItem$2f$MenuItem$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                            value: "",
                            children: "All Inquiry Types"
                        }, void 0, false, {
                            fileName: "[project]/src/views/apps/user/list/TableFilters.jsx",
                            lineNumber: 41,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$MenuItem$2f$MenuItem$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                            value: "General Inquiry",
                            children: "General Inquiry"
                        }, void 0, false, {
                            fileName: "[project]/src/views/apps/user/list/TableFilters.jsx",
                            lineNumber: 42,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$MenuItem$2f$MenuItem$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                            value: "Support Request",
                            children: "Support Request"
                        }, void 0, false, {
                            fileName: "[project]/src/views/apps/user/list/TableFilters.jsx",
                            lineNumber: 43,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$MenuItem$2f$MenuItem$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                            value: "Sales Inquiry",
                            children: "Sales Inquiry"
                        }, void 0, false, {
                            fileName: "[project]/src/views/apps/user/list/TableFilters.jsx",
                            lineNumber: 44,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$MenuItem$2f$MenuItem$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                            value: "Technical Support",
                            children: "Technical Support"
                        }, void 0, false, {
                            fileName: "[project]/src/views/apps/user/list/TableFilters.jsx",
                            lineNumber: 45,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$MenuItem$2f$MenuItem$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                            value: "Billing Question",
                            children: "Billing Question"
                        }, void 0, false, {
                            fileName: "[project]/src/views/apps/user/list/TableFilters.jsx",
                            lineNumber: 46,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$MenuItem$2f$MenuItem$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                            value: "Partnership",
                            children: "Partnership"
                        }, void 0, false, {
                            fileName: "[project]/src/views/apps/user/list/TableFilters.jsx",
                            lineNumber: 47,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$MenuItem$2f$MenuItem$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                            value: "Feedback",
                            children: "Feedback"
                        }, void 0, false, {
                            fileName: "[project]/src/views/apps/user/list/TableFilters.jsx",
                            lineNumber: 48,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$MenuItem$2f$MenuItem$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                            value: "Complaint",
                            children: "Complaint"
                        }, void 0, false, {
                            fileName: "[project]/src/views/apps/user/list/TableFilters.jsx",
                            lineNumber: 49,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/views/apps/user/list/TableFilters.jsx",
                    lineNumber: 30,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/views/apps/user/list/TableFilters.jsx",
                lineNumber: 29,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/views/apps/user/list/TableFilters.jsx",
            lineNumber: 28,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/views/apps/user/list/TableFilters.jsx",
        lineNumber: 27,
        columnNumber: 5
    }, this);
};
_s(TableFilters, "/+nX7jN7+hNk5RFnQwD7Ihda7J0=");
_c = TableFilters;
const __TURBOPACK__default__export__ = TableFilters;
var _c;
__turbopack_context__.k.register(_c, "TableFilters");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/views/apps/user/list/UserDetailsModal.jsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
// React Imports
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
// MUI Imports
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Dialog$2f$Dialog$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/Dialog/Dialog.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$DialogTitle$2f$DialogTitle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/DialogTitle/DialogTitle.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$DialogContent$2f$DialogContent$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/DialogContent/DialogContent.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Button$2f$Button$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/Button/Button.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Typography$2f$Typography$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/Typography/Typography.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Grid2$2f$Grid2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/Grid2/Grid2.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Card$2f$Card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/Card/Card.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$CardContent$2f$CardContent$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/CardContent/CardContent.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Avatar$2f$Avatar$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/Avatar/Avatar.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Divider$2f$Divider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/Divider/Divider.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$TextField$2f$TextField$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/TextField/TextField.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$IconButton$2f$IconButton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/IconButton/IconButton.js [app-client] (ecmascript)");
// PDF Export
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jspdf$2f$dist$2f$jspdf$2e$es$2e$min$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/jspdf/dist/jspdf.es.min.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jspdf$2d$autotable$2f$dist$2f$jspdf$2e$plugin$2e$autotable$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/jspdf-autotable/dist/jspdf.plugin.autotable.mjs [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const UserDetailsModal = ({ open, onClose, userData })=>{
    _s();
    const [userNotes, setUserNotes] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({});
    // Get the current user's note
    const currentNote = userData?.id ? userNotes[userData.id] || '' : '';
    // Handle note change for current user
    const handleNoteChange = (value)=>{
        if (userData?.id) {
            setUserNotes((prev)=>({
                    ...prev,
                    [userData.id]: value
                }));
        }
    };
    // Handle save note
    const handleSaveNote = ()=>{
        if (userData?.id && currentNote.trim()) {
            // Here you could also send the note to a backend API
            console.log(`Note saved for user ${userData.id}:`, currentNote);
            // You could show a success message here
            alert('Note saved successfully!');
        }
    };
    const handleDownloadPDF = ()=>{
        try {
            const doc = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jspdf$2f$dist$2f$jspdf$2e$es$2e$min$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
            // Add header
            doc.setFontSize(24);
            doc.setTextColor(40, 40, 40);
            doc.text('CAM Transport - Contact Details', 20, 25);
            // Add date
            const today = new Date();
            const formattedDate = today.toLocaleDateString();
            doc.setFontSize(12);
            doc.setTextColor(100, 100, 100);
            doc.text(`Generated on: ${formattedDate}`, 20, 35);
            // Add user avatar placeholder
            doc.setFillColor(41, 128, 185);
            doc.circle(30, 55, 8, 'F');
            doc.setTextColor(255, 255, 255);
            doc.setFontSize(16);
            doc.text(userData?.fullName?.charAt(0)?.toUpperCase() || 'U', 27, 59);
            // Add user name
            doc.setFontSize(20);
            doc.setTextColor(40, 40, 40);
            doc.text(userData?.fullName || 'Unknown User', 50, 55);
            // Add email
            doc.setFontSize(14);
            doc.setTextColor(100, 100, 100);
            doc.text(userData?.email || userData?.username || 'No email provided', 50, 65);
            // Add separator line
            doc.setDrawColor(200, 200, 200);
            doc.line(20, 75, 190, 75);
            // Contact Details Section
            doc.setFontSize(16);
            doc.setTextColor(40, 40, 40);
            doc.text('Contact Information', 20, 90);
            let yPosition = 105;
            const lineHeight = 15;
            // Contact details
            const details = [
                {
                    label: 'Contact Number:',
                    value: userData?.phone || userData?.contact || '+****************'
                },
                {
                    label: 'Company:',
                    value: userData?.company || userData?.currentPlan || 'CAM Transport'
                },
                {
                    label: 'Inquiry Type:',
                    value: userData?.inquiryType || userData?.type || 'General Inquiry'
                },
                {
                    label: 'Status:',
                    value: userData?.status || 'Pending'
                }
            ];
            details.forEach((detail)=>{
                doc.setFontSize(12);
                doc.setTextColor(100, 100, 100);
                doc.text(detail.label, 20, yPosition);
                doc.setTextColor(40, 40, 40);
                doc.text(detail.value, 70, yPosition);
                yPosition += lineHeight;
            });
            // User Message Section
            if (userData?.userMessage) {
                yPosition += 10;
                doc.setFontSize(16);
                doc.setTextColor(40, 40, 40);
                doc.text('User Message', 20, yPosition);
                yPosition += 15;
                doc.setFontSize(12);
                doc.setTextColor(40, 40, 40);
                // Split long message into multiple lines
                const message = userData.userMessage || 'Hello, I would like to inquire about your services. Please contact me at your earliest convenience.';
                const splitMessage = doc.splitTextToSize(message, 150);
                doc.text(splitMessage, 20, yPosition);
                yPosition += splitMessage.length * 6;
            }
            // Add notes if available
            if (currentNote && currentNote.trim()) {
                yPosition += 15;
                doc.setFontSize(16);
                doc.setTextColor(40, 40, 40);
                doc.text('Admin Notes', 20, yPosition);
                yPosition += 15;
                doc.setFontSize(12);
                doc.setTextColor(40, 40, 40);
                const splitNotes = doc.splitTextToSize(currentNote, 150);
                doc.text(splitNotes, 20, yPosition);
            }
            // Add footer
            doc.setFontSize(10);
            doc.setTextColor(150, 150, 150);
            doc.text('CAM Transport - Contact Management System', 20, 280);
            // Save the PDF
            const fileName = `${userData?.fullName?.replace(/\s+/g, '_') || 'contact'}_details_${new Date().toISOString().split('T')[0]}.pdf`;
            doc.save(fileName);
        } catch (error) {
            console.error('PDF Generation Error:', error);
            alert('Error generating PDF. Please try again.');
        }
    };
    if (!userData) return null;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Dialog$2f$Dialog$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        open: open,
        onClose: onClose,
        maxWidth: "md",
        fullWidth: true,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$DialogTitle$2f$DialogTitle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                className: "flex items-center justify-between",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Typography$2f$Typography$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                        variant: "h5",
                        children: "User Details"
                    }, void 0, false, {
                        fileName: "[project]/src/views/apps/user/list/UserDetailsModal.jsx",
                        lineNumber: 164,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center gap-2",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Button$2f$Button$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                variant: "outlined",
                                color: "primary",
                                size: "large",
                                startIcon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                    className: "tabler-file-type-pdf"
                                }, void 0, false, {
                                    fileName: "[project]/src/views/apps/user/list/UserDetailsModal.jsx",
                                    lineNumber: 170,
                                    columnNumber: 24
                                }, void 0),
                                onClick: handleDownloadPDF,
                                className: "font-bold",
                                sx: {
                                    fontSize: '1rem',
                                    padding: '12px 24px'
                                },
                                children: "Download PDF"
                            }, void 0, false, {
                                fileName: "[project]/src/views/apps/user/list/UserDetailsModal.jsx",
                                lineNumber: 166,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$IconButton$2f$IconButton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                onClick: onClose,
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                    className: "tabler-x"
                                }, void 0, false, {
                                    fileName: "[project]/src/views/apps/user/list/UserDetailsModal.jsx",
                                    lineNumber: 178,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/views/apps/user/list/UserDetailsModal.jsx",
                                lineNumber: 177,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/views/apps/user/list/UserDetailsModal.jsx",
                        lineNumber: 165,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/views/apps/user/list/UserDetailsModal.jsx",
                lineNumber: 163,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$DialogContent$2f$DialogContent$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Grid2$2f$Grid2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    container: true,
                    spacing: 6,
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Grid2$2f$Grid2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                            size: {
                                xs: 12
                            },
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Card$2f$Card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$CardContent$2f$CardContent$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex items-center gap-4 mb-6",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Avatar$2f$Avatar$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                    sx: {
                                                        width: 100,
                                                        height: 100,
                                                        fontSize: '3rem',
                                                        fontWeight: 'bold'
                                                    },
                                                    children: userData.fullName?.charAt(0)?.toUpperCase()
                                                }, void 0, false, {
                                                    fileName: "[project]/src/views/apps/user/list/UserDetailsModal.jsx",
                                                    lineNumber: 190,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Typography$2f$Typography$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                            variant: "h3",
                                                            className: "mb-3 font-bold",
                                                            style: {
                                                                fontSize: '2rem'
                                                            },
                                                            children: userData.fullName
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/views/apps/user/list/UserDetailsModal.jsx",
                                                            lineNumber: 196,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Typography$2f$Typography$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                            variant: "h5",
                                                            color: "text.secondary",
                                                            className: "font-medium",
                                                            style: {
                                                                fontSize: '1.2rem'
                                                            },
                                                            children: userData.email || userData.username
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/views/apps/user/list/UserDetailsModal.jsx",
                                                            lineNumber: 199,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/views/apps/user/list/UserDetailsModal.jsx",
                                                    lineNumber: 195,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/views/apps/user/list/UserDetailsModal.jsx",
                                            lineNumber: 189,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Divider$2f$Divider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            className: "mb-6"
                                        }, void 0, false, {
                                            fileName: "[project]/src/views/apps/user/list/UserDetailsModal.jsx",
                                            lineNumber: 205,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Grid2$2f$Grid2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            container: true,
                                            spacing: 4,
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Grid2$2f$Grid2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                    size: {
                                                        xs: 12,
                                                        sm: 6
                                                    },
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Typography$2f$Typography$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                            variant: "h6",
                                                            color: "text.secondary",
                                                            className: "mb-3 font-medium",
                                                            style: {
                                                                fontSize: '1.2rem'
                                                            },
                                                            children: "Contact"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/views/apps/user/list/UserDetailsModal.jsx",
                                                            lineNumber: 210,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Typography$2f$Typography$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                            variant: "h5",
                                                            className: "font-bold",
                                                            style: {
                                                                fontSize: '1.2rem'
                                                            },
                                                            children: userData.phone || userData.contact || '+****************'
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/views/apps/user/list/UserDetailsModal.jsx",
                                                            lineNumber: 213,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/views/apps/user/list/UserDetailsModal.jsx",
                                                    lineNumber: 209,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Grid2$2f$Grid2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                    size: {
                                                        xs: 12,
                                                        sm: 6
                                                    },
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Typography$2f$Typography$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                            variant: "h6",
                                                            color: "text.secondary",
                                                            className: "mb-3 font-medium",
                                                            style: {
                                                                fontSize: '1.2rem'
                                                            },
                                                            children: "Company"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/views/apps/user/list/UserDetailsModal.jsx",
                                                            lineNumber: 219,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Typography$2f$Typography$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                            variant: "h5",
                                                            className: "font-bold",
                                                            style: {
                                                                fontSize: '1.2rem'
                                                            },
                                                            children: userData.company || userData.currentPlan
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/views/apps/user/list/UserDetailsModal.jsx",
                                                            lineNumber: 222,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/views/apps/user/list/UserDetailsModal.jsx",
                                                    lineNumber: 218,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Grid2$2f$Grid2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                    size: {
                                                        xs: 12,
                                                        sm: 6
                                                    },
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Typography$2f$Typography$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                            variant: "h6",
                                                            color: "text.secondary",
                                                            className: "mb-3 font-medium",
                                                            style: {
                                                                fontSize: '1.2rem'
                                                            },
                                                            children: "Type"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/views/apps/user/list/UserDetailsModal.jsx",
                                                            lineNumber: 228,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Typography$2f$Typography$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                            variant: "h5",
                                                            className: "font-bold",
                                                            style: {
                                                                fontSize: '1.2rem'
                                                            },
                                                            children: userData.inquiryType || userData.type || 'General Inquiry'
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/views/apps/user/list/UserDetailsModal.jsx",
                                                            lineNumber: 231,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/views/apps/user/list/UserDetailsModal.jsx",
                                                    lineNumber: 227,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Grid2$2f$Grid2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                    size: {
                                                        xs: 12,
                                                        sm: 6
                                                    },
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Typography$2f$Typography$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                            variant: "h6",
                                                            color: "text.secondary",
                                                            className: "mb-3 font-medium",
                                                            style: {
                                                                fontSize: '1.2rem'
                                                            },
                                                            children: "Inquiry Date & Time"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/views/apps/user/list/UserDetailsModal.jsx",
                                                            lineNumber: 237,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Typography$2f$Typography$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                            variant: "h5",
                                                            className: "font-bold",
                                                            style: {
                                                                fontSize: '1.2rem'
                                                            },
                                                            children: userData?.inquiryDate || userData?.createdAt ? new Date(userData.inquiryDate || userData.createdAt).toLocaleString('en-US', {
                                                                year: 'numeric',
                                                                month: 'long',
                                                                day: 'numeric',
                                                                hour: '2-digit',
                                                                minute: '2-digit',
                                                                hour12: true
                                                            }) : 'Not available'
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/views/apps/user/list/UserDetailsModal.jsx",
                                                            lineNumber: 240,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/views/apps/user/list/UserDetailsModal.jsx",
                                                    lineNumber: 236,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Grid2$2f$Grid2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                    size: {
                                                        xs: 12,
                                                        sm: 6
                                                    },
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Typography$2f$Typography$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                            variant: "h6",
                                                            color: "text.secondary",
                                                            className: "mb-3 font-medium",
                                                            style: {
                                                                fontSize: '1.2rem'
                                                            },
                                                            children: "User Message"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/views/apps/user/list/UserDetailsModal.jsx",
                                                            lineNumber: 256,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Typography$2f$Typography$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                            variant: "h5",
                                                            className: "font-bold",
                                                            style: {
                                                                fontSize: '1.2rem',
                                                                lineHeight: '1.2'
                                                            },
                                                            children: userData.userMessage || 'Hello, I would like to inquire about your services. Please contact me at your earliest convenience.'
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/views/apps/user/list/UserDetailsModal.jsx",
                                                            lineNumber: 259,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/views/apps/user/list/UserDetailsModal.jsx",
                                                    lineNumber: 255,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/views/apps/user/list/UserDetailsModal.jsx",
                                            lineNumber: 208,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/views/apps/user/list/UserDetailsModal.jsx",
                                    lineNumber: 188,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/views/apps/user/list/UserDetailsModal.jsx",
                                lineNumber: 187,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/views/apps/user/list/UserDetailsModal.jsx",
                            lineNumber: 186,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Grid2$2f$Grid2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                            size: {
                                xs: 12
                            },
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Card$2f$Card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$CardContent$2f$CardContent$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Typography$2f$Typography$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            variant: "h4",
                                            className: "mb-4 font-bold",
                                            style: {
                                                fontSize: '1.2rem'
                                            },
                                            children: "Add a Note"
                                        }, void 0, false, {
                                            fileName: "[project]/src/views/apps/user/list/UserDetailsModal.jsx",
                                            lineNumber: 272,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$TextField$2f$TextField$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            fullWidth: true,
                                            multiline: true,
                                            rows: 4,
                                            placeholder: "Add your note here...",
                                            value: currentNote,
                                            onChange: (e)=>handleNoteChange(e.target.value),
                                            variant: "outlined",
                                            sx: {
                                                '& .MuiInputBase-input': {
                                                    fontSize: '1.25rem',
                                                    lineHeight: '1.5'
                                                },
                                                '& .MuiInputLabel-root': {
                                                    fontSize: '1.25rem'
                                                }
                                            }
                                        }, void 0, false, {
                                            fileName: "[project]/src/views/apps/user/list/UserDetailsModal.jsx",
                                            lineNumber: 275,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex justify-end mt-4",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Button$2f$Button$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                variant: "contained",
                                                color: "primary",
                                                size: "large",
                                                className: "font-bold",
                                                sx: {
                                                    fontSize: '1rem',
                                                    padding: '12px 24px'
                                                },
                                                onClick: handleSaveNote,
                                                children: "Save"
                                            }, void 0, false, {
                                                fileName: "[project]/src/views/apps/user/list/UserDetailsModal.jsx",
                                                lineNumber: 294,
                                                columnNumber: 19
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/views/apps/user/list/UserDetailsModal.jsx",
                                            lineNumber: 293,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/views/apps/user/list/UserDetailsModal.jsx",
                                    lineNumber: 271,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/views/apps/user/list/UserDetailsModal.jsx",
                                lineNumber: 270,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/views/apps/user/list/UserDetailsModal.jsx",
                            lineNumber: 269,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/views/apps/user/list/UserDetailsModal.jsx",
                    lineNumber: 184,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/views/apps/user/list/UserDetailsModal.jsx",
                lineNumber: 183,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/views/apps/user/list/UserDetailsModal.jsx",
        lineNumber: 162,
        columnNumber: 5
    }, this);
};
_s(UserDetailsModal, "uDVPoPHhA5m+NkLQDApjBaUsOcA=");
_c = UserDetailsModal;
const __TURBOPACK__default__export__ = UserDetailsModal;
var _c;
__turbopack_context__.k.register(_c, "UserDetailsModal");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/TablePaginationComponent.jsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// MUI Imports
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Pagination$2f$Pagination$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/Pagination/Pagination.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Typography$2f$Typography$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/Typography/Typography.js [app-client] (ecmascript)");
;
;
;
const TablePaginationComponent = ({ table })=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "flex justify-between items-center flex-wrap pli-6 border-bs bs-auto plb-[12.5px] gap-2",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Typography$2f$Typography$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                color: "text.disabled",
                children: `Showing ${table.getFilteredRowModel().rows.length === 0 ? 0 : table.getState().pagination.pageIndex * table.getState().pagination.pageSize + 1}
        to ${Math.min((table.getState().pagination.pageIndex + 1) * table.getState().pagination.pageSize, table.getFilteredRowModel().rows.length)} of ${table.getFilteredRowModel().rows.length} entries`
            }, void 0, false, {
                fileName: "[project]/src/components/TablePaginationComponent.jsx",
                lineNumber: 8,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Pagination$2f$Pagination$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                shape: "rounded",
                color: "primary",
                variant: "tonal",
                count: Math.ceil(table.getFilteredRowModel().rows.length / table.getState().pagination.pageSize),
                page: table.getState().pagination.pageIndex + 1,
                onChange: (_, page)=>{
                    table.setPageIndex(page - 1);
                },
                showFirstButton: true,
                showLastButton: true
            }, void 0, false, {
                fileName: "[project]/src/components/TablePaginationComponent.jsx",
                lineNumber: 16,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/TablePaginationComponent.jsx",
        lineNumber: 7,
        columnNumber: 5
    }, this);
};
_c = TablePaginationComponent;
const __TURBOPACK__default__export__ = TablePaginationComponent;
var _c;
__turbopack_context__.k.register(_c, "TablePaginationComponent");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/@core/components/mui/Avatar.jsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
// React Imports
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
// MUI Imports
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Avatar$2f$Avatar$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/Avatar/Avatar.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$system$2f$esm$2f$colorManipulator$2f$colorManipulator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/system/esm/colorManipulator/colorManipulator.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$styles$2f$styled$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__$3c$export__default__as__styled$3e$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/styles/styled.js [app-client] (ecmascript) <locals> <export default as styled>");
'use client';
;
;
;
;
const Avatar = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$styles$2f$styled$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__$3c$export__default__as__styled$3e$__["styled"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Avatar$2f$Avatar$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(({ skin, color, size, theme })=>{
    return {
        ...color && skin === 'light' && {
            backgroundColor: `var(--mui-palette-${color}-lightOpacity)`,
            color: `var(--mui-palette-${color}-main)`
        },
        ...color && skin === 'light-static' && {
            backgroundColor: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$system$2f$esm$2f$colorManipulator$2f$colorManipulator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["lighten"])(theme.palette[color].main, 0.84),
            color: `var(--mui-palette-${color}-main)`
        },
        ...color && skin === 'filled' && {
            backgroundColor: `var(--mui-palette-${color}-main)`,
            color: `var(--mui-palette-${color}-contrastText)`
        },
        ...size && {
            height: size,
            width: size
        }
    };
});
_c = Avatar;
const CustomAvatar = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])(_c1 = (props, ref)=>{
    // Props
    const { color, skin = 'filled', ...rest } = props;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Avatar, {
        color: color,
        skin: skin,
        ref: ref,
        ...rest
    }, void 0, false, {
        fileName: "[project]/src/@core/components/mui/Avatar.jsx",
        lineNumber: 38,
        columnNumber: 10
    }, this);
});
_c2 = CustomAvatar;
const __TURBOPACK__default__export__ = CustomAvatar;
var _c, _c1, _c2;
__turbopack_context__.k.register(_c, "Avatar");
__turbopack_context__.k.register(_c1, "CustomAvatar$forwardRef");
__turbopack_context__.k.register(_c2, "CustomAvatar");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/services/contactApi.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// API service for contact management
__turbopack_context__.s({
    "deleteContact": (()=>deleteContact),
    "fetchContacts": (()=>fetchContacts),
    "getContactById": (()=>getContactById),
    "updateContactStatus": (()=>updateContactStatus)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
const API_BASE_URL = ("TURBOPACK compile-time value", "http://localhost:8090") || 'http://localhost:8090';
const fetchContacts = async ()=>{
    try {
        const response = await fetch(`${API_BASE_URL}/contact/get-contacts`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        const contacts = await response.json();
        // Transform backend data to match frontend structure
        return contacts.map((contact)=>({
                id: contact._id,
                fullName: contact.name,
                username: contact.name,
                email: contact.email,
                phone: contact.contact,
                contact: contact.contact,
                company: contact.company || 'CAM Transport',
                currentPlan: contact.company || 'CAM Transport',
                inquiryType: contact.inquiryType,
                type: contact.inquiryType,
                billing: contact.inquiryType,
                userMessage: contact.message,
                message: contact.message,
                otherInquiry: contact.otherInquiry,
                ip: contact.ip,
                status: contact.status || 'pending',
                createdAt: contact.createdAt,
                updatedAt: contact.updatedAt,
                inquiryDate: contact.createdAt,
                // Add avatar placeholder
                avatar: null
            }));
    } catch (error) {
        console.error('Error fetching contacts:', error);
        throw error;
    }
};
const updateContactStatus = async (contactId, status)=>{
    try {
        console.log('API: Updating contact status', contactId, 'to', status);
        console.log('API URL:', `${API_BASE_URL}/contact/update-status/${contactId}`);
        const response = await fetch(`${API_BASE_URL}/contact/update-status/${contactId}`, {
            method: 'PATCH',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                status
            })
        });
        console.log('API Response status:', response.status);
        console.log('API Response ok:', response.ok);
        if (!response.ok) {
            const errorText = await response.text();
            console.error('API Error response:', errorText);
            throw new Error(`HTTP error! status: ${response.status} - ${errorText}`);
        }
        const result = await response.json();
        console.log('API Success result:', result);
        return result;
    } catch (error) {
        console.error('Error updating contact status:', error);
        throw error;
    }
};
const deleteContact = async (contactId)=>{
    try {
        const response = await fetch(`${API_BASE_URL}/contact/delete-contact/${contactId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        const result = await response.json();
        return result;
    } catch (error) {
        console.error('Error deleting contact:', error);
        throw error;
    }
};
const getContactById = async (contactId)=>{
    try {
        const contacts = await fetchContacts();
        return contacts.find((contact)=>contact.id === contactId);
    } catch (error) {
        console.error('Error fetching contact by ID:', error);
        throw error;
    }
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/@core/styles/table.module.css [app-client] (css module)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "cellWithInput": "table-module__Mig-TG__cellWithInput",
  "table": "table-module__Mig-TG__table",
});
}}),
"[project]/src/views/apps/user/list/UserListTable.jsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
// React Imports
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
// Next Imports
// import Link from 'next/link'
// import { useParams } from 'next/navigation'
// MUI Imports
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Card$2f$Card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/Card/Card.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$CardHeader$2f$CardHeader$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/CardHeader/CardHeader.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$CardContent$2f$CardContent$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/CardContent/CardContent.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Button$2f$Button$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/Button/Button.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Typography$2f$Typography$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/Typography/Typography.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Chip$2f$Chip$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/Chip/Chip.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Checkbox$2f$Checkbox$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/Checkbox/Checkbox.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$IconButton$2f$IconButton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/IconButton/IconButton.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/Box/Box.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$useMediaQuery$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/useMediaQuery/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$styles$2f$useTheme$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useTheme$3e$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/styles/useTheme.js [app-client] (ecmascript) <export default as useTheme>");
// import { styled } from '@mui/material/styles'
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$TablePagination$2f$TablePagination$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/TablePagination/TablePagination.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$MenuItem$2f$MenuItem$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/MenuItem/MenuItem.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$CircularProgress$2f$CircularProgress$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/CircularProgress/CircularProgress.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Alert$2f$Alert$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/Alert/Alert.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Select$2f$Select$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/Select/Select.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$FormControl$2f$FormControl$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/FormControl/FormControl.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$ListItemIcon$2f$ListItemIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/ListItemIcon/ListItemIcon.js [app-client] (ecmascript)");
// Third-party Imports
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/classnames/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$match$2d$sorter$2d$utils$2f$build$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/match-sorter-utils/build/lib/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$table$2d$core$2f$build$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/table-core/build/lib/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$table$2f$build$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-table/build/lib/index.mjs [app-client] (ecmascript) <locals>");
// Component Imports
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$views$2f$apps$2f$user$2f$list$2f$TableFilters$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/views/apps/user/list/TableFilters.jsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$views$2f$apps$2f$user$2f$list$2f$UserDetailsModal$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/views/apps/user/list/UserDetailsModal.jsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$TablePaginationComponent$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/TablePaginationComponent.jsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f40$core$2f$components$2f$mui$2f$TextField$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/@core/components/mui/TextField.jsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f40$core$2f$components$2f$mui$2f$Avatar$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/@core/components/mui/Avatar.jsx [app-client] (ecmascript)");
// PDF Export
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jspdf$2f$dist$2f$jspdf$2e$es$2e$min$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/jspdf/dist/jspdf.es.min.js [app-client] (ecmascript)");
// Import autotable plugin
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jspdf$2d$autotable$2f$dist$2f$jspdf$2e$plugin$2e$autotable$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/jspdf-autotable/dist/jspdf.plugin.autotable.mjs [app-client] (ecmascript)");
// API Imports
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$contactApi$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/contactApi.js [app-client] (ecmascript)");
// Util Imports
// import { getLocalizedUrl } from '@/utils/i18n'
// Style Imports
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f40$core$2f$styles$2f$table$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/src/@core/styles/table.module.css [app-client] (css module)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature(), _s2 = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
// Custom styles for responsive icons
const actionButtonStyles = {
    minWidth: '32px !important',
    width: '32px !important',
    height: '32px !important',
    padding: '4px !important',
    flexShrink: 0,
    '& i': {
        fontSize: '18px !important',
        minWidth: '18px',
        minHeight: '18px'
    },
    '&:hover': {
        backgroundColor: 'action.hover'
    }
};
// Styled Components
// const Icon = styled('i')({})
const fuzzyFilter = (row, columnId, value, addMeta)=>{
    // Rank the item
    const itemRank = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$match$2d$sorter$2d$utils$2f$build$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rankItem"])(row.getValue(columnId), value);
    // Store the itemRank info
    addMeta({
        itemRank
    });
    // Return if the item should be filtered in/out
    return itemRank.passed;
};
const DebouncedInput = ({ value: initialValue, onChange, debounce = 500, ...props })=>{
    _s();
    // States
    const [value, setValue] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(initialValue);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "DebouncedInput.useEffect": ()=>{
            setValue(initialValue);
        }
    }["DebouncedInput.useEffect"], [
        initialValue
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "DebouncedInput.useEffect": ()=>{
            const timeout = setTimeout({
                "DebouncedInput.useEffect.timeout": ()=>{
                    onChange(value);
                }
            }["DebouncedInput.useEffect.timeout"], debounce);
            return ({
                "DebouncedInput.useEffect": ()=>clearTimeout(timeout)
            })["DebouncedInput.useEffect"];
        // eslint-disable-next-line react-hooks/exhaustive-deps
        }
    }["DebouncedInput.useEffect"], [
        value
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f40$core$2f$components$2f$mui$2f$TextField$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        value: value,
        onChange: (e)=>setValue(e.target.value)
    }, void 0, false, {
        fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
        lineNumber: 118,
        columnNumber: 10
    }, this);
};
_s(DebouncedInput, "Wogv9twUGnfds4rVOUUjop35/IU=");
_c = DebouncedInput;
// Vars
const statusConfig = {
    pending: {
        label: 'Pending',
        color: 'warning',
        icon: 'tabler-clock',
        description: 'Waiting for review'
    },
    'in-view': {
        label: 'In View',
        color: 'info',
        icon: 'tabler-eye',
        description: 'Being reviewed'
    },
    completed: {
        label: 'Completed',
        color: 'success',
        icon: 'tabler-check',
        description: 'Review completed'
    }
};
// Status options for dropdown
const statusOptions = [
    {
        value: 'pending',
        label: 'Pending',
        color: 'warning',
        icon: 'tabler-clock'
    },
    {
        value: 'in-view',
        label: 'In View',
        color: 'info',
        icon: 'tabler-eye'
    },
    {
        value: 'completed',
        label: 'Completed',
        color: 'success',
        icon: 'tabler-check'
    }
];
// Status Dropdown Component
const StatusDropdown = ({ currentStatus, onStatusChange, contactId })=>{
    _s1();
    const [isOpen, setIsOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const config = statusConfig[currentStatus] || statusConfig.pending;
    const handleStatusSelect = (newStatus)=>{
        console.log('StatusDropdown: Selecting status', newStatus, 'for contact', contactId);
        onStatusChange(contactId, newStatus);
        setIsOpen(false);
    };
    if (!isOpen) {
        // Show as button/chip when closed
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Chip$2f$Chip$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
            icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                className: `${config.icon} text-xs sm:text-sm`
            }, void 0, false, {
                fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                lineNumber: 165,
                columnNumber: 15
            }, void 0),
            label: config.label,
            color: config.color,
            variant: "filled",
            size: "small",
            className: "text-xs sm:text-sm cursor-pointer",
            onClick: ()=>setIsOpen(true),
            title: "Click to change status",
            sx: {
                height: {
                    xs: '28px',
                    sm: '32px'
                },
                width: {
                    xs: '100px',
                    sm: '110px'
                },
                minWidth: {
                    xs: '100px',
                    sm: '110px'
                },
                maxWidth: {
                    xs: '100px',
                    sm: '110px'
                },
                fontSize: {
                    xs: '0.75rem',
                    sm: '0.8rem'
                },
                cursor: 'pointer',
                transition: 'all 0.2s ease-in-out',
                '& .MuiChip-label': {
                    padding: {
                        xs: '0 6px',
                        sm: '0 8px'
                    },
                    fontSize: {
                        xs: '0.7rem',
                        sm: '0.75rem'
                    },
                    fontWeight: 500,
                    whiteSpace: 'nowrap',
                    overflow: 'visible',
                    textOverflow: 'unset'
                },
                '& .MuiChip-icon': {
                    fontSize: {
                        xs: '14px',
                        sm: '16px'
                    },
                    marginLeft: {
                        xs: '6px',
                        sm: '8px'
                    },
                    marginRight: {
                        xs: '0px',
                        sm: '2px'
                    }
                },
                '&:hover': {
                    transform: 'scale(1.02)',
                    boxShadow: 2
                }
            }
        }, void 0, false, {
            fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
            lineNumber: 164,
            columnNumber: 7
        }, this);
    }
    // Show as dropdown when open
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$FormControl$2f$FormControl$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        size: "small",
        sx: {
            minWidth: 120
        },
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Select$2f$Select$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
            value: currentStatus,
            onChange: (e)=>handleStatusSelect(e.target.value),
            onClose: ()=>setIsOpen(false),
            open: isOpen,
            size: "small",
            autoFocus: true,
            sx: {
                height: '32px',
                fontSize: '0.75rem',
                '& .MuiSelect-select': {
                    padding: '4px 8px',
                    display: 'flex',
                    alignItems: 'center',
                    gap: 1
                }
            },
            children: statusOptions.map((option)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$MenuItem$2f$MenuItem$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    value: option.value,
                    onClick: ()=>handleStatusSelect(option.value),
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$ListItemIcon$2f$ListItemIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                            sx: {
                                minWidth: '20px !important'
                            },
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                className: `${option.icon} text-sm`
                            }, void 0, false, {
                                fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                lineNumber: 231,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                            lineNumber: 230,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Typography$2f$Typography$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                            variant: "body2",
                            sx: {
                                fontSize: '0.75rem'
                            },
                            children: option.label
                        }, void 0, false, {
                            fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                            lineNumber: 233,
                            columnNumber: 13
                        }, this)
                    ]
                }, option.value, true, {
                    fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                    lineNumber: 225,
                    columnNumber: 11
                }, this))
        }, void 0, false, {
            fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
            lineNumber: 206,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
        lineNumber: 205,
        columnNumber: 5
    }, this);
};
_s1(StatusDropdown, "+sus0Lb0ewKHdwiUhiTAJFoFyQ0=");
_c1 = StatusDropdown;
// Column Definitions
const columnHelper = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$table$2d$core$2f$build$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createColumnHelper"])();
// Mobile Card Component
const MobileContactCard = ({ contact, onViewDetails, onDelete, onStatusChange })=>{
    const status = contact.status || 'pending';
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Card$2f$Card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        className: "mb-4 shadow-sm",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$CardContent$2f$CardContent$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
            className: "p-4",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex items-start justify-between mb-3",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center gap-3 flex-1 min-w-0",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f40$core$2f$components$2f$mui$2f$Avatar$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                    size: 40,
                                    children: contact.fullName?.charAt(0)?.toUpperCase()
                                }, void 0, false, {
                                    fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                    lineNumber: 257,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex flex-col min-w-0 flex-1",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Typography$2f$Typography$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            variant: "h6",
                                            className: "font-semibold text-sm truncate",
                                            children: contact.fullName
                                        }, void 0, false, {
                                            fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                            lineNumber: 261,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Typography$2f$Typography$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            variant: "body2",
                                            color: "text.secondary",
                                            className: "text-xs truncate",
                                            children: contact.email || contact.username
                                        }, void 0, false, {
                                            fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                            lineNumber: 264,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                    lineNumber: 260,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                            lineNumber: 256,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center gap-1 ml-2",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$IconButton$2f$IconButton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                    onClick: ()=>onViewDetails(contact),
                                    title: "View Details",
                                    size: "small",
                                    sx: actionButtonStyles,
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                        className: "tabler-info-circle text-textSecondary"
                                    }, void 0, false, {
                                        fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                        lineNumber: 276,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                    lineNumber: 270,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$IconButton$2f$IconButton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                    onClick: ()=>onDelete(contact.id),
                                    title: "Delete",
                                    size: "small",
                                    sx: actionButtonStyles,
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                        className: "tabler-trash text-textSecondary"
                                    }, void 0, false, {
                                        fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                        lineNumber: 284,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                    lineNumber: 278,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                            lineNumber: 269,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                    lineNumber: 255,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "grid grid-cols-1 sm:grid-cols-2 gap-3 text-sm",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Typography$2f$Typography$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                    variant: "caption",
                                    color: "text.secondary",
                                    className: "font-medium",
                                    children: "Contact"
                                }, void 0, false, {
                                    fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                    lineNumber: 291,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Typography$2f$Typography$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                    variant: "body2",
                                    className: "truncate",
                                    children: contact.phone || contact.contact || '+****************'
                                }, void 0, false, {
                                    fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                    lineNumber: 294,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                            lineNumber: 290,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Typography$2f$Typography$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                    variant: "caption",
                                    color: "text.secondary",
                                    className: "font-medium",
                                    children: "Type"
                                }, void 0, false, {
                                    fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                    lineNumber: 299,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Typography$2f$Typography$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                    variant: "body2",
                                    className: "truncate",
                                    children: contact.inquiryType || contact.type || 'General Inquiry'
                                }, void 0, false, {
                                    fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                    lineNumber: 302,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                            lineNumber: 298,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "sm:col-span-2",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Typography$2f$Typography$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                    variant: "caption",
                                    color: "text.secondary",
                                    className: "font-medium",
                                    children: "Status"
                                }, void 0, false, {
                                    fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                    lineNumber: 307,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "mt-1",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(StatusDropdown, {
                                        currentStatus: status,
                                        onStatusChange: onStatusChange,
                                        contactId: contact.id
                                    }, void 0, false, {
                                        fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                        lineNumber: 311,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                    lineNumber: 310,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                            lineNumber: 306,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                    lineNumber: 289,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
            lineNumber: 254,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
        lineNumber: 253,
        columnNumber: 5
    }, this);
};
_c2 = MobileContactCard;
const UserListTable = ()=>{
    _s2();
    // States
    const [rowSelection, setRowSelection] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({});
    const [data, setData] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [filteredData, setFilteredData] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [globalFilter, setGlobalFilter] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('');
    const [modalOpen, setModalOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [selectedUser, setSelectedUser] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    // Hooks
    const theme = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$styles$2f$useTheme$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useTheme$3e$__["useTheme"])();
    const isMobile = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$useMediaQuery$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(theme.breakpoints.down('md'));
    // const { lang: locale } = useParams()
    // Load contacts from backend
    const loadContacts = async ()=>{
        try {
            console.log('🔄 Loading contacts from backend...');
            setLoading(true);
            setError(null);
            const contacts = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$contactApi$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fetchContacts"])();
            console.log('✅ Fetched contacts:', contacts.length, 'items');
            console.log('📋 Sample contact:', contacts[0]);
            // Ensure all contacts have a status field (default to 'pending' if missing)
            const contactsWithStatus = contacts.map((contact)=>({
                    ...contact,
                    status: contact.status || 'pending'
                }));
            setData(contactsWithStatus);
            setFilteredData(contactsWithStatus);
        } catch (err) {
            console.error('❌ Error loading contacts:', err);
            setError('Failed to load contacts. Please try again.');
        } finally{
            setLoading(false);
        }
    };
    // Load contacts from backend on component mount
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "UserListTable.useEffect": ()=>{
            // Clear old localStorage data that might interfere with backend status
            localStorage.removeItem('contactStatuses');
            localStorage.removeItem('jobApplicationStatuses');
            localStorage.removeItem('quoteStatuses');
            localStorage.removeItem('urgentInquiryStatuses');
            console.log('🧹 Cleared all old localStorage status data');
            loadContacts();
        }
    }["UserListTable.useEffect"], []);
    const handleViewDetails = (userData)=>{
        setSelectedUser(userData);
        setModalOpen(true);
    };
    const handleModalClose = ()=>{
        setModalOpen(false);
        setSelectedUser(null);
    };
    const handleStatusChange = async (userId, newStatus)=>{
        console.log('Updating status for user:', userId, 'to:', newStatus);
        try {
            // Call API to update status in backend
            await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$contactApi$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["updateContactStatus"])(userId, newStatus);
            console.log('Contact status updated in backend successfully');
            // Update local state only after successful backend update
            setData((prevData)=>prevData.map((item)=>item.id === userId ? {
                        ...item,
                        status: newStatus
                    } : item));
            setFilteredData((prevData)=>prevData.map((item)=>item.id === userId ? {
                        ...item,
                        status: newStatus
                    } : item));
            console.log('Contact status updated in frontend successfully');
        } catch (error) {
            console.error('Failed to update contact status:', error);
            alert('Failed to update status. Please try again.');
        }
    };
    // Optional: Clear all saved statuses (for debugging)
    const clearSavedStatuses = ()=>{
        localStorage.removeItem('contactStatuses');
        console.log('Cleared all saved contact statuses');
    };
    const handleDeleteContact = async (contactId)=>{
        try {
            // Find the contact to get their name for confirmation
            const contact = data.find((item)=>item.id === contactId);
            const contactName = contact?.fullName || 'this contact';
            // Show confirmation dialog
            const confirmed = window.confirm(`Are you sure you want to delete ${contactName}?\n\nThis action cannot be undone and will permanently remove the contact from the database.`);
            if (!confirmed) {
                return;
            }
            // Call backend API to delete contact
            await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$contactApi$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["deleteContact"])(contactId);
            // Remove contact from local state
            setData((prevData)=>prevData.filter((item)=>item.id !== contactId));
            setFilteredData((prevData)=>prevData.filter((item)=>item.id !== contactId));
            // Clear selection if deleted contact was selected
            setRowSelection((prevSelection)=>{
                const newSelection = {
                    ...prevSelection
                };
                delete newSelection[contactId];
                return newSelection;
            });
            // Show success message
            alert(`${contactName} has been deleted successfully!`);
        } catch (error) {
            console.error('Error deleting contact:', error);
            alert('Failed to delete contact. Please try again.');
        }
    };
    // Get selected rows data
    const getSelectedRowsData = ()=>{
        const selectedRows = table.getFilteredSelectedRowModel().rows;
        return selectedRows.map((row)=>row.original);
    };
    // PDF Export function
    const exportSelectedToPDF = ()=>{
        const selectedData = getSelectedRowsData();
        if (selectedData.length === 0) {
            alert('Please select at least one contact to export.');
            return;
        }
        try {
            const doc = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jspdf$2f$dist$2f$jspdf$2e$es$2e$min$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
            // Add title
            doc.setFontSize(20);
            doc.setTextColor(40, 40, 40);
            doc.text('CAM Transport - Contact Export', 20, 20);
            // Add export info
            doc.setFontSize(12);
            doc.setTextColor(100, 100, 100);
            doc.text(`Export Date: ${new Date().toLocaleDateString()}`, 20, 35);
            doc.text(`Selected Contacts: ${selectedData.length}`, 20, 45);
            // Prepare table data
            const tableData = selectedData.map((contact)=>[
                    contact.fullName || 'N/A',
                    contact.email || contact.username || 'N/A',
                    contact.phone || contact.contact || 'N/A',
                    contact.company || contact.currentPlan || 'CAM Transport',
                    contact.inquiryType || contact.type || 'General Inquiry',
                    statusConfig[contact.status || 'pending']?.label || 'Pending'
                ]);
            // Try different autoTable approaches
            if (typeof doc.autoTable === 'function') {
                // Method 1: Direct method
                doc.autoTable({
                    head: [
                        [
                            'Name',
                            'Email',
                            'Contact',
                            'Company',
                            'Inquiry Type',
                            'Status'
                        ]
                    ],
                    body: tableData,
                    startY: 60,
                    styles: {
                        fontSize: 10,
                        cellPadding: 3
                    },
                    headStyles: {
                        fillColor: [
                            41,
                            128,
                            185
                        ],
                        textColor: 255,
                        fontStyle: 'bold'
                    },
                    alternateRowStyles: {
                        fillColor: [
                            245,
                            245,
                            245
                        ]
                    }
                });
            } else if (typeof autoTable === 'function') {
                // Method 2: Imported function
                autoTable(doc, {
                    head: [
                        [
                            'Name',
                            'Email',
                            'Contact',
                            'Company',
                            'Inquiry Type',
                            'Status'
                        ]
                    ],
                    body: tableData,
                    startY: 60,
                    styles: {
                        fontSize: 10,
                        cellPadding: 3
                    },
                    headStyles: {
                        fillColor: [
                            41,
                            128,
                            185
                        ],
                        textColor: 255,
                        fontStyle: 'bold'
                    },
                    alternateRowStyles: {
                        fillColor: [
                            245,
                            245,
                            245
                        ]
                    }
                });
            } else {
                // Fallback: Manual table creation
                let yPosition = 70;
                const lineHeight = 8;
                // Add headers
                doc.setFontSize(10);
                doc.setFont(undefined, 'bold');
                doc.text('Name', 20, yPosition);
                doc.text('Email', 60, yPosition);
                doc.text('Contact', 110, yPosition);
                doc.text('Company', 150, yPosition);
                doc.text('Type', 180, yPosition);
                yPosition += lineHeight + 2;
                // Add data rows
                doc.setFont(undefined, 'normal');
                tableData.forEach((row)=>{
                    doc.text(row[0].substring(0, 15), 20, yPosition);
                    doc.text(row[1].substring(0, 20), 60, yPosition);
                    doc.text(row[2].substring(0, 15), 110, yPosition);
                    doc.text(row[3].substring(0, 12), 150, yPosition);
                    doc.text(row[4].substring(0, 10), 180, yPosition);
                    yPosition += lineHeight;
                    // Add new page if needed
                    if (yPosition > 270) {
                        doc.addPage();
                        yPosition = 20;
                    }
                });
            }
            // Save the PDF
            doc.save(`CAM_Transport_Contacts_${new Date().toISOString().split('T')[0]}.pdf`);
        } catch (error) {
            console.error('PDF Export Error:', error);
            alert('Error generating PDF. Please try again.');
        }
    };
    const columns = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "UserListTable.useMemo[columns]": ()=>[
                {
                    id: 'select',
                    header: {
                        "UserListTable.useMemo[columns]": ({ table })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Checkbox$2f$Checkbox$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                checked: table.getIsAllRowsSelected(),
                                indeterminate: table.getIsSomeRowsSelected(),
                                onChange: table.getToggleAllRowsSelectedHandler()
                            }, void 0, false, {
                                fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                lineNumber: 590,
                                columnNumber: 11
                            }, this)
                    }["UserListTable.useMemo[columns]"],
                    cell: {
                        "UserListTable.useMemo[columns]": ({ row })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Checkbox$2f$Checkbox$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                checked: row.getIsSelected(),
                                disabled: !row.getCanSelect(),
                                indeterminate: row.getIsSomeSelected(),
                                onChange: row.getToggleSelectedHandler()
                            }, void 0, false, {
                                fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                lineNumber: 599,
                                columnNumber: 11
                            }, this)
                    }["UserListTable.useMemo[columns]"]
                },
                columnHelper.accessor('fullName', {
                    header: 'User',
                    cell: {
                        "UserListTable.useMemo[columns]": ({ row })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center gap-2 sm:gap-3 min-w-[150px] sm:min-w-[200px] max-w-[250px]",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex-shrink-0",
                                        children: getAvatar({
                                            avatar: row.original.avatar,
                                            fullName: row.original.fullName
                                        })
                                    }, void 0, false, {
                                        fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                        lineNumber: 613,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex flex-col overflow-hidden min-w-0",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Typography$2f$Typography$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                color: "text.primary",
                                                className: "font-medium truncate text-sm sm:text-base",
                                                title: row.original.fullName,
                                                children: row.original.fullName
                                            }, void 0, false, {
                                                fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                                lineNumber: 617,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Typography$2f$Typography$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                variant: "body2",
                                                color: "text.secondary",
                                                className: "truncate text-xs sm:text-sm",
                                                style: {
                                                    letterSpacing: '0.3px'
                                                },
                                                title: row.original.email || row.original.username,
                                                children: row.original.email || row.original.username
                                            }, void 0, false, {
                                                fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                                lineNumber: 624,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                        lineNumber: 616,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                lineNumber: 612,
                                columnNumber: 11
                            }, this)
                    }["UserListTable.useMemo[columns]"]
                }),
                columnHelper.accessor('role', {
                    header: 'Contact',
                    cell: {
                        "UserListTable.useMemo[columns]": ({ row })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "min-w-[100px] sm:min-w-[120px] pr-3",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Typography$2f$Typography$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                    color: "text.primary",
                                    className: "font-medium text-xs sm:text-sm truncate",
                                    title: row.original.phone || row.original.contact || '+****************',
                                    children: row.original.phone || row.original.contact || '+****************'
                                }, void 0, false, {
                                    fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                    lineNumber: 641,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                lineNumber: 640,
                                columnNumber: 11
                            }, this)
                    }["UserListTable.useMemo[columns]"],
                    enableSorting: false
                }),
                columnHelper.accessor('billing', {
                    header: 'Type',
                    cell: {
                        "UserListTable.useMemo[columns]": ({ row })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "min-w-[110px] sm:min-w-[130px] pr-3",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Typography$2f$Typography$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                    color: "text.primary",
                                    className: "font-medium text-xs sm:text-sm",
                                    style: {
                                        whiteSpace: 'normal',
                                        wordWrap: 'break-word',
                                        lineHeight: '1.3'
                                    },
                                    title: row.original.inquiryType || row.original.type || 'General Inquiry',
                                    children: row.original.inquiryType || row.original.type || 'General Inquiry'
                                }, void 0, false, {
                                    fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                    lineNumber: 657,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                lineNumber: 656,
                                columnNumber: 11
                            }, this)
                    }["UserListTable.useMemo[columns]"],
                    enableSorting: false
                }),
                columnHelper.accessor('status', {
                    header: 'Status',
                    cell: {
                        "UserListTable.useMemo[columns]": ({ row })=>{
                            const status = row.original.status || 'pending';
                            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center justify-start gap-1 min-w-[110px] sm:min-w-[120px] pr-3",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(StatusDropdown, {
                                    currentStatus: status,
                                    onStatusChange: handleStatusChange,
                                    contactId: row.original.id
                                }, void 0, false, {
                                    fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                    lineNumber: 680,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                lineNumber: 679,
                                columnNumber: 13
                            }, this);
                        }
                    }["UserListTable.useMemo[columns]"],
                    enableSorting: false
                }),
                columnHelper.accessor('action', {
                    header: 'Action',
                    cell: {
                        "UserListTable.useMemo[columns]": ({ row })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center justify-start gap-2 min-w-[120px] w-[120px] pr-4",
                                style: {
                                    flexShrink: 0
                                },
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$IconButton$2f$IconButton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                        onClick: {
                                            "UserListTable.useMemo[columns]": ()=>handleViewDetails(row.original)
                                        }["UserListTable.useMemo[columns]"],
                                        title: "View Details",
                                        size: "small",
                                        sx: actionButtonStyles,
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                            className: "tabler-info-circle text-textSecondary"
                                        }, void 0, false, {
                                            fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                            lineNumber: 702,
                                            columnNumber: 15
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                        lineNumber: 696,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$IconButton$2f$IconButton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                        onClick: {
                                            "UserListTable.useMemo[columns]": ()=>handleDeleteContact(row.original.id)
                                        }["UserListTable.useMemo[columns]"],
                                        title: "Delete",
                                        size: "small",
                                        sx: actionButtonStyles,
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                            className: "tabler-trash text-textSecondary"
                                        }, void 0, false, {
                                            fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                            lineNumber: 712,
                                            columnNumber: 15
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                        lineNumber: 706,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                lineNumber: 694,
                                columnNumber: 11
                            }, this)
                    }["UserListTable.useMemo[columns]"],
                    enableSorting: false
                })
            ]
    }["UserListTable.useMemo[columns]"], // eslint-disable-next-line react-hooks/exhaustive-deps
    [
        data,
        filteredData
    ]);
    const table = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$table$2f$build$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useReactTable"])({
        data: filteredData,
        columns,
        filterFns: {
            fuzzy: fuzzyFilter
        },
        state: {
            rowSelection,
            globalFilter
        },
        initialState: {
            pagination: {
                pageSize: 10
            }
        },
        enableRowSelection: true,
        // enableRowSelection: row => row.original.age > 18, // or enable row selection conditionally per row
        globalFilterFn: fuzzyFilter,
        onRowSelectionChange: setRowSelection,
        getCoreRowModel: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$table$2d$core$2f$build$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getCoreRowModel"])(),
        onGlobalFilterChange: setGlobalFilter,
        getFilteredRowModel: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$table$2d$core$2f$build$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getFilteredRowModel"])(),
        getSortedRowModel: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$table$2d$core$2f$build$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getSortedRowModel"])(),
        getPaginationRowModel: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$table$2d$core$2f$build$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getPaginationRowModel"])(),
        getFacetedRowModel: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$table$2d$core$2f$build$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getFacetedRowModel"])(),
        getFacetedUniqueValues: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$table$2d$core$2f$build$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getFacetedUniqueValues"])(),
        getFacetedMinMaxValues: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$table$2d$core$2f$build$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getFacetedMinMaxValues"])()
    });
    const getAvatar = (params)=>{
        const { fullName } = params;
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f40$core$2f$components$2f$mui$2f$Avatar$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
            size: 34,
            children: fullName?.charAt(0)?.toUpperCase()
        }, void 0, false, {
            fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
            lineNumber: 755,
            columnNumber: 12
        }, this);
    };
    // Show loading state
    if (loading) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Card$2f$Card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$CardHeader$2f$CardHeader$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    title: "Contact List",
                    className: "pbe-4"
                }, void 0, false, {
                    fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                    lineNumber: 762,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$CardContent$2f$CardContent$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    className: "flex justify-center items-center py-8",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex flex-col items-center gap-4",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$CircularProgress$2f$CircularProgress$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                lineNumber: 765,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Typography$2f$Typography$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                variant: "body2",
                                color: "text.secondary",
                                children: "Loading contacts..."
                            }, void 0, false, {
                                fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                lineNumber: 766,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                        lineNumber: 764,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                    lineNumber: 763,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
            lineNumber: 761,
            columnNumber: 7
        }, this);
    }
    // Show error state
    if (error) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Card$2f$Card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$CardHeader$2f$CardHeader$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    title: "Contact List",
                    className: "pbe-4"
                }, void 0, false, {
                    fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                    lineNumber: 779,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$CardContent$2f$CardContent$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Alert$2f$Alert$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                            severity: "error",
                            className: "mb-4",
                            children: error
                        }, void 0, false, {
                            fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                            lineNumber: 781,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Button$2f$Button$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                            variant: "contained",
                            onClick: ()=>window.location.reload(),
                            startIcon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                className: "tabler-refresh"
                            }, void 0, false, {
                                fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                lineNumber: 787,
                                columnNumber: 24
                            }, void 0),
                            children: "Retry"
                        }, void 0, false, {
                            fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                            lineNumber: 784,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                    lineNumber: 780,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
            lineNumber: 778,
            columnNumber: 7
        }, this);
    }
    // Show empty state
    if (!data || data.length === 0) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Card$2f$Card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$CardHeader$2f$CardHeader$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    title: "Contact List",
                    className: "pbe-4"
                }, void 0, false, {
                    fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                    lineNumber: 800,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$CardContent$2f$CardContent$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    className: "flex justify-center items-center py-8",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex flex-col items-center gap-4 text-center",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                className: "tabler-users text-6xl text-textSecondary"
                            }, void 0, false, {
                                fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                lineNumber: 803,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Typography$2f$Typography$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                variant: "h6",
                                color: "text.secondary",
                                children: "No contacts found"
                            }, void 0, false, {
                                fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                lineNumber: 804,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Typography$2f$Typography$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                variant: "body2",
                                color: "text.secondary",
                                children: "Contacts submitted through the website will appear here."
                            }, void 0, false, {
                                fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                lineNumber: 807,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                        lineNumber: 802,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                    lineNumber: 801,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
            lineNumber: 799,
            columnNumber: 7
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
        children: [
            isMobile ? // Mobile View
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Card$2f$Card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                className: "w-full",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$CardHeader$2f$CardHeader$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                        title: "Contact Management",
                        subheader: "Manage contact submissions and review status",
                        className: "pb-2 px-4"
                    }, void 0, false, {
                        fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                        lineNumber: 821,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$CardContent$2f$CardContent$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                        className: "px-4",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$views$2f$apps$2f$user$2f$list$2f$TableFilters$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                setData: setFilteredData,
                                tableData: data
                            }, void 0, false, {
                                fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                lineNumber: 827,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex flex-col gap-4 mb-4 mt-4",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(DebouncedInput, {
                                        value: globalFilter ?? '',
                                        onChange: (value)=>setGlobalFilter(String(value)),
                                        placeholder: "Search contacts...",
                                        className: "w-full",
                                        size: "small"
                                    }, void 0, false, {
                                        fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                        lineNumber: 830,
                                        columnNumber: 15
                                    }, this),
                                    Object.keys(rowSelection).length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex items-center justify-between p-3 bg-primary/10 rounded-lg border border-primary/20",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex items-center gap-2",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                                        className: "tabler-check-circle text-primary"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                                        lineNumber: 842,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Typography$2f$Typography$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                        variant: "body2",
                                                        className: "font-medium text-primary",
                                                        children: [
                                                            Object.keys(rowSelection).length,
                                                            " contact",
                                                            Object.keys(rowSelection).length !== 1 ? 's' : '',
                                                            " selected"
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                                        lineNumber: 843,
                                                        columnNumber: 21
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                                lineNumber: 841,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Button$2f$Button$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                size: "small",
                                                variant: "text",
                                                onClick: ()=>setRowSelection({}),
                                                className: "text-primary",
                                                children: "Clear Selection"
                                            }, void 0, false, {
                                                fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                                lineNumber: 847,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                        lineNumber: 840,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Button$2f$Button$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                        color: "secondary",
                                        variant: "tonal",
                                        startIcon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                            className: "tabler-file-type-pdf"
                                        }, void 0, false, {
                                            fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                            lineNumber: 861,
                                            columnNumber: 28
                                        }, void 0),
                                        className: "w-full",
                                        size: "small",
                                        onClick: exportSelectedToPDF,
                                        disabled: Object.keys(rowSelection).length === 0,
                                        children: [
                                            "Export PDF (",
                                            Object.keys(rowSelection).length,
                                            ")"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                        lineNumber: 858,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$IconButton$2f$IconButton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                        color: "primary",
                                        onClick: loadContacts,
                                        disabled: loading,
                                        title: loading ? 'Loading...' : 'Refresh Data',
                                        sx: {
                                            border: '1px solid',
                                            borderColor: 'primary.main',
                                            width: '100%',
                                            height: '40px',
                                            borderRadius: '8px',
                                            '&:hover': {
                                                backgroundColor: 'primary.light',
                                                transform: 'scale(1.02)'
                                            },
                                            transition: 'all 0.2s ease-in-out'
                                        },
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                            className: `tabler-refresh ${loading ? 'animate-spin' : ''}`
                                        }, void 0, false, {
                                            fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                            lineNumber: 888,
                                            columnNumber: 17
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                        lineNumber: 870,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                lineNumber: 829,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                sx: {
                                    '& > *:not(:last-child)': {
                                        marginBottom: 2
                                    }
                                },
                                children: table.getFilteredRowModel().rows.length === 0 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Card$2f$Card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                    className: "p-8 text-center",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Typography$2f$Typography$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                        color: "text.secondary",
                                        children: "No contacts found"
                                    }, void 0, false, {
                                        fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                        lineNumber: 895,
                                        columnNumber: 19
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                    lineNumber: 894,
                                    columnNumber: 17
                                }, this) : table.getRowModel().rows.slice(0, table.getState().pagination.pageSize).map((row)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(MobileContactCard, {
                                        contact: row.original,
                                        onViewDetails: handleViewDetails,
                                        onDelete: handleDeleteContact,
                                        onStatusChange: handleStatusChange
                                    }, row.id, false, {
                                        fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                        lineNumber: 902,
                                        columnNumber: 21
                                    }, this))
                            }, void 0, false, {
                                fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                lineNumber: 892,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                className: "mt-4",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$TablePagination$2f$TablePagination$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                    component: ()=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$TablePaginationComponent$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            table: table
                                        }, void 0, false, {
                                            fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                            lineNumber: 915,
                                            columnNumber: 34
                                        }, void 0),
                                    count: table.getFilteredRowModel().rows.length,
                                    rowsPerPage: table.getState().pagination.pageSize,
                                    page: table.getState().pagination.pageIndex,
                                    onPageChange: (_, page)=>{
                                        table.setPageIndex(page);
                                    }
                                }, void 0, false, {
                                    fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                    lineNumber: 914,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                lineNumber: 913,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                        lineNumber: 826,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                lineNumber: 820,
                columnNumber: 9
            }, this) : // Desktop View
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Card$2f$Card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                className: "w-full",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$CardHeader$2f$CardHeader$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                        title: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Typography$2f$Typography$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                            variant: "h5",
                            className: "text-lg sm:text-xl lg:text-2xl",
                            children: "Contact Management"
                        }, void 0, false, {
                            fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                            lineNumber: 931,
                            columnNumber: 15
                        }, void 0),
                        subheader: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Typography$2f$Typography$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                            variant: "body2",
                            className: "text-xs sm:text-sm",
                            children: "Manage contact submissions and review status"
                        }, void 0, false, {
                            fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                            lineNumber: 936,
                            columnNumber: 15
                        }, void 0),
                        className: "pbe-4 px-3 sm:px-6"
                    }, void 0, false, {
                        fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                        lineNumber: 929,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$views$2f$apps$2f$user$2f$list$2f$TableFilters$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                        setData: setFilteredData,
                        tableData: data
                    }, void 0, false, {
                        fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                        lineNumber: 942,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex justify-between flex-col items-start lg:flex-row lg:items-center p-4 sm:p-6 border-bs gap-4",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex flex-col sm:flex-row items-start sm:items-center gap-4 w-full lg:w-auto",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f40$core$2f$components$2f$mui$2f$TextField$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                        select: true,
                                        value: table.getState().pagination.pageSize,
                                        onChange: (e)=>table.setPageSize(Number(e.target.value)),
                                        className: "w-full sm:w-[100px]",
                                        size: "small",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$MenuItem$2f$MenuItem$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                value: "10",
                                                children: "10"
                                            }, void 0, false, {
                                                fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                                lineNumber: 952,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$MenuItem$2f$MenuItem$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                value: "25",
                                                children: "25"
                                            }, void 0, false, {
                                                fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                                lineNumber: 953,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$MenuItem$2f$MenuItem$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                value: "50",
                                                children: "50"
                                            }, void 0, false, {
                                                fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                                lineNumber: 954,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                        lineNumber: 945,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Typography$2f$Typography$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                        variant: "body2",
                                        color: "text.secondary",
                                        className: "hidden sm:block",
                                        children: "entries per page"
                                    }, void 0, false, {
                                        fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                        lineNumber: 956,
                                        columnNumber: 13
                                    }, this),
                                    Object.keys(rowSelection).length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex items-center gap-2 px-3 py-1 bg-primary/10 rounded-full border border-primary/20",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                                className: "tabler-check-circle text-primary text-sm"
                                            }, void 0, false, {
                                                fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                                lineNumber: 963,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Typography$2f$Typography$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                variant: "caption",
                                                className: "font-medium text-primary",
                                                children: [
                                                    Object.keys(rowSelection).length,
                                                    " selected"
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                                lineNumber: 964,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Button$2f$Button$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                size: "small",
                                                variant: "text",
                                                onClick: ()=>setRowSelection({}),
                                                className: "text-primary p-1 min-w-auto",
                                                sx: {
                                                    fontSize: '0.7rem',
                                                    padding: '2px 6px'
                                                },
                                                children: "Clear"
                                            }, void 0, false, {
                                                fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                                lineNumber: 967,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                        lineNumber: 962,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                lineNumber: 944,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex flex-col sm:flex-row w-full lg:w-auto items-start sm:items-center gap-4",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(DebouncedInput, {
                                        value: globalFilter ?? '',
                                        onChange: (value)=>setGlobalFilter(String(value)),
                                        placeholder: "Search contacts...",
                                        className: "w-full sm:w-[250px]",
                                        size: "small"
                                    }, void 0, false, {
                                        fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                        lineNumber: 980,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Button$2f$Button$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                        color: "secondary",
                                        variant: "tonal",
                                        startIcon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                            className: "tabler-file-type-pdf"
                                        }, void 0, false, {
                                            fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                            lineNumber: 990,
                                            columnNumber: 26
                                        }, void 0),
                                        className: "w-full sm:w-auto",
                                        size: "small",
                                        onClick: exportSelectedToPDF,
                                        disabled: Object.keys(rowSelection).length === 0,
                                        children: [
                                            "Export PDF (",
                                            Object.keys(rowSelection).length,
                                            ")"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                        lineNumber: 987,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$IconButton$2f$IconButton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                        color: "primary",
                                        onClick: loadContacts,
                                        disabled: loading,
                                        title: loading ? 'Loading...' : 'Refresh Data',
                                        sx: {
                                            border: '1px solid',
                                            borderColor: 'primary.main',
                                            '&:hover': {
                                                backgroundColor: 'primary.light',
                                                transform: 'scale(1.05)'
                                            },
                                            transition: 'all 0.2s ease-in-out'
                                        },
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                            className: `tabler-refresh ${loading ? 'animate-spin' : ''}`
                                        }, void 0, false, {
                                            fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                            lineNumber: 1014,
                                            columnNumber: 15
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                        lineNumber: 999,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                lineNumber: 979,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                        lineNumber: 943,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "overflow-x-auto overflow-y-hidden",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "min-w-[580px] sm:min-w-[780px] lg:min-w-[980px]",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("table", {
                                className: `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f40$core$2f$styles$2f$table$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].table} w-full table-fixed`,
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("thead", {
                                        children: table.getHeaderGroups().map((headerGroup)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                                                children: headerGroup.headers.map((header, index)=>{
                                                    // Define column widths
                                                    let width = 'auto';
                                                    if (header.id === 'select') width = '50px';
                                                    else if (header.id === 'fullName') width = '30%';
                                                    else if (header.id === 'role') width = '20%';
                                                    else if (header.id === 'billing') width = '20%';
                                                    else if (header.id === 'status') width = '18%';
                                                    else if (header.id === 'action') width = '120px';
                                                    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                                        className: `px-3 sm:px-4 py-2 sm:py-3 ${header.id === 'action' ? 'pr-6' : ''} ${header.id === 'status' ? 'pr-3' : ''} ${header.id === 'role' ? 'pr-3' : ''} ${header.id === 'billing' ? 'pr-3' : ''}`,
                                                        style: {
                                                            width
                                                        },
                                                        children: header.isPlaceholder ? null : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
                                                                    'flex items-center': header.column.getIsSorted(),
                                                                    'cursor-pointer select-none': header.column.getCanSort()
                                                                }),
                                                                onClick: header.column.getCanSort() ? header.column.getToggleSortingHandler() : undefined,
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Typography$2f$Typography$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                                        variant: "body2",
                                                                        className: "font-semibold text-xs sm:text-sm",
                                                                        color: "text.primary",
                                                                        children: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$table$2f$build$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["flexRender"])(header.column.columnDef.header, header.getContext())
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                                                        lineNumber: 1049,
                                                                        columnNumber: 31
                                                                    }, this),
                                                                    header.column.getCanSort() && (({
                                                                        asc: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                                                            className: "tabler-chevron-up text-lg sm:text-xl ml-1"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                                                            lineNumber: 1058,
                                                                            columnNumber: 40
                                                                        }, this),
                                                                        desc: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                                                            className: "tabler-chevron-down text-lg sm:text-xl ml-1"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                                                            lineNumber: 1059,
                                                                            columnNumber: 41
                                                                        }, this)
                                                                    })[header.column.getIsSorted()] ?? null)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                                                lineNumber: 1042,
                                                                columnNumber: 29
                                                            }, this)
                                                        }, void 0, false)
                                                    }, header.id, false, {
                                                        fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                                        lineNumber: 1035,
                                                        columnNumber: 23
                                                    }, this);
                                                })
                                            }, headerGroup.id, false, {
                                                fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                                lineNumber: 1023,
                                                columnNumber: 17
                                            }, this))
                                    }, void 0, false, {
                                        fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                        lineNumber: 1021,
                                        columnNumber: 13
                                    }, this),
                                    table.getFilteredRowModel().rows.length === 0 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tbody", {
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                colSpan: table.getVisibleFlatColumns().length,
                                                className: "text-center",
                                                children: "No data available"
                                            }, void 0, false, {
                                                fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                                lineNumber: 1074,
                                                columnNumber: 19
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                            lineNumber: 1073,
                                            columnNumber: 17
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                        lineNumber: 1072,
                                        columnNumber: 15
                                    }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tbody", {
                                        children: table.getRowModel().rows.slice(0, table.getState().pagination.pageSize).map((row)=>{
                                            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                                                className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
                                                    selected: row.getIsSelected()
                                                }),
                                                children: row.getVisibleCells().map((cell, index)=>{
                                                    // Define column widths to match header
                                                    let width = 'auto';
                                                    if (cell.column.id === 'select') width = '50px';
                                                    else if (cell.column.id === 'fullName') width = '30%';
                                                    else if (cell.column.id === 'role') width = '20%';
                                                    else if (cell.column.id === 'billing') width = '20%';
                                                    else if (cell.column.id === 'status') width = '18%';
                                                    else if (cell.column.id === 'action') width = '120px';
                                                    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                        className: `px-3 sm:px-4 py-2 sm:py-3 ${cell.column.id === 'action' ? 'pr-6' : ''} ${cell.column.id === 'status' ? 'pr-3' : ''} ${cell.column.id === 'role' ? 'pr-3' : ''} ${cell.column.id === 'billing' ? 'pr-3' : ''}`,
                                                        style: {
                                                            width
                                                        },
                                                        children: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$table$2f$build$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["flexRender"])(cell.column.columnDef.cell, cell.getContext())
                                                    }, cell.id, false, {
                                                        fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                                        lineNumber: 1098,
                                                        columnNumber: 29
                                                    }, this);
                                                })
                                            }, row.id, false, {
                                                fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                                lineNumber: 1086,
                                                columnNumber: 23
                                            }, this);
                                        })
                                    }, void 0, false, {
                                        fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                        lineNumber: 1080,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                lineNumber: 1020,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                            lineNumber: 1019,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                        lineNumber: 1018,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$TablePagination$2f$TablePagination$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                        component: ()=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$TablePaginationComponent$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                table: table
                            }, void 0, false, {
                                fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                                lineNumber: 1116,
                                columnNumber: 28
                            }, void 0),
                        count: table.getFilteredRowModel().rows.length,
                        rowsPerPage: table.getState().pagination.pageSize,
                        page: table.getState().pagination.pageIndex,
                        onPageChange: (_, page)=>{
                            table.setPageIndex(page);
                        }
                    }, void 0, false, {
                        fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                        lineNumber: 1115,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                lineNumber: 928,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$views$2f$apps$2f$user$2f$list$2f$UserDetailsModal$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                open: modalOpen,
                onClose: handleModalClose,
                userData: selectedUser
            }, void 0, false, {
                fileName: "[project]/src/views/apps/user/list/UserListTable.jsx",
                lineNumber: 1127,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true);
};
_s2(UserListTable, "hPOnTLsjb/5TLPkeEI2ChSMzCvc=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$styles$2f$useTheme$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useTheme$3e$__["useTheme"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$useMediaQuery$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$table$2f$build$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useReactTable"]
    ];
});
_c3 = UserListTable;
const __TURBOPACK__default__export__ = UserListTable;
var _c, _c1, _c2, _c3;
__turbopack_context__.k.register(_c, "DebouncedInput");
__turbopack_context__.k.register(_c1, "StatusDropdown");
__turbopack_context__.k.register(_c2, "MobileContactCard");
__turbopack_context__.k.register(_c3, "UserListTable");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_0b58965e._.js.map