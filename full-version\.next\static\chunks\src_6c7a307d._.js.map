{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/services/mfaApi.js"], "sourcesContent": ["// API service for MFA (Multi-Factor Authentication) management\r\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8090'\r\n\r\n/**\r\n * Generate MFA device setup (secret and QR code)\r\n * @param {string} userId - User ID\r\n * @param {string} deviceName - Name for the device (optional)\r\n * @returns {Promise<Object>} MFA setup data including QR code and device ID\r\n */\r\nexport const generateMFASetup = async (userId, deviceName = 'Authenticator App') => {\r\n  try {\r\n    console.log('🔐 Generating MFA setup for user:', userId, 'device:', deviceName)\r\n\r\n    const response = await fetch(`${API_BASE_URL}/mfa/setup/${userId}`, {\r\n      method: 'POST',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n      },\r\n      body: JSON.stringify({ deviceName })\r\n    })\r\n\r\n    if (!response.ok) {\r\n      const errorData = await response.json()\r\n      throw new Error(errorData.message || `HTTP error! status: ${response.status}`)\r\n    }\r\n\r\n    const result = await response.json()\r\n    console.log('✅ MFA setup generated successfully:', result)\r\n\r\n    return result\r\n  } catch (error) {\r\n    console.error('❌ Error generating MFA setup:', error)\r\n    throw error\r\n  }\r\n}\r\n\r\n/**\r\n * Verify MFA device setup and activate device\r\n * @param {string} userId - User ID\r\n * @param {string} token - 6-digit TOTP token\r\n * @param {string} deviceId - Device ID from setup\r\n * @returns {Promise<Object>} Verification result with backup codes\r\n */\r\nexport const verifyMFASetup = async (userId, token, deviceId) => {\r\n  try {\r\n    console.log('🔐 Verifying MFA setup for user:', userId, 'device:', deviceId)\r\n\r\n    const response = await fetch(`${API_BASE_URL}/mfa/setup/verify/${userId}`, {\r\n      method: 'POST',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n      },\r\n      body: JSON.stringify({ token, deviceId })\r\n    })\r\n\r\n    if (!response.ok) {\r\n      const errorData = await response.json()\r\n      throw new Error(errorData.message || `HTTP error! status: ${response.status}`)\r\n    }\r\n\r\n    const result = await response.json()\r\n    console.log('✅ MFA setup verified successfully:', result)\r\n\r\n    return result\r\n  } catch (error) {\r\n    console.error('❌ Error verifying MFA setup:', error)\r\n    throw error\r\n  }\r\n}\r\n\r\n/**\r\n * Verify MFA token during login\r\n * @param {string} userId - User ID\r\n * @param {string} token - 6-digit TOTP token or backup code\r\n * @param {boolean} isBackupCode - Whether the token is a backup code\r\n * @returns {Promise<Object>} Verification result\r\n */\r\nexport const verifyMFAToken = async (userId, token, isBackupCode = false) => {\r\n  try {\r\n    console.log('🔐 Verifying MFA token for user:', userId)\r\n\r\n    const response = await fetch(`${API_BASE_URL}/mfa/verify/${userId}`, {\r\n      method: 'POST',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n      },\r\n      body: JSON.stringify({ token, isBackupCode })\r\n    })\r\n\r\n    if (!response.ok) {\r\n      const errorData = await response.json()\r\n      throw new Error(errorData.message || `HTTP error! status: ${response.status}`)\r\n    }\r\n\r\n    const result = await response.json()\r\n    console.log('✅ MFA token verified successfully:', result)\r\n\r\n    return result\r\n  } catch (error) {\r\n    console.error('❌ Error verifying MFA token:', error)\r\n    throw error\r\n  }\r\n}\r\n\r\n/**\r\n * Get MFA status for a user\r\n * @param {string} userId - User ID\r\n * @returns {Promise<Object>} MFA status information\r\n */\r\nexport const getMFAStatus = async (userId) => {\r\n  try {\r\n    console.log('🔍 Getting MFA status for user:', userId)\r\n\r\n    const response = await fetch(`${API_BASE_URL}/mfa/status/${userId}`, {\r\n      method: 'GET',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n      },\r\n    })\r\n\r\n    if (!response.ok) {\r\n      const errorData = await response.json()\r\n      throw new Error(errorData.message || `HTTP error! status: ${response.status}`)\r\n    }\r\n\r\n    const result = await response.json()\r\n    console.log('✅ MFA status retrieved successfully:', result)\r\n\r\n    return result\r\n  } catch (error) {\r\n    console.error('❌ Error getting MFA status:', error)\r\n    throw error\r\n  }\r\n}\r\n\r\n/**\r\n * Disable MFA for a user\r\n * @param {string} userId - User ID\r\n * @param {string} password - User's current password\r\n * @returns {Promise<Object>} Disable result\r\n */\r\nexport const disableMFA = async (userId, password) => {\r\n  try {\r\n    console.log('🔐 Disabling MFA for user:', userId)\r\n\r\n    const response = await fetch(`${API_BASE_URL}/mfa/disable/${userId}`, {\r\n      method: 'POST',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n      },\r\n      body: JSON.stringify({ password })\r\n    })\r\n\r\n    if (!response.ok) {\r\n      const errorData = await response.json()\r\n      throw new Error(errorData.message || `HTTP error! status: ${response.status}`)\r\n    }\r\n\r\n    const result = await response.json()\r\n    console.log('✅ MFA disabled successfully:', result)\r\n\r\n    return result\r\n  } catch (error) {\r\n    console.error('❌ Error disabling MFA:', error)\r\n    throw error\r\n  }\r\n}\r\n\r\n/**\r\n * Generate new backup codes\r\n * @param {string} userId - User ID\r\n * @returns {Promise<Object>} New backup codes\r\n */\r\nexport const generateBackupCodes = async (userId) => {\r\n  try {\r\n    console.log('🔐 Generating backup codes for user:', userId)\r\n\r\n    const response = await fetch(`${API_BASE_URL}/mfa/backup-codes/${userId}`, {\r\n      method: 'POST',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n      },\r\n    })\r\n\r\n    if (!response.ok) {\r\n      const errorData = await response.json()\r\n      throw new Error(errorData.message || `HTTP error! status: ${response.status}`)\r\n    }\r\n\r\n    const result = await response.json()\r\n    console.log('✅ Backup codes generated successfully:', result)\r\n\r\n    return result\r\n  } catch (error) {\r\n    console.error('❌ Error generating backup codes:', error)\r\n    throw error\r\n  }\r\n}\r\n\r\n/**\r\n * Get all MFA devices for a user\r\n * @param {string} userId - User ID\r\n * @returns {Promise<Object>} List of MFA devices\r\n */\r\nexport const getMFADevices = async (userId) => {\r\n  try {\r\n    console.log('📱 Getting MFA devices for user:', userId)\r\n\r\n    const response = await fetch(`${API_BASE_URL}/mfa/devices/${userId}`, {\r\n      method: 'GET',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n      },\r\n    })\r\n\r\n    if (!response.ok) {\r\n      const errorData = await response.json()\r\n      throw new Error(errorData.message || `HTTP error! status: ${response.status}`)\r\n    }\r\n\r\n    const result = await response.json()\r\n    console.log('✅ MFA devices retrieved successfully:', result)\r\n\r\n    return result\r\n  } catch (error) {\r\n    console.error('❌ Error getting MFA devices:', error)\r\n    throw error\r\n  }\r\n}\r\n\r\n/**\r\n * Update device name\r\n * @param {string} userId - User ID\r\n * @param {string} deviceId - Device ID\r\n * @param {string} deviceName - New device name\r\n * @returns {Promise<Object>} Update result\r\n */\r\nexport const updateDeviceName = async (userId, deviceId, deviceName) => {\r\n  try {\r\n    console.log('📝 Updating device name for user:', userId, 'device:', deviceId)\r\n\r\n    const response = await fetch(`${API_BASE_URL}/mfa/devices/${userId}/${deviceId}/name`, {\r\n      method: 'PUT',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n      },\r\n      body: JSON.stringify({ deviceName })\r\n    })\r\n\r\n    if (!response.ok) {\r\n      const errorData = await response.json()\r\n      throw new Error(errorData.message || `HTTP error! status: ${response.status}`)\r\n    }\r\n\r\n    const result = await response.json()\r\n    console.log('✅ Device name updated successfully:', result)\r\n\r\n    return result\r\n  } catch (error) {\r\n    console.error('❌ Error updating device name:', error)\r\n    throw error\r\n  }\r\n}\r\n\r\n/**\r\n * Remove/deactivate a device\r\n * @param {string} userId - User ID\r\n * @param {string} deviceId - Device ID\r\n * @returns {Promise<Object>} Removal result\r\n */\r\nexport const removeDevice = async (userId, deviceId) => {\r\n  try {\r\n    console.log('🗑️ Removing device for user:', userId, 'device:', deviceId)\r\n\r\n    const response = await fetch(`${API_BASE_URL}/mfa/devices/${userId}/${deviceId}`, {\r\n      method: 'DELETE',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n      },\r\n    })\r\n\r\n    if (!response.ok) {\r\n      const errorData = await response.json()\r\n      throw new Error(errorData.message || `HTTP error! status: ${response.status}`)\r\n    }\r\n\r\n    const result = await response.json()\r\n    console.log('✅ Device removed successfully:', result)\r\n\r\n    return result\r\n  } catch (error) {\r\n    console.error('❌ Error removing device:', error)\r\n    throw error\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA,+DAA+D;;;;;;;;;;;;AAC1C;AAArB,MAAM,eAAe,6DAAmC;AAQjD,MAAM,mBAAmB,OAAO,QAAQ,aAAa,mBAAmB;IAC7E,IAAI;QACF,QAAQ,GAAG,CAAC,qCAAqC,QAAQ,WAAW;QAEpE,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,WAAW,EAAE,QAAQ,EAAE;YAClE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBAAE;YAAW;QACpC;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC/E;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAClC,QAAQ,GAAG,CAAC,uCAAuC;QAEnD,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,MAAM;IACR;AACF;AASO,MAAM,iBAAiB,OAAO,QAAQ,OAAO;IAClD,IAAI;QACF,QAAQ,GAAG,CAAC,oCAAoC,QAAQ,WAAW;QAEnE,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,kBAAkB,EAAE,QAAQ,EAAE;YACzE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBAAE;gBAAO;YAAS;QACzC;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC/E;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAClC,QAAQ,GAAG,CAAC,sCAAsC;QAElD,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,MAAM;IACR;AACF;AASO,MAAM,iBAAiB,OAAO,QAAQ,OAAO,eAAe,KAAK;IACtE,IAAI;QACF,QAAQ,GAAG,CAAC,oCAAoC;QAEhD,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,YAAY,EAAE,QAAQ,EAAE;YACnE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBAAE;gBAAO;YAAa;QAC7C;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC/E;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAClC,QAAQ,GAAG,CAAC,sCAAsC;QAElD,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,MAAM;IACR;AACF;AAOO,MAAM,eAAe,OAAO;IACjC,IAAI;QACF,QAAQ,GAAG,CAAC,mCAAmC;QAE/C,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,YAAY,EAAE,QAAQ,EAAE;YACnE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC/E;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAClC,QAAQ,GAAG,CAAC,wCAAwC;QAEpD,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,MAAM;IACR;AACF;AAQO,MAAM,aAAa,OAAO,QAAQ;IACvC,IAAI;QACF,QAAQ,GAAG,CAAC,8BAA8B;QAE1C,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,aAAa,EAAE,QAAQ,EAAE;YACpE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBAAE;YAAS;QAClC;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC/E;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAClC,QAAQ,GAAG,CAAC,gCAAgC;QAE5C,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,MAAM;IACR;AACF;AAOO,MAAM,sBAAsB,OAAO;IACxC,IAAI;QACF,QAAQ,GAAG,CAAC,wCAAwC;QAEpD,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,kBAAkB,EAAE,QAAQ,EAAE;YACzE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC/E;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAClC,QAAQ,GAAG,CAAC,0CAA0C;QAEtD,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,MAAM;IACR;AACF;AAOO,MAAM,gBAAgB,OAAO;IAClC,IAAI;QACF,QAAQ,GAAG,CAAC,oCAAoC;QAEhD,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,aAAa,EAAE,QAAQ,EAAE;YACpE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC/E;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAClC,QAAQ,GAAG,CAAC,yCAAyC;QAErD,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,MAAM;IACR;AACF;AASO,MAAM,mBAAmB,OAAO,QAAQ,UAAU;IACvD,IAAI;QACF,QAAQ,GAAG,CAAC,qCAAqC,QAAQ,WAAW;QAEpE,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,aAAa,EAAE,OAAO,CAAC,EAAE,SAAS,KAAK,CAAC,EAAE;YACrF,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBAAE;YAAW;QACpC;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC/E;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAClC,QAAQ,GAAG,CAAC,uCAAuC;QAEnD,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,MAAM;IACR;AACF;AAQO,MAAM,eAAe,OAAO,QAAQ;IACzC,IAAI;QACF,QAAQ,GAAG,CAAC,iCAAiC,QAAQ,WAAW;QAEhE,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,aAAa,EAAE,OAAO,CAAC,EAAE,UAAU,EAAE;YAChF,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC/E;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAClC,QAAQ,GAAG,CAAC,kCAAkC;QAE9C,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM;IACR;AACF", "debugId": null}}, {"offset": {"line": 236, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/components/MFA/MFASetup.jsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState } from 'react'\r\nimport {\r\n  <PERSON><PERSON>,\r\n  DialogTitle,\r\n  <PERSON>alogContent,\r\n  <PERSON>alog<PERSON>ctions,\r\n  <PERSON>ton,\r\n  <PERSON>po<PERSON>,\r\n  Box,\r\n  TextField,\r\n  Stepper,\r\n  Step,\r\n  StepLabel,\r\n  Alert,\r\n  Card,\r\n  CardContent,\r\n  Chip,\r\n  Grid,\r\n  IconButton,\r\n  Tooltip\r\n} from '@mui/material'\r\nimport { ContentCopy, Download, Security, Smartphone, Key } from '@mui/icons-material'\r\nimport { generateMFASetup, verifyMFASetup } from '@/services/mfaApi'\r\n\r\nconst MFASetup = ({ open, onClose, userId, onSuccess, isAddingDevice = false }) => {\r\n  const [activeStep, setActiveStep] = useState(0)\r\n  const [loading, setLoading] = useState(false)\r\n  const [error, setError] = useState('')\r\n  const [mfaData, setMfaData] = useState(null)\r\n  const [verificationToken, setVerificationToken] = useState('')\r\n  const [backupCodes, setBackupCodes] = useState([])\r\n  const [deviceName, setDeviceName] = useState('')\r\n\r\n  const steps = isAddingDevice\r\n    ? ['Device Info', 'Scan QR Code', 'Verify Setup', 'Complete']\r\n    : ['Generate Setup', 'Scan QR Code', 'Verify Setup', 'Save Backup Codes']\r\n\r\n  const handleGenerateSetup = async () => {\r\n    try {\r\n      setLoading(true)\r\n      setError('')\r\n\r\n      const deviceNameToUse = isAddingDevice ? deviceName || 'New Device' : 'Authenticator App'\r\n      const result = await generateMFASetup(userId, deviceNameToUse)\r\n      setMfaData(result.data)\r\n      setActiveStep(isAddingDevice ? 1 : 1)\r\n    } catch (err) {\r\n      setError(err.message)\r\n    } finally {\r\n      setLoading(false)\r\n    }\r\n  }\r\n\r\n  const handleVerifySetup = async () => {\r\n    if (!verificationToken || verificationToken.length !== 6) {\r\n      setError('Please enter a valid 6-digit code')\r\n      return\r\n    }\r\n\r\n    if (!mfaData?.deviceId) {\r\n      setError('Device setup data is missing. Please restart the setup.')\r\n      return\r\n    }\r\n\r\n    try {\r\n      setLoading(true)\r\n      setError('')\r\n\r\n      const result = await verifyMFASetup(userId, verificationToken, mfaData.deviceId)\r\n\r\n      if (result.data.isFirstDevice) {\r\n        setBackupCodes(result.data.backupCodes)\r\n      }\r\n\r\n      setActiveStep(3)\r\n\r\n      // Show success message\r\n      if (onSuccess) {\r\n        const message = isAddingDevice\r\n          ? `Device \"${mfaData.deviceName}\" has been successfully added!`\r\n          : 'MFA has been successfully enabled for your account!'\r\n        onSuccess(message)\r\n      }\r\n    } catch (err) {\r\n      setError(err.message)\r\n    } finally {\r\n      setLoading(false)\r\n    }\r\n  }\r\n\r\n  const copyToClipboard = (text) => {\r\n    navigator.clipboard.writeText(text)\r\n    // You could add a toast notification here\r\n  }\r\n\r\n  const downloadBackupCodes = () => {\r\n    const content = `CAM Transport - MFA Backup Codes\\n\\nGenerated: ${new Date().toLocaleString()}\\n\\nBackup Codes:\\n${backupCodes.join('\\n')}\\n\\nImportant:\\n- Keep these codes safe and secure\\n- Each code can only be used once\\n- Use these codes if you lose access to your authenticator app\\n- Generate new codes if you suspect these have been compromised`\r\n\r\n    const blob = new Blob([content], { type: 'text/plain' })\r\n    const url = URL.createObjectURL(blob)\r\n    const a = document.createElement('a')\r\n    a.href = url\r\n    a.download = 'cam-transport-backup-codes.txt'\r\n    document.body.appendChild(a)\r\n    a.click()\r\n    document.body.removeChild(a)\r\n    URL.revokeObjectURL(url)\r\n  }\r\n\r\n  const handleClose = () => {\r\n    setActiveStep(0)\r\n    setMfaData(null)\r\n    setVerificationToken('')\r\n    setBackupCodes([])\r\n    setError('')\r\n    onClose()\r\n  }\r\n\r\n  const renderStepContent = () => {\r\n    switch (activeStep) {\r\n      case 0:\r\n        if (isAddingDevice) {\r\n          return (\r\n            <Box sx={{ py: 3 }}>\r\n              <Typography variant=\"h6\" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\r\n                <Smartphone /> Add New Device\r\n              </Typography>\r\n              <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 3 }}>\r\n                Give your new authenticator device a name to help you identify it later.\r\n              </Typography>\r\n\r\n              <TextField\r\n                fullWidth\r\n                label=\"Device Name\"\r\n                value={deviceName}\r\n                onChange={(e) => setDeviceName(e.target.value)}\r\n                placeholder=\"e.g., iPhone, Work Phone, Personal Tablet\"\r\n                sx={{ mb: 3 }}\r\n              />\r\n\r\n              <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end' }}>\r\n                <Button onClick={handleClose}>\r\n                  Cancel\r\n                </Button>\r\n                <Button\r\n                  variant=\"contained\"\r\n                  onClick={handleGenerateSetup}\r\n                  disabled={loading}\r\n                >\r\n                  {loading ? 'Generating...' : 'Continue'}\r\n                </Button>\r\n              </Box>\r\n            </Box>\r\n          )\r\n        }\r\n\r\n        return (\r\n          <Box sx={{ textAlign: 'center', py: 3 }}>\r\n            <Security sx={{ fontSize: 64, color: 'primary.main', mb: 2 }} />\r\n            <Typography variant=\"h6\" gutterBottom>\r\n              Enable Two-Factor Authentication\r\n            </Typography>\r\n            <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 3 }}>\r\n              Add an extra layer of security to your account by enabling MFA.\r\n              You'll need an authenticator app like Google Authenticator or Authy.\r\n            </Typography>\r\n            <Button\r\n              variant=\"contained\"\r\n              onClick={handleGenerateSetup}\r\n              disabled={loading}\r\n              size=\"large\"\r\n            >\r\n              {loading ? 'Generating...' : 'Get Started'}\r\n            </Button>\r\n          </Box>\r\n        )\r\n\r\n      case 1:\r\n        return (\r\n          <Box sx={{ py: 2 }}>\r\n            <Typography variant=\"h6\" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\r\n              <Smartphone /> Scan QR Code\r\n            </Typography>\r\n            <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 3 }}>\r\n              Open your authenticator app and scan this QR code:\r\n            </Typography>\r\n\r\n            {mfaData && (\r\n              <Box sx={{ textAlign: 'center', mb: 3 }}>\r\n                <img\r\n                  src={mfaData.qrCode}\r\n                  alt=\"MFA QR Code\"\r\n                  style={{ maxWidth: '200px', border: '1px solid #ddd', borderRadius: '8px' }}\r\n                />\r\n              </Box>\r\n            )}\r\n\r\n            <Alert severity=\"info\" sx={{ mb: 2 }}>\r\n              Can't scan the QR code? Enter this key manually in your authenticator app:\r\n            </Alert>\r\n\r\n            {mfaData && (\r\n              <Card variant=\"outlined\" sx={{ mb: 3 }}>\r\n                <CardContent sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\r\n                  <Typography variant=\"body2\" sx={{ fontFamily: 'monospace', wordBreak: 'break-all' }}>\r\n                    {mfaData.manualEntryKey}\r\n                  </Typography>\r\n                  <Tooltip title=\"Copy to clipboard\">\r\n                    <IconButton onClick={() => copyToClipboard(mfaData.manualEntryKey)}>\r\n                      <ContentCopy />\r\n                    </IconButton>\r\n                  </Tooltip>\r\n                </CardContent>\r\n              </Card>\r\n            )}\r\n\r\n            <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end' }}>\r\n              <Button onClick={() => setActiveStep(0)}>\r\n                Back\r\n              </Button>\r\n              <Button variant=\"contained\" onClick={() => setActiveStep(2)}>\r\n                I've Added the Account\r\n              </Button>\r\n            </Box>\r\n          </Box>\r\n        )\r\n\r\n      case 2:\r\n        return (\r\n          <Box sx={{ py: 2 }}>\r\n            <Typography variant=\"h6\" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\r\n              <Key /> Verify Setup\r\n            </Typography>\r\n            <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 3 }}>\r\n              Enter the 6-digit code from your authenticator app to verify the setup:\r\n            </Typography>\r\n\r\n            <TextField\r\n              fullWidth\r\n              label=\"Verification Code\"\r\n              value={verificationToken}\r\n              onChange={(e) => setVerificationToken(e.target.value.replace(/\\D/g, '').slice(0, 6))}\r\n              placeholder=\"000000\"\r\n              inputProps={{\r\n                style: { textAlign: 'center', fontSize: '1.5rem', letterSpacing: '0.5rem' },\r\n                maxLength: 6\r\n              }}\r\n              sx={{ mb: 3 }}\r\n            />\r\n\r\n            <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end' }}>\r\n              <Button onClick={() => setActiveStep(1)}>\r\n                Back\r\n              </Button>\r\n              <Button\r\n                variant=\"contained\"\r\n                onClick={handleVerifySetup}\r\n                disabled={loading || verificationToken.length !== 6}\r\n              >\r\n                {loading ? 'Verifying...' : 'Verify & Enable MFA'}\r\n              </Button>\r\n            </Box>\r\n          </Box>\r\n        )\r\n\r\n      case 3:\r\n        if (isAddingDevice) {\r\n          return (\r\n            <Box sx={{ py: 2, textAlign: 'center' }}>\r\n              <Security sx={{ fontSize: 64, color: 'success.main', mb: 2 }} />\r\n              <Typography variant=\"h6\" gutterBottom>\r\n                Device Added Successfully!\r\n              </Typography>\r\n              <Alert severity=\"success\" sx={{ mb: 2 }}>\r\n                🎉 Your device \"{mfaData?.deviceName}\" has been added to your account!\r\n              </Alert>\r\n              <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 3 }}>\r\n                You can now use this device for two-factor authentication when logging in.\r\n              </Typography>\r\n\r\n              <Button variant=\"contained\" onClick={handleClose}>\r\n                Done\r\n              </Button>\r\n            </Box>\r\n          )\r\n        }\r\n\r\n        return (\r\n          <Box sx={{ py: 2 }}>\r\n            <Typography variant=\"h6\" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\r\n              <Download /> Save Backup Codes\r\n            </Typography>\r\n            <Alert severity=\"success\" sx={{ mb: 2 }}>\r\n              🎉 MFA has been successfully enabled for your account!\r\n            </Alert>\r\n            <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 3 }}>\r\n              Save these backup codes in a safe place. You can use them to access your account if you lose your authenticator device.\r\n            </Typography>\r\n\r\n            {backupCodes.length > 0 && (\r\n              <Card variant=\"outlined\" sx={{ mb: 3 }}>\r\n                <CardContent>\r\n                  <Grid container spacing={1}>\r\n                    {backupCodes.map((code, index) => (\r\n                      <Grid item xs={6} key={index}>\r\n                        <Chip\r\n                          label={code}\r\n                          variant=\"outlined\"\r\n                          sx={{ fontFamily: 'monospace', width: '100%' }}\r\n                        />\r\n                      </Grid>\r\n                    ))}\r\n                  </Grid>\r\n                </CardContent>\r\n              </Card>\r\n            )}\r\n\r\n            <Box sx={{ display: 'flex', gap: 2, justifyContent: 'space-between' }}>\r\n              {backupCodes.length > 0 && (\r\n                <Button\r\n                  variant=\"outlined\"\r\n                  startIcon={<Download />}\r\n                  onClick={downloadBackupCodes}\r\n                >\r\n                  Download Codes\r\n                </Button>\r\n              )}\r\n              <Button variant=\"contained\" onClick={handleClose}>\r\n                Complete Setup\r\n              </Button>\r\n            </Box>\r\n          </Box>\r\n        )\r\n\r\n      default:\r\n        return null\r\n    }\r\n  }\r\n\r\n  return (\r\n    <Dialog open={open} onClose={handleClose} maxWidth=\"sm\" fullWidth>\r\n      <DialogTitle>\r\n        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\r\n          <Security />\r\n          Multi-Factor Authentication Setup\r\n        </Box>\r\n      </DialogTitle>\r\n\r\n      <DialogContent>\r\n        <Stepper activeStep={activeStep} sx={{ mb: 3 }}>\r\n          {steps.map((label) => (\r\n            <Step key={label}>\r\n              <StepLabel>{label}</StepLabel>\r\n            </Step>\r\n          ))}\r\n        </Stepper>\r\n\r\n        {error && (\r\n          <Alert severity=\"error\" sx={{ mb: 2 }}>\r\n            {error}\r\n          </Alert>\r\n        )}\r\n\r\n        {renderStepContent()}\r\n      </DialogContent>\r\n    </Dialog>\r\n  )\r\n}\r\n\r\nexport default MFASetup\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAoBA;AAAA;AAAA;AAAA;AAAA;AACA;;;AAxBA;;;;;;;;;AA0BA,MAAM,WAAW,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,iBAAiB,KAAK,EAAE;;IAC5E,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,QAAQ,iBACV;QAAC;QAAe;QAAgB;QAAgB;KAAW,GAC3D;QAAC;QAAkB;QAAgB;QAAgB;KAAoB;IAE3E,MAAM,sBAAsB;QAC1B,IAAI;YACF,WAAW;YACX,SAAS;YAET,MAAM,kBAAkB,iBAAiB,cAAc,eAAe;YACtE,MAAM,SAAS,MAAM,CAAA,GAAA,4HAAA,CAAA,mBAAgB,AAAD,EAAE,QAAQ;YAC9C,WAAW,OAAO,IAAI;YACtB,cAAc,iBAAiB,IAAI;QACrC,EAAE,OAAO,KAAK;YACZ,SAAS,IAAI,OAAO;QACtB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,oBAAoB;QACxB,IAAI,CAAC,qBAAqB,kBAAkB,MAAM,KAAK,GAAG;YACxD,SAAS;YACT;QACF;QAEA,IAAI,CAAC,SAAS,UAAU;YACtB,SAAS;YACT;QACF;QAEA,IAAI;YACF,WAAW;YACX,SAAS;YAET,MAAM,SAAS,MAAM,CAAA,GAAA,4HAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,mBAAmB,QAAQ,QAAQ;YAE/E,IAAI,OAAO,IAAI,CAAC,aAAa,EAAE;gBAC7B,eAAe,OAAO,IAAI,CAAC,WAAW;YACxC;YAEA,cAAc;YAEd,uBAAuB;YACvB,IAAI,WAAW;gBACb,MAAM,UAAU,iBACZ,CAAC,QAAQ,EAAE,QAAQ,UAAU,CAAC,8BAA8B,CAAC,GAC7D;gBACJ,UAAU;YACZ;QACF,EAAE,OAAO,KAAK;YACZ,SAAS,IAAI,OAAO;QACtB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,UAAU,SAAS,CAAC,SAAS,CAAC;IAC9B,0CAA0C;IAC5C;IAEA,MAAM,sBAAsB;QAC1B,MAAM,UAAU,CAAC,+CAA+C,EAAE,IAAI,OAAO,cAAc,GAAG,mBAAmB,EAAE,YAAY,IAAI,CAAC,MAAM,sNAAsN,CAAC;QAEjW,MAAM,OAAO,IAAI,KAAK;YAAC;SAAQ,EAAE;YAAE,MAAM;QAAa;QACtD,MAAM,MAAM,IAAI,eAAe,CAAC;QAChC,MAAM,IAAI,SAAS,aAAa,CAAC;QACjC,EAAE,IAAI,GAAG;QACT,EAAE,QAAQ,GAAG;QACb,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,EAAE,KAAK;QACP,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,IAAI,eAAe,CAAC;IACtB;IAEA,MAAM,cAAc;QAClB,cAAc;QACd,WAAW;QACX,qBAAqB;QACrB,eAAe,EAAE;QACjB,SAAS;QACT;IACF;IAEA,MAAM,oBAAoB;QACxB,OAAQ;YACN,KAAK;gBACH,IAAI,gBAAgB;oBAClB,qBACE,6LAAC,oLAAA,CAAA,MAAG;wBAAC,IAAI;4BAAE,IAAI;wBAAE;;0CACf,6LAAC,yMAAA,CAAA,aAAU;gCAAC,SAAQ;gCAAK,YAAY;gCAAC,IAAI;oCAAE,SAAS;oCAAQ,YAAY;oCAAU,KAAK;gCAAE;;kDACxF,6LAAC,kKAAA,CAAA,UAAU;;;;;oCAAG;;;;;;;0CAEhB,6LAAC,yMAAA,CAAA,aAAU;gCAAC,SAAQ;gCAAQ,OAAM;gCAAiB,IAAI;oCAAE,IAAI;gCAAE;0CAAG;;;;;;0CAIlE,6LAAC,sMAAA,CAAA,YAAS;gCACR,SAAS;gCACT,OAAM;gCACN,OAAO;gCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gCAC7C,aAAY;gCACZ,IAAI;oCAAE,IAAI;gCAAE;;;;;;0CAGd,6LAAC,oLAAA,CAAA,MAAG;gCAAC,IAAI;oCAAE,SAAS;oCAAQ,KAAK;oCAAG,gBAAgB;gCAAW;;kDAC7D,6LAAC,6LAAA,CAAA,SAAM;wCAAC,SAAS;kDAAa;;;;;;kDAG9B,6LAAC,6LAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,SAAS;wCACT,UAAU;kDAET,UAAU,kBAAkB;;;;;;;;;;;;;;;;;;gBAKvC;gBAEA,qBACE,6LAAC,oLAAA,CAAA,MAAG;oBAAC,IAAI;wBAAE,WAAW;wBAAU,IAAI;oBAAE;;sCACpC,6LAAC,gKAAA,CAAA,UAAQ;4BAAC,IAAI;gCAAE,UAAU;gCAAI,OAAO;gCAAgB,IAAI;4BAAE;;;;;;sCAC3D,6LAAC,yMAAA,CAAA,aAAU;4BAAC,SAAQ;4BAAK,YAAY;sCAAC;;;;;;sCAGtC,6LAAC,yMAAA,CAAA,aAAU;4BAAC,SAAQ;4BAAQ,OAAM;4BAAiB,IAAI;gCAAE,IAAI;4BAAE;sCAAG;;;;;;sCAIlE,6LAAC,6LAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,SAAS;4BACT,UAAU;4BACV,MAAK;sCAEJ,UAAU,kBAAkB;;;;;;;;;;;;YAKrC,KAAK;gBACH,qBACE,6LAAC,oLAAA,CAAA,MAAG;oBAAC,IAAI;wBAAE,IAAI;oBAAE;;sCACf,6LAAC,yMAAA,CAAA,aAAU;4BAAC,SAAQ;4BAAK,YAAY;4BAAC,IAAI;gCAAE,SAAS;gCAAQ,YAAY;gCAAU,KAAK;4BAAE;;8CACxF,6LAAC,kKAAA,CAAA,UAAU;;;;;gCAAG;;;;;;;sCAEhB,6LAAC,yMAAA,CAAA,aAAU;4BAAC,SAAQ;4BAAQ,OAAM;4BAAiB,IAAI;gCAAE,IAAI;4BAAE;sCAAG;;;;;;wBAIjE,yBACC,6LAAC,oLAAA,CAAA,MAAG;4BAAC,IAAI;gCAAE,WAAW;gCAAU,IAAI;4BAAE;sCACpC,cAAA,6LAAC;gCACC,KAAK,QAAQ,MAAM;gCACnB,KAAI;gCACJ,OAAO;oCAAE,UAAU;oCAAS,QAAQ;oCAAkB,cAAc;gCAAM;;;;;;;;;;;sCAKhF,6LAAC,0LAAA,CAAA,QAAK;4BAAC,UAAS;4BAAO,IAAI;gCAAE,IAAI;4BAAE;sCAAG;;;;;;wBAIrC,yBACC,6LAAC,uLAAA,CAAA,OAAI;4BAAC,SAAQ;4BAAW,IAAI;gCAAE,IAAI;4BAAE;sCACnC,cAAA,6LAAC,4MAAA,CAAA,cAAW;gCAAC,IAAI;oCAAE,SAAS;oCAAQ,YAAY;oCAAU,gBAAgB;gCAAgB;;kDACxF,6LAAC,yMAAA,CAAA,aAAU;wCAAC,SAAQ;wCAAQ,IAAI;4CAAE,YAAY;4CAAa,WAAW;wCAAY;kDAC/E,QAAQ,cAAc;;;;;;kDAEzB,6LAAC,gMAAA,CAAA,UAAO;wCAAC,OAAM;kDACb,cAAA,6LAAC,yMAAA,CAAA,aAAU;4CAAC,SAAS,IAAM,gBAAgB,QAAQ,cAAc;sDAC/D,cAAA,6LAAC,mKAAA,CAAA,UAAW;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOtB,6LAAC,oLAAA,CAAA,MAAG;4BAAC,IAAI;gCAAE,SAAS;gCAAQ,KAAK;gCAAG,gBAAgB;4BAAW;;8CAC7D,6LAAC,6LAAA,CAAA,SAAM;oCAAC,SAAS,IAAM,cAAc;8CAAI;;;;;;8CAGzC,6LAAC,6LAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAY,SAAS,IAAM,cAAc;8CAAI;;;;;;;;;;;;;;;;;;YAOrE,KAAK;gBACH,qBACE,6LAAC,oLAAA,CAAA,MAAG;oBAAC,IAAI;wBAAE,IAAI;oBAAE;;sCACf,6LAAC,yMAAA,CAAA,aAAU;4BAAC,SAAQ;4BAAK,YAAY;4BAAC,IAAI;gCAAE,SAAS;gCAAQ,YAAY;gCAAU,KAAK;4BAAE;;8CACxF,6LAAC,2JAAA,CAAA,UAAG;;;;;gCAAG;;;;;;;sCAET,6LAAC,yMAAA,CAAA,aAAU;4BAAC,SAAQ;4BAAQ,OAAM;4BAAiB,IAAI;gCAAE,IAAI;4BAAE;sCAAG;;;;;;sCAIlE,6LAAC,sMAAA,CAAA,YAAS;4BACR,SAAS;4BACT,OAAM;4BACN,OAAO;4BACP,UAAU,CAAC,IAAM,qBAAqB,EAAE,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,IAAI,KAAK,CAAC,GAAG;4BACjF,aAAY;4BACZ,YAAY;gCACV,OAAO;oCAAE,WAAW;oCAAU,UAAU;oCAAU,eAAe;gCAAS;gCAC1E,WAAW;4BACb;4BACA,IAAI;gCAAE,IAAI;4BAAE;;;;;;sCAGd,6LAAC,oLAAA,CAAA,MAAG;4BAAC,IAAI;gCAAE,SAAS;gCAAQ,KAAK;gCAAG,gBAAgB;4BAAW;;8CAC7D,6LAAC,6LAAA,CAAA,SAAM;oCAAC,SAAS,IAAM,cAAc;8CAAI;;;;;;8CAGzC,6LAAC,6LAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS;oCACT,UAAU,WAAW,kBAAkB,MAAM,KAAK;8CAEjD,UAAU,iBAAiB;;;;;;;;;;;;;;;;;;YAMtC,KAAK;gBACH,IAAI,gBAAgB;oBAClB,qBACE,6LAAC,oLAAA,CAAA,MAAG;wBAAC,IAAI;4BAAE,IAAI;4BAAG,WAAW;wBAAS;;0CACpC,6LAAC,gKAAA,CAAA,UAAQ;gCAAC,IAAI;oCAAE,UAAU;oCAAI,OAAO;oCAAgB,IAAI;gCAAE;;;;;;0CAC3D,6LAAC,yMAAA,CAAA,aAAU;gCAAC,SAAQ;gCAAK,YAAY;0CAAC;;;;;;0CAGtC,6LAAC,0LAAA,CAAA,QAAK;gCAAC,UAAS;gCAAU,IAAI;oCAAE,IAAI;gCAAE;;oCAAG;oCACtB,SAAS;oCAAW;;;;;;;0CAEvC,6LAAC,yMAAA,CAAA,aAAU;gCAAC,SAAQ;gCAAQ,OAAM;gCAAiB,IAAI;oCAAE,IAAI;gCAAE;0CAAG;;;;;;0CAIlE,6LAAC,6LAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAY,SAAS;0CAAa;;;;;;;;;;;;gBAKxD;gBAEA,qBACE,6LAAC,oLAAA,CAAA,MAAG;oBAAC,IAAI;wBAAE,IAAI;oBAAE;;sCACf,6LAAC,yMAAA,CAAA,aAAU;4BAAC,SAAQ;4BAAK,YAAY;4BAAC,IAAI;gCAAE,SAAS;gCAAQ,YAAY;gCAAU,KAAK;4BAAE;;8CACxF,6LAAC,gKAAA,CAAA,UAAQ;;;;;gCAAG;;;;;;;sCAEd,6LAAC,0LAAA,CAAA,QAAK;4BAAC,UAAS;4BAAU,IAAI;gCAAE,IAAI;4BAAE;sCAAG;;;;;;sCAGzC,6LAAC,yMAAA,CAAA,aAAU;4BAAC,SAAQ;4BAAQ,OAAM;4BAAiB,IAAI;gCAAE,IAAI;4BAAE;sCAAG;;;;;;wBAIjE,YAAY,MAAM,GAAG,mBACpB,6LAAC,uLAAA,CAAA,OAAI;4BAAC,SAAQ;4BAAW,IAAI;gCAAE,IAAI;4BAAE;sCACnC,cAAA,6LAAC,4MAAA,CAAA,cAAW;0CACV,cAAA,6LAAC,uLAAA,CAAA,OAAI;oCAAC,SAAS;oCAAC,SAAS;8CACtB,YAAY,GAAG,CAAC,CAAC,MAAM,sBACtB,6LAAC,uLAAA,CAAA,OAAI;4CAAC,IAAI;4CAAC,IAAI;sDACb,cAAA,6LAAC,uLAAA,CAAA,OAAI;gDACH,OAAO;gDACP,SAAQ;gDACR,IAAI;oDAAE,YAAY;oDAAa,OAAO;gDAAO;;;;;;2CAJ1B;;;;;;;;;;;;;;;;;;;;sCAajC,6LAAC,oLAAA,CAAA,MAAG;4BAAC,IAAI;gCAAE,SAAS;gCAAQ,KAAK;gCAAG,gBAAgB;4BAAgB;;gCACjE,YAAY,MAAM,GAAG,mBACpB,6LAAC,6LAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,yBAAW,6LAAC,gKAAA,CAAA,UAAQ;;;;;oCACpB,SAAS;8CACV;;;;;;8CAIH,6LAAC,6LAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAY,SAAS;8CAAa;;;;;;;;;;;;;;;;;;YAO1D;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM;QAAC,MAAM;QAAM,SAAS;QAAa,UAAS;QAAK,SAAS;;0BAC/D,6LAAC,4MAAA,CAAA,cAAW;0BACV,cAAA,6LAAC,oLAAA,CAAA,MAAG;oBAAC,IAAI;wBAAE,SAAS;wBAAQ,YAAY;wBAAU,KAAK;oBAAE;;sCACvD,6LAAC,gKAAA,CAAA,UAAQ;;;;;wBAAG;;;;;;;;;;;;0BAKhB,6LAAC,kNAAA,CAAA,gBAAa;;kCACZ,6LAAC,gMAAA,CAAA,UAAO;wBAAC,YAAY;wBAAY,IAAI;4BAAE,IAAI;wBAAE;kCAC1C,MAAM,GAAG,CAAC,CAAC,sBACV,6LAAC,uLAAA,CAAA,OAAI;0CACH,cAAA,6LAAC,sMAAA,CAAA,YAAS;8CAAE;;;;;;+BADH;;;;;;;;;;oBAMd,uBACC,6LAAC,0LAAA,CAAA,QAAK;wBAAC,UAAS;wBAAQ,IAAI;4BAAE,IAAI;wBAAE;kCACjC;;;;;;oBAIJ;;;;;;;;;;;;;AAIT;GAvVM;KAAA;uCAyVS", "debugId": null}}, {"offset": {"line": 1080, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/components/MFA/DeviceManagement.jsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState, useEffect } from 'react'\r\nimport {\r\n  Card,\r\n  CardContent,\r\n  CardHeader,\r\n  Typography,\r\n  Box,\r\n  Button,\r\n  List,\r\n  ListItem,\r\n  ListItemText,\r\n  ListItemSecondaryAction,\r\n  IconButton,\r\n  Chip,\r\n  Dialog,\r\n  DialogTitle,\r\n  DialogContent,\r\n  DialogActions,\r\n  TextField,\r\n  Alert,\r\n  Divider,\r\n  Grid,\r\n  Tooltip\r\n} from '@mui/material'\r\nimport {\r\n  Smartphone,\r\n  Edit,\r\n  Delete,\r\n  Add,\r\n  AccessTime,\r\n  CalendarToday,\r\n  Computer,\r\n  PhoneAndroid,\r\n  Tablet\r\n} from '@mui/icons-material'\r\nimport { getMFADevices, updateDeviceName, removeDevice } from '@/services/mfaApi'\r\nimport MFASetup from './MFASetup'\r\n\r\nconst DeviceManagement = ({ userId, onDeviceChange }) => {\r\n  const [devices, setDevices] = useState([])\r\n  const [loading, setLoading] = useState(true)\r\n  const [error, setError] = useState('')\r\n  const [editDialogOpen, setEditDialogOpen] = useState(false)\r\n  const [removeDialogOpen, setRemoveDialogOpen] = useState(false)\r\n  const [addDeviceOpen, setAddDeviceOpen] = useState(false)\r\n  const [selectedDevice, setSelectedDevice] = useState(null)\r\n  const [newDeviceName, setNewDeviceName] = useState('')\r\n  const [maxDevices, setMaxDevices] = useState(5)\r\n\r\n  useEffect(() => {\r\n    loadDevices()\r\n  }, [userId])\r\n\r\n  const loadDevices = async () => {\r\n    try {\r\n      setLoading(true)\r\n      setError('')\r\n      \r\n      const result = await getMFADevices(userId)\r\n      setDevices(result.data.devices)\r\n      setMaxDevices(result.data.maxDevices)\r\n    } catch (err) {\r\n      setError(err.message)\r\n    } finally {\r\n      setLoading(false)\r\n    }\r\n  }\r\n\r\n  const handleEditDevice = (device) => {\r\n    setSelectedDevice(device)\r\n    setNewDeviceName(device.deviceName)\r\n    setEditDialogOpen(true)\r\n  }\r\n\r\n  const handleUpdateDeviceName = async () => {\r\n    if (!newDeviceName.trim()) {\r\n      setError('Device name cannot be empty')\r\n      return\r\n    }\r\n\r\n    try {\r\n      setLoading(true)\r\n      setError('')\r\n      \r\n      await updateDeviceName(userId, selectedDevice.deviceId, newDeviceName.trim())\r\n      await loadDevices()\r\n      setEditDialogOpen(false)\r\n      setSelectedDevice(null)\r\n      setNewDeviceName('')\r\n      \r\n      if (onDeviceChange) onDeviceChange()\r\n    } catch (err) {\r\n      setError(err.message)\r\n    } finally {\r\n      setLoading(false)\r\n    }\r\n  }\r\n\r\n  const handleRemoveDevice = (device) => {\r\n    setSelectedDevice(device)\r\n    setRemoveDialogOpen(true)\r\n  }\r\n\r\n  const confirmRemoveDevice = async () => {\r\n    try {\r\n      setLoading(true)\r\n      setError('')\r\n      \r\n      await removeDevice(userId, selectedDevice.deviceId)\r\n      await loadDevices()\r\n      setRemoveDialogOpen(false)\r\n      setSelectedDevice(null)\r\n      \r\n      if (onDeviceChange) onDeviceChange()\r\n    } catch (err) {\r\n      setError(err.message)\r\n    } finally {\r\n      setLoading(false)\r\n    }\r\n  }\r\n\r\n  const getDeviceIcon = (deviceInfo) => {\r\n    const platform = deviceInfo?.platform?.toLowerCase() || ''\r\n    const userAgent = deviceInfo?.userAgent?.toLowerCase() || ''\r\n    \r\n    if (platform.includes('android') || userAgent.includes('android')) {\r\n      return <PhoneAndroid />\r\n    } else if (platform.includes('ios') || userAgent.includes('iphone') || userAgent.includes('ipad')) {\r\n      return userAgent.includes('ipad') ? <Tablet /> : <PhoneAndroid />\r\n    } else if (platform.includes('windows') || platform.includes('mac') || platform.includes('linux')) {\r\n      return <Computer />\r\n    }\r\n    return <Smartphone />\r\n  }\r\n\r\n  const formatDate = (dateString) => {\r\n    if (!dateString) return 'Never'\r\n    return new Date(dateString).toLocaleDateString()\r\n  }\r\n\r\n  const formatDateTime = (dateString) => {\r\n    if (!dateString) return 'Never'\r\n    return new Date(dateString).toLocaleString()\r\n  }\r\n\r\n  if (loading && devices.length === 0) {\r\n    return (\r\n      <Card>\r\n        <CardContent>\r\n          <Typography>Loading devices...</Typography>\r\n        </CardContent>\r\n      </Card>\r\n    )\r\n  }\r\n\r\n  return (\r\n    <>\r\n      <Card>\r\n        <CardHeader\r\n          avatar={<Smartphone sx={{ color: 'primary.main' }} />}\r\n          title=\"MFA Devices\"\r\n          subheader={`${devices.length} of ${maxDevices} devices registered`}\r\n          action={\r\n            devices.length < maxDevices && (\r\n              <Button\r\n                variant=\"outlined\"\r\n                startIcon={<Add />}\r\n                onClick={() => setAddDeviceOpen(true)}\r\n                disabled={loading}\r\n              >\r\n                Add Device\r\n              </Button>\r\n            )\r\n          }\r\n        />\r\n        <CardContent>\r\n          {error && (\r\n            <Alert severity=\"error\" sx={{ mb: 2 }}>\r\n              {error}\r\n            </Alert>\r\n          )}\r\n\r\n          {devices.length === 0 ? (\r\n            <Box sx={{ textAlign: 'center', py: 4 }}>\r\n              <Smartphone sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />\r\n              <Typography variant=\"h6\" color=\"text.secondary\" gutterBottom>\r\n                No MFA devices registered\r\n              </Typography>\r\n              <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 3 }}>\r\n                Add your first authenticator device to enable MFA\r\n              </Typography>\r\n              <Button\r\n                variant=\"contained\"\r\n                startIcon={<Add />}\r\n                onClick={() => setAddDeviceOpen(true)}\r\n              >\r\n                Add First Device\r\n              </Button>\r\n            </Box>\r\n          ) : (\r\n            <List>\r\n              {devices.map((device, index) => (\r\n                <Box key={device.deviceId}>\r\n                  <ListItem>\r\n                    <Box sx={{ display: 'flex', alignItems: 'center', mr: 2 }}>\r\n                      {getDeviceIcon(device.deviceInfo)}\r\n                    </Box>\r\n                    <ListItemText\r\n                      primary={\r\n                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\r\n                          <Typography variant=\"subtitle1\">\r\n                            {device.deviceName}\r\n                          </Typography>\r\n                          <Chip \r\n                            label=\"Active\" \r\n                            color=\"success\" \r\n                            size=\"small\" \r\n                            variant=\"outlined\"\r\n                          />\r\n                        </Box>\r\n                      }\r\n                      secondary={\r\n                        <Grid container spacing={2} sx={{ mt: 0.5 }}>\r\n                          <Grid item xs={12} sm={6}>\r\n                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>\r\n                              <CalendarToday sx={{ fontSize: 16, color: 'text.secondary' }} />\r\n                              <Typography variant=\"body2\" color=\"text.secondary\">\r\n                                Added: {formatDate(device.registeredAt)}\r\n                              </Typography>\r\n                            </Box>\r\n                          </Grid>\r\n                          <Grid item xs={12} sm={6}>\r\n                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>\r\n                              <AccessTime sx={{ fontSize: 16, color: 'text.secondary' }} />\r\n                              <Typography variant=\"body2\" color=\"text.secondary\">\r\n                                Last used: {formatDateTime(device.lastUsedAt)}\r\n                              </Typography>\r\n                            </Box>\r\n                          </Grid>\r\n                        </Grid>\r\n                      }\r\n                    />\r\n                    <ListItemSecondaryAction>\r\n                      <Box sx={{ display: 'flex', gap: 1 }}>\r\n                        <Tooltip title=\"Edit device name\">\r\n                          <IconButton\r\n                            edge=\"end\"\r\n                            onClick={() => handleEditDevice(device)}\r\n                            disabled={loading}\r\n                          >\r\n                            <Edit />\r\n                          </IconButton>\r\n                        </Tooltip>\r\n                        <Tooltip title=\"Remove device\">\r\n                          <IconButton\r\n                            edge=\"end\"\r\n                            onClick={() => handleRemoveDevice(device)}\r\n                            disabled={loading || devices.length === 1}\r\n                            color=\"error\"\r\n                          >\r\n                            <Delete />\r\n                          </IconButton>\r\n                        </Tooltip>\r\n                      </Box>\r\n                    </ListItemSecondaryAction>\r\n                  </ListItem>\r\n                  {index < devices.length - 1 && <Divider />}\r\n                </Box>\r\n              ))}\r\n            </List>\r\n          )}\r\n\r\n          {devices.length >= maxDevices && (\r\n            <Alert severity=\"info\" sx={{ mt: 2 }}>\r\n              You have reached the maximum number of devices ({maxDevices}). \r\n              Remove a device to add a new one.\r\n            </Alert>\r\n          )}\r\n        </CardContent>\r\n      </Card>\r\n\r\n      {/* Edit Device Name Dialog */}\r\n      <Dialog open={editDialogOpen} onClose={() => setEditDialogOpen(false)} maxWidth=\"sm\" fullWidth>\r\n        <DialogTitle>Edit Device Name</DialogTitle>\r\n        <DialogContent>\r\n          <TextField\r\n            fullWidth\r\n            label=\"Device Name\"\r\n            value={newDeviceName}\r\n            onChange={(e) => setNewDeviceName(e.target.value)}\r\n            sx={{ mt: 2 }}\r\n            placeholder=\"e.g., iPhone, Work Phone, Personal Tablet\"\r\n          />\r\n        </DialogContent>\r\n        <DialogActions>\r\n          <Button onClick={() => setEditDialogOpen(false)}>\r\n            Cancel\r\n          </Button>\r\n          <Button \r\n            variant=\"contained\"\r\n            onClick={handleUpdateDeviceName}\r\n            disabled={loading || !newDeviceName.trim()}\r\n          >\r\n            {loading ? 'Updating...' : 'Update'}\r\n          </Button>\r\n        </DialogActions>\r\n      </Dialog>\r\n\r\n      {/* Remove Device Dialog */}\r\n      <Dialog open={removeDialogOpen} onClose={() => setRemoveDialogOpen(false)} maxWidth=\"sm\" fullWidth>\r\n        <DialogTitle>Remove Device</DialogTitle>\r\n        <DialogContent>\r\n          <Alert severity=\"warning\" sx={{ mb: 2 }}>\r\n            Are you sure you want to remove \"{selectedDevice?.deviceName}\"? \r\n            This action cannot be undone.\r\n          </Alert>\r\n          <Typography variant=\"body2\" color=\"text.secondary\">\r\n            You will no longer be able to use this device for MFA verification. \r\n            Make sure you have access to other devices or backup codes.\r\n          </Typography>\r\n        </DialogContent>\r\n        <DialogActions>\r\n          <Button onClick={() => setRemoveDialogOpen(false)}>\r\n            Cancel\r\n          </Button>\r\n          <Button \r\n            color=\"error\" \r\n            variant=\"contained\"\r\n            onClick={confirmRemoveDevice}\r\n            disabled={loading}\r\n          >\r\n            {loading ? 'Removing...' : 'Remove Device'}\r\n          </Button>\r\n        </DialogActions>\r\n      </Dialog>\r\n\r\n      {/* Add Device Dialog */}\r\n      <MFASetup\r\n        open={addDeviceOpen}\r\n        onClose={() => setAddDeviceOpen(false)}\r\n        userId={userId}\r\n        onSuccess={(message) => {\r\n          alert(message)\r\n          setAddDeviceOpen(false)\r\n          loadDevices()\r\n          if (onDeviceChange) onDeviceChange()\r\n        }}\r\n        isAddingDevice={true}\r\n      />\r\n    </>\r\n  )\r\n}\r\n\r\nexport default DeviceManagement\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAuBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AACA;;;AAtCA;;;;;;;;;;;;;;AAwCA,MAAM,mBAAmB,CAAC,EAAE,MAAM,EAAE,cAAc,EAAE;;IAClD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACzC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR;QACF;qCAAG;QAAC;KAAO;IAEX,MAAM,cAAc;QAClB,IAAI;YACF,WAAW;YACX,SAAS;YAET,MAAM,SAAS,MAAM,CAAA,GAAA,4HAAA,CAAA,gBAAa,AAAD,EAAE;YACnC,WAAW,OAAO,IAAI,CAAC,OAAO;YAC9B,cAAc,OAAO,IAAI,CAAC,UAAU;QACtC,EAAE,OAAO,KAAK;YACZ,SAAS,IAAI,OAAO;QACtB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,kBAAkB;QAClB,iBAAiB,OAAO,UAAU;QAClC,kBAAkB;IACpB;IAEA,MAAM,yBAAyB;QAC7B,IAAI,CAAC,cAAc,IAAI,IAAI;YACzB,SAAS;YACT;QACF;QAEA,IAAI;YACF,WAAW;YACX,SAAS;YAET,MAAM,CAAA,GAAA,4HAAA,CAAA,mBAAgB,AAAD,EAAE,QAAQ,eAAe,QAAQ,EAAE,cAAc,IAAI;YAC1E,MAAM;YACN,kBAAkB;YAClB,kBAAkB;YAClB,iBAAiB;YAEjB,IAAI,gBAAgB;QACtB,EAAE,OAAO,KAAK;YACZ,SAAS,IAAI,OAAO;QACtB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,kBAAkB;QAClB,oBAAoB;IACtB;IAEA,MAAM,sBAAsB;QAC1B,IAAI;YACF,WAAW;YACX,SAAS;YAET,MAAM,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD,EAAE,QAAQ,eAAe,QAAQ;YAClD,MAAM;YACN,oBAAoB;YACpB,kBAAkB;YAElB,IAAI,gBAAgB;QACtB,EAAE,OAAO,KAAK;YACZ,SAAS,IAAI,OAAO;QACtB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,MAAM,WAAW,YAAY,UAAU,iBAAiB;QACxD,MAAM,YAAY,YAAY,WAAW,iBAAiB;QAE1D,IAAI,SAAS,QAAQ,CAAC,cAAc,UAAU,QAAQ,CAAC,YAAY;YACjE,qBAAO,6LAAC,oKAAA,CAAA,UAAY;;;;;QACtB,OAAO,IAAI,SAAS,QAAQ,CAAC,UAAU,UAAU,QAAQ,CAAC,aAAa,UAAU,QAAQ,CAAC,SAAS;YACjG,OAAO,UAAU,QAAQ,CAAC,wBAAU,6LAAC,8JAAA,CAAA,UAAM;;;;qCAAM,6LAAC,oKAAA,CAAA,UAAY;;;;;QAChE,OAAO,IAAI,SAAS,QAAQ,CAAC,cAAc,SAAS,QAAQ,CAAC,UAAU,SAAS,QAAQ,CAAC,UAAU;YACjG,qBAAO,6LAAC,gKAAA,CAAA,UAAQ;;;;;QAClB;QACA,qBAAO,6LAAC,kKAAA,CAAA,UAAU;;;;;IACpB;IAEA,MAAM,aAAa,CAAC;QAClB,IAAI,CAAC,YAAY,OAAO;QACxB,OAAO,IAAI,KAAK,YAAY,kBAAkB;IAChD;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,CAAC,YAAY,OAAO;QACxB,OAAO,IAAI,KAAK,YAAY,cAAc;IAC5C;IAEA,IAAI,WAAW,QAAQ,MAAM,KAAK,GAAG;QACnC,qBACE,6LAAC,uLAAA,CAAA,OAAI;sBACH,cAAA,6LAAC,4MAAA,CAAA,cAAW;0BACV,cAAA,6LAAC,yMAAA,CAAA,aAAU;8BAAC;;;;;;;;;;;;;;;;IAIpB;IAEA,qBACE;;0BACE,6LAAC,uLAAA,CAAA,OAAI;;kCACH,6LAAC,yMAAA,CAAA,aAAU;wBACT,sBAAQ,6LAAC,kKAAA,CAAA,UAAU;4BAAC,IAAI;gCAAE,OAAO;4BAAe;;;;;;wBAChD,OAAM;wBACN,WAAW,GAAG,QAAQ,MAAM,CAAC,IAAI,EAAE,WAAW,mBAAmB,CAAC;wBAClE,QACE,QAAQ,MAAM,GAAG,4BACf,6LAAC,6LAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,yBAAW,6LAAC,2JAAA,CAAA,UAAG;;;;;4BACf,SAAS,IAAM,iBAAiB;4BAChC,UAAU;sCACX;;;;;;;;;;;kCAMP,6LAAC,4MAAA,CAAA,cAAW;;4BACT,uBACC,6LAAC,0LAAA,CAAA,QAAK;gCAAC,UAAS;gCAAQ,IAAI;oCAAE,IAAI;gCAAE;0CACjC;;;;;;4BAIJ,QAAQ,MAAM,KAAK,kBAClB,6LAAC,oLAAA,CAAA,MAAG;gCAAC,IAAI;oCAAE,WAAW;oCAAU,IAAI;gCAAE;;kDACpC,6LAAC,kKAAA,CAAA,UAAU;wCAAC,IAAI;4CAAE,UAAU;4CAAI,OAAO;4CAAkB,IAAI;wCAAE;;;;;;kDAC/D,6LAAC,yMAAA,CAAA,aAAU;wCAAC,SAAQ;wCAAK,OAAM;wCAAiB,YAAY;kDAAC;;;;;;kDAG7D,6LAAC,yMAAA,CAAA,aAAU;wCAAC,SAAQ;wCAAQ,OAAM;wCAAiB,IAAI;4CAAE,IAAI;wCAAE;kDAAG;;;;;;kDAGlE,6LAAC,6LAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,yBAAW,6LAAC,2JAAA,CAAA,UAAG;;;;;wCACf,SAAS,IAAM,iBAAiB;kDACjC;;;;;;;;;;;qDAKH,6LAAC,uLAAA,CAAA,OAAI;0CACF,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,6LAAC,oLAAA,CAAA,MAAG;;0DACF,6LAAC,mMAAA,CAAA,WAAQ;;kEACP,6LAAC,oLAAA,CAAA,MAAG;wDAAC,IAAI;4DAAE,SAAS;4DAAQ,YAAY;4DAAU,IAAI;wDAAE;kEACrD,cAAc,OAAO,UAAU;;;;;;kEAElC,6LAAC,+MAAA,CAAA,eAAY;wDACX,uBACE,6LAAC,oLAAA,CAAA,MAAG;4DAAC,IAAI;gEAAE,SAAS;gEAAQ,YAAY;gEAAU,KAAK;4DAAE;;8EACvD,6LAAC,yMAAA,CAAA,aAAU;oEAAC,SAAQ;8EACjB,OAAO,UAAU;;;;;;8EAEpB,6LAAC,uLAAA,CAAA,OAAI;oEACH,OAAM;oEACN,OAAM;oEACN,MAAK;oEACL,SAAQ;;;;;;;;;;;;wDAId,yBACE,6LAAC,uLAAA,CAAA,OAAI;4DAAC,SAAS;4DAAC,SAAS;4DAAG,IAAI;gEAAE,IAAI;4DAAI;;8EACxC,6LAAC,uLAAA,CAAA,OAAI;oEAAC,IAAI;oEAAC,IAAI;oEAAI,IAAI;8EACrB,cAAA,6LAAC,oLAAA,CAAA,MAAG;wEAAC,IAAI;4EAAE,SAAS;4EAAQ,YAAY;4EAAU,KAAK;wEAAI;;0FACzD,6LAAC,qKAAA,CAAA,UAAa;gFAAC,IAAI;oFAAE,UAAU;oFAAI,OAAO;gFAAiB;;;;;;0FAC3D,6LAAC,yMAAA,CAAA,aAAU;gFAAC,SAAQ;gFAAQ,OAAM;;oFAAiB;oFACzC,WAAW,OAAO,YAAY;;;;;;;;;;;;;;;;;;8EAI5C,6LAAC,uLAAA,CAAA,OAAI;oEAAC,IAAI;oEAAC,IAAI;oEAAI,IAAI;8EACrB,cAAA,6LAAC,oLAAA,CAAA,MAAG;wEAAC,IAAI;4EAAE,SAAS;4EAAQ,YAAY;4EAAU,KAAK;wEAAI;;0FACzD,6LAAC,kKAAA,CAAA,UAAU;gFAAC,IAAI;oFAAE,UAAU;oFAAI,OAAO;gFAAiB;;;;;;0FACxD,6LAAC,yMAAA,CAAA,aAAU;gFAAC,SAAQ;gFAAQ,OAAM;;oFAAiB;oFACrC,eAAe,OAAO,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kEAOxD,6LAAC,gPAAA,CAAA,0BAAuB;kEACtB,cAAA,6LAAC,oLAAA,CAAA,MAAG;4DAAC,IAAI;gEAAE,SAAS;gEAAQ,KAAK;4DAAE;;8EACjC,6LAAC,gMAAA,CAAA,UAAO;oEAAC,OAAM;8EACb,cAAA,6LAAC,yMAAA,CAAA,aAAU;wEACT,MAAK;wEACL,SAAS,IAAM,iBAAiB;wEAChC,UAAU;kFAEV,cAAA,6LAAC,4JAAA,CAAA,UAAI;;;;;;;;;;;;;;;8EAGT,6LAAC,gMAAA,CAAA,UAAO;oEAAC,OAAM;8EACb,cAAA,6LAAC,yMAAA,CAAA,aAAU;wEACT,MAAK;wEACL,SAAS,IAAM,mBAAmB;wEAClC,UAAU,WAAW,QAAQ,MAAM,KAAK;wEACxC,OAAM;kFAEN,cAAA,6LAAC,8JAAA,CAAA,UAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4CAMhB,QAAQ,QAAQ,MAAM,GAAG,mBAAK,6LAAC,gMAAA,CAAA,UAAO;;;;;;uCAhE/B,OAAO,QAAQ;;;;;;;;;;4BAsE9B,QAAQ,MAAM,IAAI,4BACjB,6LAAC,0LAAA,CAAA,QAAK;gCAAC,UAAS;gCAAO,IAAI;oCAAE,IAAI;gCAAE;;oCAAG;oCACa;oCAAW;;;;;;;;;;;;;;;;;;;0BAQpE,6LAAC,6LAAA,CAAA,SAAM;gBAAC,MAAM;gBAAgB,SAAS,IAAM,kBAAkB;gBAAQ,UAAS;gBAAK,SAAS;;kCAC5F,6LAAC,4MAAA,CAAA,cAAW;kCAAC;;;;;;kCACb,6LAAC,kNAAA,CAAA,gBAAa;kCACZ,cAAA,6LAAC,sMAAA,CAAA,YAAS;4BACR,SAAS;4BACT,OAAM;4BACN,OAAO;4BACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;4BAChD,IAAI;gCAAE,IAAI;4BAAE;4BACZ,aAAY;;;;;;;;;;;kCAGhB,6LAAC,kNAAA,CAAA,gBAAa;;0CACZ,6LAAC,6LAAA,CAAA,SAAM;gCAAC,SAAS,IAAM,kBAAkB;0CAAQ;;;;;;0CAGjD,6LAAC,6LAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,SAAS;gCACT,UAAU,WAAW,CAAC,cAAc,IAAI;0CAEvC,UAAU,gBAAgB;;;;;;;;;;;;;;;;;;0BAMjC,6LAAC,6LAAA,CAAA,SAAM;gBAAC,MAAM;gBAAkB,SAAS,IAAM,oBAAoB;gBAAQ,UAAS;gBAAK,SAAS;;kCAChG,6LAAC,4MAAA,CAAA,cAAW;kCAAC;;;;;;kCACb,6LAAC,kNAAA,CAAA,gBAAa;;0CACZ,6LAAC,0LAAA,CAAA,QAAK;gCAAC,UAAS;gCAAU,IAAI;oCAAE,IAAI;gCAAE;;oCAAG;oCACL,gBAAgB;oCAAW;;;;;;;0CAG/D,6LAAC,yMAAA,CAAA,aAAU;gCAAC,SAAQ;gCAAQ,OAAM;0CAAiB;;;;;;;;;;;;kCAKrD,6LAAC,kNAAA,CAAA,gBAAa;;0CACZ,6LAAC,6LAAA,CAAA,SAAM;gCAAC,SAAS,IAAM,oBAAoB;0CAAQ;;;;;;0CAGnD,6LAAC,6LAAA,CAAA,SAAM;gCACL,OAAM;gCACN,SAAQ;gCACR,SAAS;gCACT,UAAU;0CAET,UAAU,gBAAgB;;;;;;;;;;;;;;;;;;0BAMjC,6LAAC,wIAAA,CAAA,UAAQ;gBACP,MAAM;gBACN,SAAS,IAAM,iBAAiB;gBAChC,QAAQ;gBACR,WAAW,CAAC;oBACV,MAAM;oBACN,iBAAiB;oBACjB;oBACA,IAAI,gBAAgB;gBACtB;gBACA,gBAAgB;;;;;;;;AAIxB;GAzTM;KAAA;uCA2TS", "debugId": null}}, {"offset": {"line": 1817, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/components/MFA/MFASettings.jsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState, useEffect } from 'react'\r\nimport {\r\n  Card,\r\n  CardContent,\r\n  CardHeader,\r\n  Typography,\r\n  Box,\r\n  Button,\r\n  Switch,\r\n  FormControlLabel,\r\n  Alert,\r\n  Chip,\r\n  Dialog,\r\n  DialogTitle,\r\n  DialogContent,\r\n  DialogActions,\r\n  TextField,\r\n  Grid,\r\n  Divider\r\n} from '@mui/material'\r\nimport { Security, Shield, Key, Download, Warning } from '@mui/icons-material'\r\nimport { getMFAStatus, disableMFA, generateBackupCodes } from '@/services/mfaApi'\r\nimport MFASetup from './MFASetup'\r\nimport DeviceManagement from './DeviceManagement'\r\n\r\nconst MFASettings = ({ userId, userEmail }) => {\r\n  const [mfaStatus, setMfaStatus] = useState(null)\r\n  const [loading, setLoading] = useState(true)\r\n  const [error, setError] = useState('')\r\n  const [setupOpen, setSetupOpen] = useState(false)\r\n  const [disableDialogOpen, setDisableDialogOpen] = useState(false)\r\n  const [password, setPassword] = useState('')\r\n  const [backupCodes, setBackupCodes] = useState([])\r\n  const [backupCodesDialogOpen, setBackupCodesDialogOpen] = useState(false)\r\n\r\n  useEffect(() => {\r\n    loadMFAStatus()\r\n  }, [userId])\r\n\r\n  const loadMFAStatus = async () => {\r\n    try {\r\n      setLoading(true)\r\n      setError('')\r\n\r\n      const result = await getMFAStatus(userId)\r\n      setMfaStatus(result.data)\r\n    } catch (err) {\r\n      setError(err.message)\r\n    } finally {\r\n      setLoading(false)\r\n    }\r\n  }\r\n\r\n  const handleDisableMFA = async () => {\r\n    if (!password) {\r\n      setError('Password is required to disable MFA')\r\n      return\r\n    }\r\n\r\n    try {\r\n      setLoading(true)\r\n      setError('')\r\n\r\n      await disableMFA(userId, password)\r\n      setMfaStatus({ ...mfaStatus, mfaEnabled: false })\r\n      setDisableDialogOpen(false)\r\n      setPassword('')\r\n\r\n      // Show success message\r\n      alert('MFA has been disabled successfully')\r\n    } catch (err) {\r\n      setError(err.message)\r\n    } finally {\r\n      setLoading(false)\r\n    }\r\n  }\r\n\r\n  const handleGenerateBackupCodes = async () => {\r\n    try {\r\n      setLoading(true)\r\n      setError('')\r\n\r\n      const result = await generateBackupCodes(userId)\r\n      setBackupCodes(result.data.backupCodes)\r\n      setBackupCodesDialogOpen(true)\r\n\r\n      // Update backup codes count\r\n      setMfaStatus({ ...mfaStatus, backupCodesCount: result.data.backupCodes.length })\r\n    } catch (err) {\r\n      setError(err.message)\r\n    } finally {\r\n      setLoading(false)\r\n    }\r\n  }\r\n\r\n  const downloadBackupCodes = () => {\r\n    const content = `CAM Transport - MFA Backup Codes\\n\\nGenerated: ${new Date().toLocaleString()}\\nUser: ${userEmail}\\n\\nBackup Codes:\\n${backupCodes.join('\\n')}\\n\\nImportant:\\n- Keep these codes safe and secure\\n- Each code can only be used once\\n- Use these codes if you lose access to your authenticator app\\n- Generate new codes if you suspect these have been compromised`\r\n\r\n    const blob = new Blob([content], { type: 'text/plain' })\r\n    const url = URL.createObjectURL(blob)\r\n    const a = document.createElement('a')\r\n    a.href = url\r\n    a.download = 'cam-transport-backup-codes.txt'\r\n    document.body.appendChild(a)\r\n    a.click()\r\n    document.body.removeChild(a)\r\n    URL.revokeObjectURL(url)\r\n  }\r\n\r\n  if (loading && !mfaStatus) {\r\n    return (\r\n      <Card>\r\n        <CardContent>\r\n          <Typography>Loading MFA settings...</Typography>\r\n        </CardContent>\r\n      </Card>\r\n    )\r\n  }\r\n\r\n  return (\r\n    <>\r\n      <Card>\r\n        <CardHeader\r\n          avatar={<Security sx={{ color: 'primary.main' }} />}\r\n          title=\"Two-Factor Authentication\"\r\n          subheader=\"Add an extra layer of security to your account\"\r\n        />\r\n        <CardContent>\r\n          {error && (\r\n            <Alert severity=\"error\" sx={{ mb: 2 }}>\r\n              {error}\r\n            </Alert>\r\n          )}\r\n\r\n          <Box sx={{ mb: 3 }}>\r\n            <FormControlLabel\r\n              control={\r\n                <Switch\r\n                  checked={mfaStatus?.mfaEnabled || false}\r\n                  disabled={loading}\r\n                />\r\n              }\r\n              label={\r\n                <Box>\r\n                  <Typography variant=\"body1\">\r\n                    Two-Factor Authentication\r\n                  </Typography>\r\n                  <Typography variant=\"body2\" color=\"text.secondary\">\r\n                    {mfaStatus?.mfaEnabled\r\n                      ? 'Your account is protected with 2FA'\r\n                      : 'Secure your account with an authenticator app'\r\n                    }\r\n                  </Typography>\r\n                </Box>\r\n              }\r\n            />\r\n          </Box>\r\n\r\n          {mfaStatus?.mfaEnabled ? (\r\n            <Box>\r\n              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>\r\n                <Shield sx={{ color: 'success.main' }} />\r\n                <Typography variant=\"h6\" color=\"success.main\">\r\n                  MFA is Enabled\r\n                </Typography>\r\n              </Box>\r\n\r\n              <Grid container spacing={2} sx={{ mb: 3 }}>\r\n                <Grid item xs={12} sm={6}>\r\n                  <Typography variant=\"body2\" color=\"text.secondary\">\r\n                    Enabled on:\r\n                  </Typography>\r\n                  <Typography variant=\"body1\">\r\n                    {mfaStatus.mfaSetupAt ? new Date(mfaStatus.mfaSetupAt).toLocaleDateString() : 'Unknown'}\r\n                  </Typography>\r\n                </Grid>\r\n                <Grid item xs={12} sm={6}>\r\n                  <Typography variant=\"body2\" color=\"text.secondary\">\r\n                    Last used:\r\n                  </Typography>\r\n                  <Typography variant=\"body1\">\r\n                    {mfaStatus.lastMfaUsed ? new Date(mfaStatus.lastMfaUsed).toLocaleDateString() : 'Never'}\r\n                  </Typography>\r\n                </Grid>\r\n                <Grid item xs={12} sm={6}>\r\n                  <Typography variant=\"body2\" color=\"text.secondary\">\r\n                    Backup codes remaining:\r\n                  </Typography>\r\n                  <Chip\r\n                    label={`${mfaStatus.backupCodesCount || 0} codes`}\r\n                    color={mfaStatus.backupCodesCount > 3 ? 'success' : 'warning'}\r\n                    size=\"small\"\r\n                  />\r\n                </Grid>\r\n              </Grid>\r\n\r\n              <Divider sx={{ my: 2 }} />\r\n\r\n              <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>\r\n                <Button\r\n                  variant=\"outlined\"\r\n                  startIcon={<Key />}\r\n                  onClick={handleGenerateBackupCodes}\r\n                  disabled={loading}\r\n                >\r\n                  Generate New Backup Codes\r\n                </Button>\r\n                <Button\r\n                  variant=\"outlined\"\r\n                  color=\"error\"\r\n                  startIcon={<Warning />}\r\n                  onClick={() => setDisableDialogOpen(true)}\r\n                  disabled={loading}\r\n                >\r\n                  Disable MFA\r\n                </Button>\r\n              </Box>\r\n            </Box>\r\n          ) : (\r\n            <Box>\r\n              <Alert severity=\"info\" sx={{ mb: 2 }}>\r\n                Two-factor authentication is not enabled. Enable it now to secure your account.\r\n              </Alert>\r\n\r\n              <Button\r\n                variant=\"contained\"\r\n                startIcon={<Security />}\r\n                onClick={() => setSetupOpen(true)}\r\n                disabled={loading}\r\n                size=\"large\"\r\n              >\r\n                Enable Two-Factor Authentication\r\n              </Button>\r\n            </Box>\r\n          )}\r\n        </CardContent>\r\n      </Card>\r\n\r\n      {/* Device Management - Show only if MFA is enabled */}\r\n      {mfaStatus?.mfaEnabled && (\r\n        <Box sx={{ mt: 3 }}>\r\n          <DeviceManagement\r\n            userId={userId}\r\n            onDeviceChange={loadMFAStatus}\r\n          />\r\n        </Box>\r\n      )}\r\n\r\n      {/* MFA Setup Dialog */}\r\n      <MFASetup\r\n        open={setupOpen}\r\n        onClose={() => setSetupOpen(false)}\r\n        userId={userId}\r\n        onSuccess={(message) => {\r\n          alert(message)\r\n          loadMFAStatus()\r\n        }}\r\n      />\r\n\r\n      {/* Disable MFA Dialog */}\r\n      <Dialog open={disableDialogOpen} onClose={() => setDisableDialogOpen(false)} maxWidth=\"sm\" fullWidth>\r\n        <DialogTitle>\r\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\r\n            <Warning color=\"error\" />\r\n            Disable Two-Factor Authentication\r\n          </Box>\r\n        </DialogTitle>\r\n        <DialogContent>\r\n          <Alert severity=\"warning\" sx={{ mb: 2 }}>\r\n            Disabling two-factor authentication will make your account less secure. Are you sure you want to continue?\r\n          </Alert>\r\n          <TextField\r\n            fullWidth\r\n            type=\"password\"\r\n            label=\"Enter your password to confirm\"\r\n            value={password}\r\n            onChange={(e) => setPassword(e.target.value)}\r\n            sx={{ mt: 2 }}\r\n          />\r\n        </DialogContent>\r\n        <DialogActions>\r\n          <Button onClick={() => setDisableDialogOpen(false)}>\r\n            Cancel\r\n          </Button>\r\n          <Button\r\n            color=\"error\"\r\n            variant=\"contained\"\r\n            onClick={handleDisableMFA}\r\n            disabled={loading || !password}\r\n          >\r\n            {loading ? 'Disabling...' : 'Disable MFA'}\r\n          </Button>\r\n        </DialogActions>\r\n      </Dialog>\r\n\r\n      {/* Backup Codes Dialog */}\r\n      <Dialog open={backupCodesDialogOpen} onClose={() => setBackupCodesDialogOpen(false)} maxWidth=\"sm\" fullWidth>\r\n        <DialogTitle>\r\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\r\n            <Key />\r\n            New Backup Codes Generated\r\n          </Box>\r\n        </DialogTitle>\r\n        <DialogContent>\r\n          <Alert severity=\"success\" sx={{ mb: 2 }}>\r\n            Your new backup codes have been generated. Save them in a secure location.\r\n          </Alert>\r\n\r\n          <Grid container spacing={1} sx={{ mb: 2 }}>\r\n            {backupCodes.map((code, index) => (\r\n              <Grid item xs={6} key={index}>\r\n                <Chip\r\n                  label={code}\r\n                  variant=\"outlined\"\r\n                  sx={{ fontFamily: 'monospace', width: '100%' }}\r\n                />\r\n              </Grid>\r\n            ))}\r\n          </Grid>\r\n\r\n          <Alert severity=\"warning\">\r\n            These codes replace your previous backup codes. Each code can only be used once.\r\n          </Alert>\r\n        </DialogContent>\r\n        <DialogActions>\r\n          <Button\r\n            startIcon={<Download />}\r\n            onClick={downloadBackupCodes}\r\n          >\r\n            Download Codes\r\n          </Button>\r\n          <Button variant=\"contained\" onClick={() => setBackupCodesDialogOpen(false)}>\r\n            I've Saved Them\r\n          </Button>\r\n        </DialogActions>\r\n      </Dialog>\r\n    </>\r\n  )\r\n}\r\n\r\nexport default MFASettings\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAmBA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;AAzBA;;;;;;;;;;;AA2BA,MAAM,cAAc,CAAC,EAAE,MAAM,EAAE,SAAS,EAAE;;IACxC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACjD,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR;QACF;gCAAG;QAAC;KAAO;IAEX,MAAM,gBAAgB;QACpB,IAAI;YACF,WAAW;YACX,SAAS;YAET,MAAM,SAAS,MAAM,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD,EAAE;YAClC,aAAa,OAAO,IAAI;QAC1B,EAAE,OAAO,KAAK;YACZ,SAAS,IAAI,OAAO;QACtB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,mBAAmB;QACvB,IAAI,CAAC,UAAU;YACb,SAAS;YACT;QACF;QAEA,IAAI;YACF,WAAW;YACX,SAAS;YAET,MAAM,CAAA,GAAA,4HAAA,CAAA,aAAU,AAAD,EAAE,QAAQ;YACzB,aAAa;gBAAE,GAAG,SAAS;gBAAE,YAAY;YAAM;YAC/C,qBAAqB;YACrB,YAAY;YAEZ,uBAAuB;YACvB,MAAM;QACR,EAAE,OAAO,KAAK;YACZ,SAAS,IAAI,OAAO;QACtB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,4BAA4B;QAChC,IAAI;YACF,WAAW;YACX,SAAS;YAET,MAAM,SAAS,MAAM,CAAA,GAAA,4HAAA,CAAA,sBAAmB,AAAD,EAAE;YACzC,eAAe,OAAO,IAAI,CAAC,WAAW;YACtC,yBAAyB;YAEzB,4BAA4B;YAC5B,aAAa;gBAAE,GAAG,SAAS;gBAAE,kBAAkB,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM;YAAC;QAChF,EAAE,OAAO,KAAK;YACZ,SAAS,IAAI,OAAO;QACtB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,sBAAsB;QAC1B,MAAM,UAAU,CAAC,+CAA+C,EAAE,IAAI,OAAO,cAAc,GAAG,QAAQ,EAAE,UAAU,mBAAmB,EAAE,YAAY,IAAI,CAAC,MAAM,sNAAsN,CAAC;QAErX,MAAM,OAAO,IAAI,KAAK;YAAC;SAAQ,EAAE;YAAE,MAAM;QAAa;QACtD,MAAM,MAAM,IAAI,eAAe,CAAC;QAChC,MAAM,IAAI,SAAS,aAAa,CAAC;QACjC,EAAE,IAAI,GAAG;QACT,EAAE,QAAQ,GAAG;QACb,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,EAAE,KAAK;QACP,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,IAAI,eAAe,CAAC;IACtB;IAEA,IAAI,WAAW,CAAC,WAAW;QACzB,qBACE,6LAAC,uLAAA,CAAA,OAAI;sBACH,cAAA,6LAAC,4MAAA,CAAA,cAAW;0BACV,cAAA,6LAAC,yMAAA,CAAA,aAAU;8BAAC;;;;;;;;;;;;;;;;IAIpB;IAEA,qBACE;;0BACE,6LAAC,uLAAA,CAAA,OAAI;;kCACH,6LAAC,yMAAA,CAAA,aAAU;wBACT,sBAAQ,6LAAC,gKAAA,CAAA,UAAQ;4BAAC,IAAI;gCAAE,OAAO;4BAAe;;;;;;wBAC9C,OAAM;wBACN,WAAU;;;;;;kCAEZ,6LAAC,4MAAA,CAAA,cAAW;;4BACT,uBACC,6LAAC,0LAAA,CAAA,QAAK;gCAAC,UAAS;gCAAQ,IAAI;oCAAE,IAAI;gCAAE;0CACjC;;;;;;0CAIL,6LAAC,oLAAA,CAAA,MAAG;gCAAC,IAAI;oCAAE,IAAI;gCAAE;0CACf,cAAA,6LAAC,2NAAA,CAAA,mBAAgB;oCACf,uBACE,6LAAC,6LAAA,CAAA,SAAM;wCACL,SAAS,WAAW,cAAc;wCAClC,UAAU;;;;;;oCAGd,qBACE,6LAAC,oLAAA,CAAA,MAAG;;0DACF,6LAAC,yMAAA,CAAA,aAAU;gDAAC,SAAQ;0DAAQ;;;;;;0DAG5B,6LAAC,yMAAA,CAAA,aAAU;gDAAC,SAAQ;gDAAQ,OAAM;0DAC/B,WAAW,aACR,uCACA;;;;;;;;;;;;;;;;;;;;;;4BAQb,WAAW,2BACV,6LAAC,oLAAA,CAAA,MAAG;;kDACF,6LAAC,oLAAA,CAAA,MAAG;wCAAC,IAAI;4CAAE,SAAS;4CAAQ,YAAY;4CAAU,KAAK;4CAAG,IAAI;wCAAE;;0DAC9D,6LAAC,8JAAA,CAAA,UAAM;gDAAC,IAAI;oDAAE,OAAO;gDAAe;;;;;;0DACpC,6LAAC,yMAAA,CAAA,aAAU;gDAAC,SAAQ;gDAAK,OAAM;0DAAe;;;;;;;;;;;;kDAKhD,6LAAC,uLAAA,CAAA,OAAI;wCAAC,SAAS;wCAAC,SAAS;wCAAG,IAAI;4CAAE,IAAI;wCAAE;;0DACtC,6LAAC,uLAAA,CAAA,OAAI;gDAAC,IAAI;gDAAC,IAAI;gDAAI,IAAI;;kEACrB,6LAAC,yMAAA,CAAA,aAAU;wDAAC,SAAQ;wDAAQ,OAAM;kEAAiB;;;;;;kEAGnD,6LAAC,yMAAA,CAAA,aAAU;wDAAC,SAAQ;kEACjB,UAAU,UAAU,GAAG,IAAI,KAAK,UAAU,UAAU,EAAE,kBAAkB,KAAK;;;;;;;;;;;;0DAGlF,6LAAC,uLAAA,CAAA,OAAI;gDAAC,IAAI;gDAAC,IAAI;gDAAI,IAAI;;kEACrB,6LAAC,yMAAA,CAAA,aAAU;wDAAC,SAAQ;wDAAQ,OAAM;kEAAiB;;;;;;kEAGnD,6LAAC,yMAAA,CAAA,aAAU;wDAAC,SAAQ;kEACjB,UAAU,WAAW,GAAG,IAAI,KAAK,UAAU,WAAW,EAAE,kBAAkB,KAAK;;;;;;;;;;;;0DAGpF,6LAAC,uLAAA,CAAA,OAAI;gDAAC,IAAI;gDAAC,IAAI;gDAAI,IAAI;;kEACrB,6LAAC,yMAAA,CAAA,aAAU;wDAAC,SAAQ;wDAAQ,OAAM;kEAAiB;;;;;;kEAGnD,6LAAC,uLAAA,CAAA,OAAI;wDACH,OAAO,GAAG,UAAU,gBAAgB,IAAI,EAAE,MAAM,CAAC;wDACjD,OAAO,UAAU,gBAAgB,GAAG,IAAI,YAAY;wDACpD,MAAK;;;;;;;;;;;;;;;;;;kDAKX,6LAAC,gMAAA,CAAA,UAAO;wCAAC,IAAI;4CAAE,IAAI;wCAAE;;;;;;kDAErB,6LAAC,oLAAA,CAAA,MAAG;wCAAC,IAAI;4CAAE,SAAS;4CAAQ,KAAK;4CAAG,UAAU;wCAAO;;0DACnD,6LAAC,6LAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,yBAAW,6LAAC,2JAAA,CAAA,UAAG;;;;;gDACf,SAAS;gDACT,UAAU;0DACX;;;;;;0DAGD,6LAAC,6LAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,OAAM;gDACN,yBAAW,6LAAC,+JAAA,CAAA,UAAO;;;;;gDACnB,SAAS,IAAM,qBAAqB;gDACpC,UAAU;0DACX;;;;;;;;;;;;;;;;;qDAML,6LAAC,oLAAA,CAAA,MAAG;;kDACF,6LAAC,0LAAA,CAAA,QAAK;wCAAC,UAAS;wCAAO,IAAI;4CAAE,IAAI;wCAAE;kDAAG;;;;;;kDAItC,6LAAC,6LAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,yBAAW,6LAAC,gKAAA,CAAA,UAAQ;;;;;wCACpB,SAAS,IAAM,aAAa;wCAC5B,UAAU;wCACV,MAAK;kDACN;;;;;;;;;;;;;;;;;;;;;;;;YASR,WAAW,4BACV,6LAAC,oLAAA,CAAA,MAAG;gBAAC,IAAI;oBAAE,IAAI;gBAAE;0BACf,cAAA,6LAAC,gJAAA,CAAA,UAAgB;oBACf,QAAQ;oBACR,gBAAgB;;;;;;;;;;;0BAMtB,6LAAC,wIAAA,CAAA,UAAQ;gBACP,MAAM;gBACN,SAAS,IAAM,aAAa;gBAC5B,QAAQ;gBACR,WAAW,CAAC;oBACV,MAAM;oBACN;gBACF;;;;;;0BAIF,6LAAC,6LAAA,CAAA,SAAM;gBAAC,MAAM;gBAAmB,SAAS,IAAM,qBAAqB;gBAAQ,UAAS;gBAAK,SAAS;;kCAClG,6LAAC,4MAAA,CAAA,cAAW;kCACV,cAAA,6LAAC,oLAAA,CAAA,MAAG;4BAAC,IAAI;gCAAE,SAAS;gCAAQ,YAAY;gCAAU,KAAK;4BAAE;;8CACvD,6LAAC,+JAAA,CAAA,UAAO;oCAAC,OAAM;;;;;;gCAAU;;;;;;;;;;;;kCAI7B,6LAAC,kNAAA,CAAA,gBAAa;;0CACZ,6LAAC,0LAAA,CAAA,QAAK;gCAAC,UAAS;gCAAU,IAAI;oCAAE,IAAI;gCAAE;0CAAG;;;;;;0CAGzC,6LAAC,sMAAA,CAAA,YAAS;gCACR,SAAS;gCACT,MAAK;gCACL,OAAM;gCACN,OAAO;gCACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;gCAC3C,IAAI;oCAAE,IAAI;gCAAE;;;;;;;;;;;;kCAGhB,6LAAC,kNAAA,CAAA,gBAAa;;0CACZ,6LAAC,6LAAA,CAAA,SAAM;gCAAC,SAAS,IAAM,qBAAqB;0CAAQ;;;;;;0CAGpD,6LAAC,6LAAA,CAAA,SAAM;gCACL,OAAM;gCACN,SAAQ;gCACR,SAAS;gCACT,UAAU,WAAW,CAAC;0CAErB,UAAU,iBAAiB;;;;;;;;;;;;;;;;;;0BAMlC,6LAAC,6LAAA,CAAA,SAAM;gBAAC,MAAM;gBAAuB,SAAS,IAAM,yBAAyB;gBAAQ,UAAS;gBAAK,SAAS;;kCAC1G,6LAAC,4MAAA,CAAA,cAAW;kCACV,cAAA,6LAAC,oLAAA,CAAA,MAAG;4BAAC,IAAI;gCAAE,SAAS;gCAAQ,YAAY;gCAAU,KAAK;4BAAE;;8CACvD,6LAAC,2JAAA,CAAA,UAAG;;;;;gCAAG;;;;;;;;;;;;kCAIX,6LAAC,kNAAA,CAAA,gBAAa;;0CACZ,6LAAC,0LAAA,CAAA,QAAK;gCAAC,UAAS;gCAAU,IAAI;oCAAE,IAAI;gCAAE;0CAAG;;;;;;0CAIzC,6LAAC,uLAAA,CAAA,OAAI;gCAAC,SAAS;gCAAC,SAAS;gCAAG,IAAI;oCAAE,IAAI;gCAAE;0CACrC,YAAY,GAAG,CAAC,CAAC,MAAM,sBACtB,6LAAC,uLAAA,CAAA,OAAI;wCAAC,IAAI;wCAAC,IAAI;kDACb,cAAA,6LAAC,uLAAA,CAAA,OAAI;4CACH,OAAO;4CACP,SAAQ;4CACR,IAAI;gDAAE,YAAY;gDAAa,OAAO;4CAAO;;;;;;uCAJ1B;;;;;;;;;;0CAU3B,6LAAC,0LAAA,CAAA,QAAK;gCAAC,UAAS;0CAAU;;;;;;;;;;;;kCAI5B,6LAAC,kNAAA,CAAA,gBAAa;;0CACZ,6LAAC,6LAAA,CAAA,SAAM;gCACL,yBAAW,6LAAC,gKAAA,CAAA,UAAQ;;;;;gCACpB,SAAS;0CACV;;;;;;0CAGD,6LAAC,6LAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAY,SAAS,IAAM,yBAAyB;0CAAQ;;;;;;;;;;;;;;;;;;;;AAOtF;GAzTM;KAAA;uCA2TS", "debugId": null}}, {"offset": {"line": 2557, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/app/%5Blang%5D/%28dashboard%29/%28private%29/apps/admin-security/page.jsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState, useEffect } from 'react'\r\nimport { useSession } from 'next-auth/react'\r\nimport {\r\n  Container,\r\n  Typography,\r\n  Box,\r\n  Grid,\r\n  Card,\r\n  CardContent,\r\n  CardHeader,\r\n  Alert,\r\n  Chip,\r\n  LinearProgress,\r\n  List,\r\n  ListItem,\r\n  ListItemText,\r\n  ListItemIcon,\r\n  Divider,\r\n  Button\r\n} from '@mui/material'\r\nimport {\r\n  Shield,\r\n  Key,\r\n  CheckCircle,\r\n  Warning,\r\n  Error,\r\n  Info,\r\n  AccessTime\r\n} from '@mui/icons-material'\r\nimport { getMFAStatus } from '@/services/mfaApi'\r\nimport MFASettings from '@/components/MFA/MFASettings'\r\n\r\nconst AdminSecurityDashboard = () => {\r\n  const { data: session, status } = useSession()\r\n  const [userId, setUserId] = useState(null)\r\n  const [userEmail, setUserEmail] = useState(null)\r\n  const [mfaStatus, setMfaStatus] = useState(null)\r\n  const [loading, setLoading] = useState(true)\r\n  const [error, setError] = useState('')\r\n\r\n  useEffect(() => {\r\n    // Get real user data from NextAuth session\r\n    if (session?.user) {\r\n      setUserEmail(session.user.email)\r\n      setUserId(session.user.id)\r\n    }\r\n  }, [session])\r\n\r\n  useEffect(() => {\r\n    if (userId) {\r\n      loadSecurityData()\r\n    }\r\n  }, [userId])\r\n\r\n  const loadSecurityData = async () => {\r\n    try {\r\n      setLoading(true)\r\n      setError('')\r\n\r\n      const statusResult = await getMFAStatus(userId)\r\n      setMfaStatus(statusResult.data)\r\n    } catch (err) {\r\n      setError(err.message)\r\n    } finally {\r\n      setLoading(false)\r\n    }\r\n  }\r\n\r\n  const getSecurityScore = () => {\r\n    let score = 0\r\n    let maxScore = 100\r\n\r\n    // MFA enabled (60 points)\r\n    if (mfaStatus?.mfaEnabled) score += 60\r\n\r\n    // Recent MFA usage (25 points)\r\n    if (mfaStatus?.lastMfaUsed) {\r\n      const daysSinceLastUse = (new Date() - new Date(mfaStatus.lastMfaUsed)) / (1000 * 60 * 60 * 24)\r\n      if (daysSinceLastUse <= 7) score += 25\r\n      else if (daysSinceLastUse <= 30) score += 15\r\n    }\r\n\r\n    // Backup codes available (15 points)\r\n    if (mfaStatus?.backupCodesCount > 0) score += 15\r\n\r\n    return Math.min(score, maxScore)\r\n  }\r\n\r\n  const getSecurityLevel = (score) => {\r\n    if (score >= 80) return { level: 'Excellent', color: 'success', icon: <CheckCircle /> }\r\n    if (score >= 60) return { level: 'Good', color: 'info', icon: <Info /> }\r\n    if (score >= 40) return { level: 'Fair', color: 'warning', icon: <Warning /> }\r\n    return { level: 'Poor', color: 'error', icon: <Error /> }\r\n  }\r\n\r\n  const formatDate = (dateString) => {\r\n    if (!dateString) return 'Never'\r\n    return new Date(dateString).toLocaleDateString()\r\n  }\r\n\r\n  if (status === 'loading' || loading || !userId) {\r\n    return (\r\n      <Container maxWidth=\"lg\" sx={{ py: 4 }}>\r\n        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '200px' }}>\r\n          <LinearProgress sx={{ width: '100%' }} />\r\n        </Box>\r\n      </Container>\r\n    )\r\n  }\r\n\r\n  if (status === 'unauthenticated') {\r\n    return (\r\n      <Container maxWidth=\"lg\" sx={{ py: 4 }}>\r\n        <Alert severity=\"error\">\r\n          You must be logged in to access the security dashboard.\r\n        </Alert>\r\n      </Container>\r\n    )\r\n  }\r\n\r\n  const securityScore = getSecurityScore()\r\n  const securityLevel = getSecurityLevel(securityScore)\r\n\r\n  return (\r\n    <Container maxWidth=\"lg\" sx={{ py: 4 }}>\r\n      <Box sx={{ mb: 4 }}>\r\n        <Typography variant=\"h4\" component=\"h1\" gutterBottom>\r\n          Admin Security Dashboard\r\n        </Typography>\r\n        <Typography variant=\"body1\" color=\"text.secondary\">\r\n          Monitor and manage your account security settings and MFA devices.\r\n        </Typography>\r\n      </Box>\r\n\r\n      {error && (\r\n        <Alert severity=\"error\" sx={{ mb: 3 }}>\r\n          {error}\r\n        </Alert>\r\n      )}\r\n\r\n      <Grid container spacing={3}>\r\n        {/* Security Score Card */}\r\n        <Grid item xs={12} md={6}>\r\n          <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>\r\n            <CardHeader\r\n              avatar={securityLevel.icon}\r\n              title=\"Security Score\"\r\n              subheader={`${securityScore}/100`}\r\n            />\r\n            <CardContent sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>\r\n              <Box sx={{ mb: 2 }}>\r\n                <LinearProgress\r\n                  variant=\"determinate\"\r\n                  value={securityScore}\r\n                  color={securityLevel.color}\r\n                  sx={{ height: 8, borderRadius: 4 }}\r\n                />\r\n              </Box>\r\n              <Chip\r\n                label={securityLevel.level}\r\n                color={securityLevel.color}\r\n                variant=\"outlined\"\r\n              />\r\n            </CardContent>\r\n          </Card>\r\n        </Grid>\r\n\r\n        {/* MFA Status Card */}\r\n        <Grid item xs={12} md={6}>\r\n          <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>\r\n            <CardHeader\r\n              avatar={<Shield />}\r\n              title=\"MFA Status\"\r\n              subheader={mfaStatus?.mfaEnabled ? 'Enabled' : 'Disabled'}\r\n            />\r\n            <CardContent sx={{ flexGrow: 1 }}>\r\n              <List dense>\r\n                <ListItem>\r\n                  <ListItemIcon>\r\n                    <Key />\r\n                  </ListItemIcon>\r\n                  <ListItemText\r\n                    primary=\"Backup Codes\"\r\n                    secondary={`${mfaStatus?.backupCodesCount || 0} available`}\r\n                  />\r\n                </ListItem>\r\n                <ListItem>\r\n                  <ListItemIcon>\r\n                    <AccessTime />\r\n                  </ListItemIcon>\r\n                  <ListItemText\r\n                    primary=\"Last Used\"\r\n                    secondary={formatDate(mfaStatus?.lastMfaUsed)}\r\n                  />\r\n                </ListItem>\r\n              </List>\r\n            </CardContent>\r\n          </Card>\r\n        </Grid>\r\n\r\n        {/* Recent Activity */}\r\n        <Grid item xs={12}>\r\n          <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>\r\n            <CardHeader\r\n              avatar={<AccessTime />}\r\n              title=\"Recent Security Activity\"\r\n            />\r\n            <CardContent sx={{ flexGrow: 1 }}>\r\n              <List>\r\n                {mfaStatus?.mfaSetupAt && (\r\n                  <ListItem>\r\n                    <ListItemIcon>\r\n                      <Shield color=\"success\" />\r\n                    </ListItemIcon>\r\n                    <ListItemText\r\n                      primary=\"MFA Enabled\"\r\n                      secondary={`Setup completed on ${formatDate(mfaStatus.mfaSetupAt)}`}\r\n                    />\r\n                  </ListItem>\r\n                )}\r\n                {!mfaStatus?.mfaEnabled && (\r\n                  <ListItem>\r\n                    <ListItemText\r\n                      primary=\"No security activity yet\"\r\n                      secondary=\"Enable MFA to start tracking security events\"\r\n                    />\r\n                  </ListItem>\r\n                )}\r\n              </List>\r\n            </CardContent>\r\n          </Card>\r\n        </Grid>\r\n\r\n        {/* MFA Settings */}\r\n        <Grid item xs={12}>\r\n          <MFASettings userId={userId} userEmail={userEmail} />\r\n        </Grid>\r\n      </Grid>\r\n    </Container>\r\n  )\r\n}\r\n\r\nexport default AdminSecurityDashboard\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAkBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AACA;;;AAhCA;;;;;;;;;;;;;AAkCA,MAAM,yBAAyB;;IAC7B,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IAC3C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4CAAE;YACR,2CAA2C;YAC3C,IAAI,SAAS,MAAM;gBACjB,aAAa,QAAQ,IAAI,CAAC,KAAK;gBAC/B,UAAU,QAAQ,IAAI,CAAC,EAAE;YAC3B;QACF;2CAAG;QAAC;KAAQ;IAEZ,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4CAAE;YACR,IAAI,QAAQ;gBACV;YACF;QACF;2CAAG;QAAC;KAAO;IAEX,MAAM,mBAAmB;QACvB,IAAI;YACF,WAAW;YACX,SAAS;YAET,MAAM,eAAe,MAAM,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD,EAAE;YACxC,aAAa,aAAa,IAAI;QAChC,EAAE,OAAO,KAAK;YACZ,SAAS,IAAI,OAAO;QACtB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,mBAAmB;QACvB,IAAI,QAAQ;QACZ,IAAI,WAAW;QAEf,0BAA0B;QAC1B,IAAI,WAAW,YAAY,SAAS;QAEpC,+BAA+B;QAC/B,IAAI,WAAW,aAAa;YAC1B,MAAM,mBAAmB,CAAC,IAAI,SAAS,IAAI,KAAK,UAAU,WAAW,CAAC,IAAI,CAAC,OAAO,KAAK,KAAK,EAAE;YAC9F,IAAI,oBAAoB,GAAG,SAAS;iBAC/B,IAAI,oBAAoB,IAAI,SAAS;QAC5C;QAEA,qCAAqC;QACrC,IAAI,WAAW,mBAAmB,GAAG,SAAS;QAE9C,OAAO,KAAK,GAAG,CAAC,OAAO;IACzB;IAEA,MAAM,mBAAmB,CAAC;QACxB,IAAI,SAAS,IAAI,OAAO;YAAE,OAAO;YAAa,OAAO;YAAW,oBAAM,6LAAC,mKAAA,CAAA,UAAW;;;;;QAAI;QACtF,IAAI,SAAS,IAAI,OAAO;YAAE,OAAO;YAAQ,OAAO;YAAQ,oBAAM,6LAAC,4JAAA,CAAA,UAAI;;;;;QAAI;QACvE,IAAI,SAAS,IAAI,OAAO;YAAE,OAAO;YAAQ,OAAO;YAAW,oBAAM,6LAAC,+JAAA,CAAA,UAAO;;;;;QAAI;QAC7E,OAAO;YAAE,OAAO;YAAQ,OAAO;YAAS,oBAAM,6LAAC,6JAAA,CAAA,UAAK;;;;;QAAI;IAC1D;IAEA,MAAM,aAAa,CAAC;QAClB,IAAI,CAAC,YAAY,OAAO;QACxB,OAAO,IAAI,KAAK,YAAY,kBAAkB;IAChD;IAEA,IAAI,WAAW,aAAa,WAAW,CAAC,QAAQ;QAC9C,qBACE,6LAAC,sMAAA,CAAA,YAAS;YAAC,UAAS;YAAK,IAAI;gBAAE,IAAI;YAAE;sBACnC,cAAA,6LAAC,oLAAA,CAAA,MAAG;gBAAC,IAAI;oBAAE,SAAS;oBAAQ,gBAAgB;oBAAU,YAAY;oBAAU,WAAW;gBAAQ;0BAC7F,cAAA,6LAAC,qNAAA,CAAA,iBAAc;oBAAC,IAAI;wBAAE,OAAO;oBAAO;;;;;;;;;;;;;;;;IAI5C;IAEA,IAAI,WAAW,mBAAmB;QAChC,qBACE,6LAAC,sMAAA,CAAA,YAAS;YAAC,UAAS;YAAK,IAAI;gBAAE,IAAI;YAAE;sBACnC,cAAA,6LAAC,0LAAA,CAAA,QAAK;gBAAC,UAAS;0BAAQ;;;;;;;;;;;IAK9B;IAEA,MAAM,gBAAgB;IACtB,MAAM,gBAAgB,iBAAiB;IAEvC,qBACE,6LAAC,sMAAA,CAAA,YAAS;QAAC,UAAS;QAAK,IAAI;YAAE,IAAI;QAAE;;0BACnC,6LAAC,oLAAA,CAAA,MAAG;gBAAC,IAAI;oBAAE,IAAI;gBAAE;;kCACf,6LAAC,yMAAA,CAAA,aAAU;wBAAC,SAAQ;wBAAK,WAAU;wBAAK,YAAY;kCAAC;;;;;;kCAGrD,6LAAC,yMAAA,CAAA,aAAU;wBAAC,SAAQ;wBAAQ,OAAM;kCAAiB;;;;;;;;;;;;YAKpD,uBACC,6LAAC,0LAAA,CAAA,QAAK;gBAAC,UAAS;gBAAQ,IAAI;oBAAE,IAAI;gBAAE;0BACjC;;;;;;0BAIL,6LAAC,uLAAA,CAAA,OAAI;gBAAC,SAAS;gBAAC,SAAS;;kCAEvB,6LAAC,uLAAA,CAAA,OAAI;wBAAC,IAAI;wBAAC,IAAI;wBAAI,IAAI;kCACrB,cAAA,6LAAC,uLAAA,CAAA,OAAI;4BAAC,IAAI;gCAAE,QAAQ;gCAAQ,SAAS;gCAAQ,eAAe;4BAAS;;8CACnE,6LAAC,yMAAA,CAAA,aAAU;oCACT,QAAQ,cAAc,IAAI;oCAC1B,OAAM;oCACN,WAAW,GAAG,cAAc,IAAI,CAAC;;;;;;8CAEnC,6LAAC,4MAAA,CAAA,cAAW;oCAAC,IAAI;wCAAE,UAAU;wCAAG,SAAS;wCAAQ,eAAe;wCAAU,gBAAgB;oCAAS;;sDACjG,6LAAC,oLAAA,CAAA,MAAG;4CAAC,IAAI;gDAAE,IAAI;4CAAE;sDACf,cAAA,6LAAC,qNAAA,CAAA,iBAAc;gDACb,SAAQ;gDACR,OAAO;gDACP,OAAO,cAAc,KAAK;gDAC1B,IAAI;oDAAE,QAAQ;oDAAG,cAAc;gDAAE;;;;;;;;;;;sDAGrC,6LAAC,uLAAA,CAAA,OAAI;4CACH,OAAO,cAAc,KAAK;4CAC1B,OAAO,cAAc,KAAK;4CAC1B,SAAQ;;;;;;;;;;;;;;;;;;;;;;;kCAOhB,6LAAC,uLAAA,CAAA,OAAI;wBAAC,IAAI;wBAAC,IAAI;wBAAI,IAAI;kCACrB,cAAA,6LAAC,uLAAA,CAAA,OAAI;4BAAC,IAAI;gCAAE,QAAQ;gCAAQ,SAAS;gCAAQ,eAAe;4BAAS;;8CACnE,6LAAC,yMAAA,CAAA,aAAU;oCACT,sBAAQ,6LAAC,8JAAA,CAAA,UAAM;;;;;oCACf,OAAM;oCACN,WAAW,WAAW,aAAa,YAAY;;;;;;8CAEjD,6LAAC,4MAAA,CAAA,cAAW;oCAAC,IAAI;wCAAE,UAAU;oCAAE;8CAC7B,cAAA,6LAAC,uLAAA,CAAA,OAAI;wCAAC,KAAK;;0DACT,6LAAC,mMAAA,CAAA,WAAQ;;kEACP,6LAAC,+MAAA,CAAA,eAAY;kEACX,cAAA,6LAAC,2JAAA,CAAA,UAAG;;;;;;;;;;kEAEN,6LAAC,+MAAA,CAAA,eAAY;wDACX,SAAQ;wDACR,WAAW,GAAG,WAAW,oBAAoB,EAAE,UAAU,CAAC;;;;;;;;;;;;0DAG9D,6LAAC,mMAAA,CAAA,WAAQ;;kEACP,6LAAC,+MAAA,CAAA,eAAY;kEACX,cAAA,6LAAC,kKAAA,CAAA,UAAU;;;;;;;;;;kEAEb,6LAAC,+MAAA,CAAA,eAAY;wDACX,SAAQ;wDACR,WAAW,WAAW,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAS7C,6LAAC,uLAAA,CAAA,OAAI;wBAAC,IAAI;wBAAC,IAAI;kCACb,cAAA,6LAAC,uLAAA,CAAA,OAAI;4BAAC,IAAI;gCAAE,QAAQ;gCAAQ,SAAS;gCAAQ,eAAe;4BAAS;;8CACnE,6LAAC,yMAAA,CAAA,aAAU;oCACT,sBAAQ,6LAAC,kKAAA,CAAA,UAAU;;;;;oCACnB,OAAM;;;;;;8CAER,6LAAC,4MAAA,CAAA,cAAW;oCAAC,IAAI;wCAAE,UAAU;oCAAE;8CAC7B,cAAA,6LAAC,uLAAA,CAAA,OAAI;;4CACF,WAAW,4BACV,6LAAC,mMAAA,CAAA,WAAQ;;kEACP,6LAAC,+MAAA,CAAA,eAAY;kEACX,cAAA,6LAAC,8JAAA,CAAA,UAAM;4DAAC,OAAM;;;;;;;;;;;kEAEhB,6LAAC,+MAAA,CAAA,eAAY;wDACX,SAAQ;wDACR,WAAW,CAAC,mBAAmB,EAAE,WAAW,UAAU,UAAU,GAAG;;;;;;;;;;;;4CAIxE,CAAC,WAAW,4BACX,6LAAC,mMAAA,CAAA,WAAQ;0DACP,cAAA,6LAAC,+MAAA,CAAA,eAAY;oDACX,SAAQ;oDACR,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUxB,6LAAC,uLAAA,CAAA,OAAI;wBAAC,IAAI;wBAAC,IAAI;kCACb,cAAA,6LAAC,2IAAA,CAAA,UAAW;4BAAC,QAAQ;4BAAQ,WAAW;;;;;;;;;;;;;;;;;;;;;;;AAKlD;GAhNM;;QAC8B,iJAAA,CAAA,aAAU;;;KADxC;uCAkNS", "debugId": null}}]}