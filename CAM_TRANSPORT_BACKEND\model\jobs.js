const mongoose = require('mongoose');

const JobsSchema = new mongoose.Schema({
    first_name: {
        type: String,
        required: true
    },

    last_name: {
        type: String,
        required: true,
    },

    email: {
        type: String,
        required: true
    },

    phone_number: {
        type: String,
        required: true
    },

    dob: {
        type: Date,
        required: true
    },

    address: {
        type: String,
        required: true
    },

    position: {
        type: String,
        enum: ['Driving Position', 'Non-Driving Position'],
        required: true
    },

    specific_driving_role: {
        type: String,
        enum: ['CDL Driver', 'Non-CDL Driver', 'Heavy Equipment Operator', 'Local Driver', 'Long Haul Driver', 'Other Driving Role']
    },

    specific_non_driving_role: {
        type: String,
        enum: ['Dispatcher', 'Admin', 'Manager', 'Technician', 'HR', 'Accountant', 'Other Non-Driving Role']
    },

    commercial_license: {
        type: Boolean
    },

    other_job: {
        type: String
    },

    employment_type: {
        type: String,
        enum: ['Full-time', 'Part-time', 'Contract', 'Temporary', 'Freelance'],
        required: true,
    },

    preferred_start_date: {
        type: Date,
        required: true
    },

    relocate: {
        type: String,
        required: true,
        enum: ['Yes', 'No'],
    },

    experience: {
        type: String,
        required: true,
        enum: ['Fresher', '<6 months', '6-12 months', '1-2 years', '2-5 years', '5+ years', '10+ years'],
    },

    resume: {
        type: String,
        required: true
    },

    work_reason: {
        type: String,
        required: true
    },

    reference: {
        type: String,
        required: true,
        enum: [
            'Job Portal(Indeed,Monster,etc.)',
            'Social Media(LinkedIN,Facebook,Instagram)',
            'Friends or Colleagues',
            'Advertisement',
            'Walk-in or On-site Visit',
            'Other(Please specify)',
        ],
    },

    other_reference: {
        type: String,
        required: function () {
            return this.reference === 'Other(Please specify)';
        }
    },
    _honeypot: {
        type: String,
        default: '',
        select: false // Don't expose in queries
    },
    ip: {
        type: String,
        required: false
    },
    status: {
        type: String,
        enum: ['pending', 'in-view', 'completed'],
        default: 'pending'
    }
}, {
    timestamps: true,
});

const Jobs = mongoose.model('JobApplication', JobsSchema);

module.exports = Jobs;