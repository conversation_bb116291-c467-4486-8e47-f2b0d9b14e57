const mongoose = require('mongoose');
const speakeasy = require('speakeasy');
require('dotenv').config();

// Connect to MongoDB using the same connection as the backend
if (!process.env.MONGO_URL) {
    console.error('❌ MONGO_URL environment variable is missing!');
    process.exit(1);
}

mongoose.connect(process.env.MONGO_URL, {
    ssl: true,
    serverSelectionTimeoutMS: 5000,
    connectTimeoutMS: 10000
});

// Define the Login schema (simplified)
const LoginSchema = new mongoose.Schema({
    username: String,
    email: String,
    password: String,
    mfaSecret: String,
    mfaEnabled: Boolean,
    mfaDevices: Array,
    isVerified: <PERSON><PERSON><PERSON>,
    role: String
}, { timestamps: true });

const Login = mongoose.model('Login', LoginSchema);

async function checkUserMFA() {
    console.log('🔍 CHECKING MFA <NAME_EMAIL>');
    console.log('==============================================\n');
    
    try {
        // Find the user
        const user = await Login.findOne({ email: '<EMAIL>' });
        
        if (!user) {
            console.log('❌ User not found with email: <EMAIL>');
            return;
        }
        
        console.log('✅ User found:');
        console.log('   Username:', user.username);
        console.log('   Email:', user.email);
        console.log('   MFA Enabled:', user.mfaEnabled);
        console.log('   MFA Secret Length:', user.mfaSecret ? user.mfaSecret.length : 'null');
        console.log('   MFA Devices:', user.mfaDevices ? user.mfaDevices.length : 0);
        console.log('   Is Verified:', user.isVerified);
        
        if (user.mfaSecret) {
            console.log('\n🔐 MFA Secret Details:');
            console.log('   Secret Preview:', `${user.mfaSecret.substring(0, 4)}****`);
            
            // Generate current token
            const currentToken = speakeasy.totp({
                secret: user.mfaSecret,
                encoding: 'base32'
            });
            
            console.log('   Current Expected Token:', currentToken);
            console.log('   Current Time:', new Date().toLocaleTimeString());
            
            // Test verification
            const verified = speakeasy.totp.verify({
                secret: user.mfaSecret,
                encoding: 'base32',
                token: currentToken,
                window: 1
            });
            
            console.log('   Self-verification test:', verified ? '✅ PASS' : '❌ FAIL');
            
            // Show next few tokens for timing issues
            console.log('\n⏰ Next tokens (in case of timing issues):');
            for (let i = 1; i <= 3; i++) {
                const futureTime = Math.floor(Date.now() / 1000) + (i * 30);
                const futureToken = speakeasy.totp({
                    secret: user.mfaSecret,
                    encoding: 'base32',
                    time: futureTime
                });
                const timeStr = new Date(futureTime * 1000).toLocaleTimeString();
                console.log(`   ${timeStr}: ${futureToken}`);
            }
        } else {
            console.log('\n❌ No MFA secret found - user needs to set up MFA');
        }
        
    } catch (error) {
        console.error('❌ Error:', error);
    } finally {
        mongoose.connection.close();
    }
}

checkUserMFA();
