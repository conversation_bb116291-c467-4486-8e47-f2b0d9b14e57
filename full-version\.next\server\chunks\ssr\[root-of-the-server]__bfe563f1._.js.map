{"version": 3, "sources": [], "sections": [{"offset": {"line": 79, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/fake-db/apps/ecommerce.js"], "sourcesContent": ["export const db = {\r\n  products: [\r\n    {\r\n      id: 1,\r\n      productName: 'iPhone 14 Pro',\r\n      category: 'Electronics',\r\n      stock: true,\r\n      sku: 19472,\r\n      price: '$999',\r\n      qty: 665,\r\n      status: 'Inactive',\r\n      image: '/images/apps/ecommerce/product-1.png',\r\n      productBrand: 'Super Retina XDR display footnote Pro Motion technology'\r\n    },\r\n    {\r\n      id: 2,\r\n      productName: 'Echo Dot (4th Gen)',\r\n      category: 'Electronics',\r\n      stock: false,\r\n      sku: 72836,\r\n      price: '$25.50',\r\n      qty: 827,\r\n      status: 'Published',\r\n      image: '/images/apps/ecommerce/product-2.png',\r\n      productBrand: 'Echo Dot Smart speaker with Alexa'\r\n    },\r\n    {\r\n      id: 3,\r\n      productName: 'Dohioue Wall Clock',\r\n      category: 'Accessories',\r\n      stock: false,\r\n      sku: 29540,\r\n      price: '$16.34',\r\n      qty: 804,\r\n      status: 'Published',\r\n      image: '/images/apps/ecommerce/product-3.png',\r\n      productBrand: 'Modern 10 Inch Battery Operated Wall Clocks'\r\n    },\r\n    {\r\n      id: 4,\r\n      productName: 'INZCOU Running Shoes',\r\n      category: 'Shoes',\r\n      stock: false,\r\n      sku: 49402,\r\n      price: '$36.98',\r\n      qty: 528,\r\n      status: 'Scheduled',\r\n      image: '/images/apps/ecommerce/product-4.png',\r\n      productBrand: 'Lightweight Tennis Shoes Non Slip Gym Workout Shoes'\r\n    },\r\n    {\r\n      id: 5,\r\n      productName: 'Apple Watch Series 7',\r\n      category: 'Office',\r\n      stock: false,\r\n      sku: 46658,\r\n      price: '$799',\r\n      qty: 851,\r\n      status: 'Scheduled',\r\n      image: '/images/apps/ecommerce/product-5.png',\r\n      productBrand: 'Starlight Aluminum Case with Starlight Sport Band.'\r\n    },\r\n    {\r\n      id: 6,\r\n      productName: 'Meta Quest 2',\r\n      category: 'Office',\r\n      stock: true,\r\n      sku: 57640,\r\n      price: '$299',\r\n      qty: 962,\r\n      status: 'Scheduled',\r\n      image: '/images/apps/ecommerce/product-6.png',\r\n      productBrand: 'Advanced All-In-One Virtual Reality Headset'\r\n    },\r\n    {\r\n      id: 7,\r\n      productName: 'MacBook Pro 16',\r\n      category: 'Electronics',\r\n      stock: true,\r\n      sku: 92885,\r\n      price: '$2648.95',\r\n      qty: 965,\r\n      status: 'Published',\r\n      image: '/images/apps/ecommerce/product-7.png',\r\n      productBrand: 'Laptop M2 Pro chip with 12‑core CPU and 19‑core GPU'\r\n    },\r\n    {\r\n      id: 8,\r\n      productName: 'SAMSUNG Galaxy S22 Ultra',\r\n      category: 'Electronics',\r\n      stock: true,\r\n      sku: 75257,\r\n      price: '$899',\r\n      qty: 447,\r\n      status: 'Published',\r\n      image: '/images/apps/ecommerce/product-8.png',\r\n      productBrand: 'Android Smartphone, 256GB, 8K Camera'\r\n    },\r\n    {\r\n      id: 9,\r\n      productName: 'Air Jordan',\r\n      category: 'Shoes',\r\n      stock: false,\r\n      sku: 31063,\r\n      price: '$125',\r\n      qty: 942,\r\n      status: 'Inactive',\r\n      image: '/images/apps/ecommerce/product-9.png',\r\n      productBrand: 'Air Jordan is a line of basketball shoes produced by Nike'\r\n    },\r\n    {\r\n      id: 10,\r\n      productName: 'VISKABACKA',\r\n      category: 'Home Decor',\r\n      stock: false,\r\n      sku: 91848,\r\n      price: '$190.45',\r\n      qty: 133,\r\n      status: 'Scheduled',\r\n      image: '/images/apps/ecommerce/product-10.png',\r\n      productBrand: 'Armchair, Skartofta black/light grey'\r\n    },\r\n    {\r\n      id: 11,\r\n      productName: 'Nintendo Switch',\r\n      category: 'Games',\r\n      stock: true,\r\n      sku: 52575,\r\n      price: '$296.99',\r\n      qty: 870,\r\n      status: 'Inactive',\r\n      image: '/images/apps/ecommerce/product-11.png',\r\n      productBrand: 'TV Mode, Tabletop Mode, Handheld Mode'\r\n    },\r\n    {\r\n      id: 12,\r\n      productName: 'PlayStation 5',\r\n      category: 'Games',\r\n      stock: true,\r\n      sku: 59551,\r\n      price: '$499',\r\n      qty: 145,\r\n      status: 'Scheduled',\r\n      image: '/images/apps/ecommerce/product-12.png',\r\n      productBrand: 'Marvel at incredible graphics and experience'\r\n    },\r\n    {\r\n      id: 13,\r\n      productName: 'Amazon Fire TV',\r\n      category: 'Electronics',\r\n      stock: false,\r\n      sku: 5829,\r\n      price: '$263.49',\r\n      qty: 587,\r\n      status: 'Scheduled',\r\n      image: '/images/apps/ecommerce/product-13.png',\r\n      productBrand: '4K UHD smart TV, stream live TV without cable'\r\n    },\r\n    {\r\n      id: 14,\r\n      productName: 'Smiletag Ceramic Vase',\r\n      category: 'Home Decor',\r\n      stock: false,\r\n      sku: 24456,\r\n      price: '$34.99',\r\n      qty: 310,\r\n      status: 'Scheduled',\r\n      image: '/images/apps/ecommerce/product-14.png',\r\n      productBrand: 'Modern Farmhouse Decor Vase Set of 3'\r\n    },\r\n    {\r\n      id: 15,\r\n      productName: 'Apple iPad',\r\n      category: 'Electronics',\r\n      stock: true,\r\n      sku: 35946,\r\n      price: '$248.39',\r\n      qty: 468,\r\n      status: 'Published',\r\n      image: '/images/apps/ecommerce/product-15.png',\r\n      productBrand: '10.2-inch Retina Display, 64GB'\r\n    },\r\n    {\r\n      id: 16,\r\n      productName: 'BANGE Anti Theft Backpack',\r\n      category: 'Office',\r\n      stock: true,\r\n      sku: 41867,\r\n      price: '$79.99',\r\n      qty: 519,\r\n      status: 'Inactive',\r\n      image: '/images/apps/ecommerce/product-16.png',\r\n      productBrand: 'Smart Business Laptop Fits 15.6 Inch Notebook'\r\n    },\r\n    {\r\n      id: 17,\r\n      productName: 'Xbox Series X/S',\r\n      category: 'Games',\r\n      stock: true,\r\n      sku: 43224,\r\n      price: '$49.99',\r\n      qty: 787,\r\n      status: 'Published',\r\n      image: '/images/apps/ecommerce/product-17.png',\r\n      productBrand: 'Dual Controller Charger Station Dock'\r\n    },\r\n    {\r\n      id: 18,\r\n      productName: 'Canon EOS Rebel T7',\r\n      category: 'Electronics',\r\n      stock: true,\r\n      sku: 63474,\r\n      price: '$399',\r\n      qty: 810,\r\n      status: 'Scheduled',\r\n      image: '/images/apps/ecommerce/product-18.png',\r\n      productBrand: '18-55mm Lens | Built-in Wi-Fi | 24.1 MP CMOS Sensor'\r\n    },\r\n    {\r\n      id: 19,\r\n      productName: 'Honiway Wall Mirror',\r\n      category: 'Home Decor',\r\n      stock: false,\r\n      sku: 15859,\r\n      price: '$23.99',\r\n      qty: 735,\r\n      status: 'Inactive',\r\n      image: '/images/apps/ecommerce/product-19.png',\r\n      productBrand: 'Decorative 12 inch Rustic Wood Mirror Sunburst Boho'\r\n    },\r\n    {\r\n      id: 20,\r\n      productName: 'Tommaso Veloce Shoes',\r\n      category: 'Shoes',\r\n      stock: false,\r\n      sku: 28844,\r\n      price: '$922.09',\r\n      qty: 294,\r\n      status: 'Inactive',\r\n      image: '/images/apps/ecommerce/product-20.png',\r\n      productBrand: 'Peloton Shoes Triathlon Road Bike Indoor Cycling'\r\n    },\r\n    {\r\n      id: 21,\r\n      productName: 'Zoolab',\r\n      category: 'Accessories',\r\n      stock: true,\r\n      sku: 99009,\r\n      price: '$719.13',\r\n      qty: 927,\r\n      status: 'Scheduled',\r\n      image: '/images/apps/ecommerce/product-1.png',\r\n      productBrand: 'Cruickshank-Jones'\r\n    },\r\n    {\r\n      id: 22,\r\n      productName: 'Viva',\r\n      category: 'Home Decor',\r\n      stock: false,\r\n      sku: 53795,\r\n      price: '$775.80',\r\n      qty: 442,\r\n      status: 'Scheduled',\r\n      image: '/images/apps/ecommerce/product-2.png',\r\n      productBrand: 'Ferry Group'\r\n    },\r\n    {\r\n      id: 23,\r\n      productName: 'Transcof',\r\n      category: 'Shoes',\r\n      stock: true,\r\n      sku: 77663,\r\n      price: '$817.60',\r\n      qty: 256,\r\n      status: 'Published',\r\n      image: '/images/apps/ecommerce/product-3.png',\r\n      productBrand: 'Bruen-Heathcote'\r\n    },\r\n    {\r\n      id: 24,\r\n      productName: 'Uerified',\r\n      category: 'Accessories',\r\n      stock: false,\r\n      sku: 45282,\r\n      price: '$167.19',\r\n      qty: 728,\r\n      status: 'Published',\r\n      image: '/images/apps/ecommerce/product-4.png',\r\n      productBrand: 'Koch Group'\r\n    },\r\n    {\r\n      id: 25,\r\n      productName: 'Y-find',\r\n      category: 'Home Decor',\r\n      stock: false,\r\n      sku: 5622,\r\n      price: '$189.77',\r\n      qty: 445,\r\n      status: 'Scheduled',\r\n      image: '/images/apps/ecommerce/product-5.png',\r\n      productBrand: 'Emmerich and Sons'\r\n    },\r\n    {\r\n      id: 26,\r\n      productName: 'Wigtax',\r\n      category: 'Accessories',\r\n      stock: true,\r\n      sku: 38920,\r\n      price: '$411.46',\r\n      qty: 857,\r\n      status: 'Scheduled',\r\n      image: '/images/apps/ecommerce/product-6.png',\r\n      productBrand: 'Zulauf-Prohaska'\r\n    },\r\n    {\r\n      id: 27,\r\n      productName: 'Tempsoft',\r\n      category: 'Accessories',\r\n      stock: true,\r\n      sku: 78211,\r\n      price: '$961.76',\r\n      qty: 975,\r\n      status: 'Published',\r\n      image: '/images/apps/ecommerce/product-7.png',\r\n      productBrand: 'VonRueden, Rogahn and Kris'\r\n    },\r\n    {\r\n      id: 28,\r\n      productName: 'Rt',\r\n      category: 'Accessories',\r\n      stock: true,\r\n      sku: 98552,\r\n      price: '$514.14',\r\n      qty: 39,\r\n      status: 'Published',\r\n      image: '/images/apps/ecommerce/product-8.png',\r\n      productBrand: \"Romaguera, O'Connell and Abernathy\"\r\n    },\r\n    {\r\n      id: 29,\r\n      productName: 'Zontrax',\r\n      category: 'Shoes',\r\n      stock: true,\r\n      sku: 7151,\r\n      price: '$591.30',\r\n      qty: 74,\r\n      status: 'Published',\r\n      image: '/images/apps/ecommerce/product-9.png',\r\n      productBrand: 'Mills, Hagenes and Bednar'\r\n    },\r\n    {\r\n      id: 30,\r\n      productName: 'Keylex',\r\n      category: 'Accessories',\r\n      stock: true,\r\n      sku: 79571,\r\n      price: '$928.07',\r\n      qty: 245,\r\n      status: 'Inactive',\r\n      image: '/images/apps/ecommerce/product-10.png',\r\n      productBrand: 'Sanford, Harvey and Parisian'\r\n    },\r\n    {\r\n      id: 31,\r\n      productName: 'Trippledex',\r\n      category: 'Home Decor',\r\n      stock: false,\r\n      sku: 51597,\r\n      price: '$312.03',\r\n      qty: 657,\r\n      status: 'Inactive',\r\n      image: '/images/apps/ecommerce/product-11.png',\r\n      productBrand: 'Conroy-Bergstrom'\r\n    },\r\n    {\r\n      id: 32,\r\n      productName: 'Opela',\r\n      category: 'Accessories',\r\n      stock: true,\r\n      sku: 6506,\r\n      price: '$951.29',\r\n      qty: 770,\r\n      status: 'Published',\r\n      image: '/images/apps/ecommerce/product-12.png',\r\n      productBrand: 'Langosh Inc'\r\n    },\r\n    {\r\n      id: 33,\r\n      productName: 'Span',\r\n      category: 'Shoes',\r\n      stock: false,\r\n      sku: 33523,\r\n      price: '$600.43',\r\n      qty: 622,\r\n      status: 'Inactive',\r\n      image: '/images/apps/ecommerce/product-13.png',\r\n      productBrand: 'Jerde-Walsh'\r\n    },\r\n    {\r\n      id: 34,\r\n      productName: 'Rank',\r\n      category: 'Accessories',\r\n      stock: false,\r\n      sku: 60307,\r\n      price: '$337.90',\r\n      qty: 896,\r\n      status: 'Scheduled',\r\n      image: '/images/apps/ecommerce/product-14.png',\r\n      productBrand: 'Barrows, Quitzon and Roberts'\r\n    },\r\n    {\r\n      id: 35,\r\n      productName: 'Tempsoft',\r\n      category: 'Accessories',\r\n      stock: true,\r\n      sku: 75059,\r\n      price: '$959.47',\r\n      qty: 239,\r\n      status: 'Inactive',\r\n      image: '/images/apps/ecommerce/product-15.png',\r\n      productBrand: 'Russel-Grant'\r\n    },\r\n    {\r\n      id: 36,\r\n      productName: 'Ventosanzap',\r\n      category: 'Accessories',\r\n      stock: true,\r\n      sku: 69072,\r\n      price: '$756.81',\r\n      qty: 410,\r\n      status: 'Scheduled',\r\n      image: '/images/apps/ecommerce/product-16.png',\r\n      productBrand: \"O'Conner-Zboncak\"\r\n    },\r\n    {\r\n      id: 37,\r\n      productName: 'Mat Lam Tam',\r\n      category: 'Accessories',\r\n      stock: false,\r\n      sku: 68290,\r\n      price: '$256.86',\r\n      qty: 630,\r\n      status: 'Published',\r\n      image: '/images/apps/ecommerce/product-17.png',\r\n      productBrand: 'Rutherford, Heller and Bashirian'\r\n    },\r\n    {\r\n      id: 38,\r\n      productName: 'Zamit',\r\n      category: 'Shoes',\r\n      stock: true,\r\n      sku: 89552,\r\n      price: '$378.54',\r\n      qty: 247,\r\n      status: 'Inactive',\r\n      image: '/images/apps/ecommerce/product-18.png',\r\n      productBrand: 'Swift-Altenwerth'\r\n    },\r\n    {\r\n      id: 39,\r\n      productName: 'Tresom',\r\n      category: 'Shoes',\r\n      stock: true,\r\n      sku: 50863,\r\n      price: '$166.17',\r\n      qty: 672,\r\n      status: 'Inactive',\r\n      image: '/images/apps/ecommerce/product-19.png',\r\n      productBrand: \"O'Kon, Waelchi and Lesch\"\r\n    },\r\n    {\r\n      id: 40,\r\n      productName: 'Viva',\r\n      category: 'Accessories',\r\n      stock: false,\r\n      sku: 90484,\r\n      price: '$745.76',\r\n      qty: 697,\r\n      status: 'Published',\r\n      image: '/images/apps/ecommerce/product-20.png',\r\n      productBrand: 'Johnston, Anderson and Metz'\r\n    },\r\n    {\r\n      id: 41,\r\n      productName: 'Matsoft',\r\n      category: 'Accessories',\r\n      stock: true,\r\n      sku: 76980,\r\n      price: '$603.16',\r\n      qty: 74,\r\n      status: 'Published',\r\n      image: '/images/apps/ecommerce/product-1.png',\r\n      productBrand: \"O'Conner, Paucek and Braun\"\r\n    },\r\n    {\r\n      id: 42,\r\n      productName: 'Wiodex',\r\n      category: 'Home Decor',\r\n      stock: true,\r\n      sku: 66971,\r\n      price: '$772.51',\r\n      qty: 280,\r\n      status: 'Published',\r\n      image: '/images/apps/ecommerce/product-2.png',\r\n      productBrand: 'Wisoky-Kassulke'\r\n    },\r\n    {\r\n      id: 43,\r\n      productName: 'Keylex',\r\n      category: 'Shoes',\r\n      stock: false,\r\n      sku: 16589,\r\n      price: '$986.22',\r\n      qty: 758,\r\n      status: 'Inactive',\r\n      image: '/images/apps/ecommerce/product-3.png',\r\n      productBrand: 'Haag, Bruen and Reichel'\r\n    },\r\n    {\r\n      id: 44,\r\n      productName: 'Konklux',\r\n      category: 'Accessories',\r\n      stock: true,\r\n      sku: 73896,\r\n      price: '$988.47',\r\n      qty: 14,\r\n      status: 'Inactive',\r\n      image: '/images/apps/ecommerce/product-4.png',\r\n      productBrand: 'Ankunding Inc'\r\n    },\r\n    {\r\n      id: 45,\r\n      productName: 'Tresom',\r\n      category: 'Accessories',\r\n      stock: false,\r\n      sku: 67489,\r\n      price: '$946.62',\r\n      qty: 35,\r\n      status: 'Inactive',\r\n      image: '/images/apps/ecommerce/product-5.png',\r\n      productBrand: 'Deckow and Sons'\r\n    },\r\n    {\r\n      id: 46,\r\n      productName: 'Quo Lux',\r\n      category: 'Shoes',\r\n      stock: true,\r\n      sku: 48177,\r\n      price: '$224.28',\r\n      qty: 935,\r\n      status: 'Scheduled',\r\n      image: '/images/apps/ecommerce/product-1.png',\r\n      productBrand: 'Kreiger, Reynolds and Sporer'\r\n    },\r\n    {\r\n      id: 47,\r\n      productName: 'Roldlamis',\r\n      category: 'Home Decor',\r\n      stock: true,\r\n      sku: 225,\r\n      price: '$952.14',\r\n      qty: 361,\r\n      status: 'Published',\r\n      image: '/images/apps/ecommerce/product-6.png',\r\n      productBrand: 'Kuphal-Abbott'\r\n    },\r\n    {\r\n      id: 48,\r\n      productName: 'Tampflex',\r\n      category: 'Accessories',\r\n      stock: false,\r\n      sku: 29438,\r\n      price: '$646.21',\r\n      qty: 908,\r\n      status: 'Scheduled',\r\n      image: '/images/apps/ecommerce/product-7.png',\r\n      productBrand: 'Romaguera, Schmeler and Volkman'\r\n    },\r\n    {\r\n      id: 49,\r\n      productName: 'Span',\r\n      category: 'Home Decor',\r\n      stock: true,\r\n      sku: 55666,\r\n      price: '$583.13',\r\n      qty: 898,\r\n      status: 'Scheduled',\r\n      image: '/images/apps/ecommerce/product-1.png',\r\n      productBrand: 'Hane-Romaguera'\r\n    },\r\n    {\r\n      id: 50,\r\n      productName: 'Zamit',\r\n      category: 'Accessories',\r\n      stock: false,\r\n      sku: 55860,\r\n      price: '$273.67',\r\n      qty: 332,\r\n      status: 'Published',\r\n      image: '/images/apps/ecommerce/product-9.png',\r\n      productBrand: 'Hoeger-Powlowski'\r\n    }\r\n  ],\r\n  reviews: [\r\n    {\r\n      id: 1,\r\n      product: 'iPhone 14 Pro',\r\n      companyName: 'Super Retina XDR display footnote Pro Motion technology',\r\n      productImage: '/images/apps/ecommerce/product-1.png',\r\n      reviewer: 'Zane Scraggs',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      date: '5/28/2020',\r\n      status: 'Published',\r\n      review: 2,\r\n      head: 'lorem ipsum dolor',\r\n      para: 'Nulla ut erat id mauris vulputate elementum. Nullam varius. Nulla facilisi.'\r\n    },\r\n    {\r\n      id: 2,\r\n      product: 'Echo Dot (4th Gen)',\r\n      companyName: 'Echo Dot Smart speaker with Alexa',\r\n      productImage: '/images/apps/ecommerce/product-2.png',\r\n      reviewer: 'Stacey Hallgalley',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/2.png',\r\n      date: '3/21/2021',\r\n      status: 'Published',\r\n      review: 5,\r\n      head: 'libero ut',\r\n      para: 'Aliquam quis turpis eget elit sodales scelerisque. Mauris sit amet eros. Suspendisse accumsan tortor quis turpis.'\r\n    },\r\n    {\r\n      id: 3,\r\n      product: 'Dohioue Wall Clock',\r\n      companyName: 'Modern 10 Inch Battery Operated Wall Clocks',\r\n      productImage: '/images/apps/ecommerce/product-3.png',\r\n      reviewer: 'Francyne Coulthurst',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/3.png',\r\n      date: '8/10/2020',\r\n      status: 'Published',\r\n      review: 2,\r\n      head: 'neque libero convallis',\r\n      para: 'Phasellus in felis. Donec semper sapien a libero. Nam dui.'\r\n    },\r\n    {\r\n      id: 4,\r\n      product: 'INZCOU Running Shoes',\r\n      companyName: 'Lightweight Tennis Shoes Non Slip Gym Workout Shoes',\r\n      productImage: '/images/apps/ecommerce/product-4.png',\r\n      reviewer: 'Nate De Mitris',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/4.png',\r\n      date: '12/18/2021',\r\n      status: 'Pending',\r\n      review: 3,\r\n      head: 'accumsan tellus nisi eu',\r\n      para: 'Praesent id massa id nisl venenatis lacinia. Aenean sit amet justo. Morbi ut odio.'\r\n    },\r\n    {\r\n      id: 5,\r\n      product: 'Apple Watch Series 7',\r\n      companyName: 'Starlight Aluminum Case with Starlight Sport Band.',\r\n      productImage: '/images/apps/ecommerce/product-5.png',\r\n      reviewer: 'Ethel Zanardii',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/5.png',\r\n      date: '6/12/2020',\r\n      status: 'Pending',\r\n      review: 1,\r\n      head: 'etiam faucibus cursus',\r\n      para: 'Cras non velit nec nisi vulputate nonummy. Maecenas tincidunt lacus at velit. Vivamus vel nulla eget eros elementum pellentesque.'\r\n    },\r\n    {\r\n      id: 6,\r\n      product: 'Meta Quest 2',\r\n      companyName: 'Advanced All-In-One Virtual Reality Headset',\r\n      productImage: '/images/apps/ecommerce/product-6.png',\r\n      reviewer: 'Fancy Tweedell',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/6.png',\r\n      date: '11/23/2020',\r\n      status: 'Pending',\r\n      review: 1,\r\n      head: 'in faucibus orci luctus et',\r\n      para: 'Curabitur in libero ut massa volutpat convallis. Morbi odio odio, elementum eu, interdum eu, tincidunt in, leo. Maecenas pulvinar lobortis est.'\r\n    },\r\n    {\r\n      id: 7,\r\n      product: 'MacBook Pro 16',\r\n      companyName: 'Laptop M2 Pro chip with 12‑core CPU and 19‑core GPU',\r\n      productImage: '/images/apps/ecommerce/product-7.png',\r\n      reviewer: 'Abeu Gregorace',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/7.png',\r\n      date: '9/8/2020',\r\n      status: 'Pending',\r\n      review: 2,\r\n      head: 'vel enim',\r\n      para: 'Curabitur in libero ut massa volutpat convallis. Morbi odio odio, elementum eu, interdum eu, tincidunt in, leo. Maecenas pulvinar lobortis est.'\r\n    },\r\n    {\r\n      id: 8,\r\n      product: 'SAMSUNG Galaxy S22 Ultra',\r\n      companyName: 'Android Smartphone, 256GB, 8K Camera',\r\n      productImage: '/images/apps/ecommerce/product-8.png',\r\n      reviewer: 'Sibylle Goodacre',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/8.png',\r\n      date: '6/10/2021',\r\n      status: 'Pending',\r\n      review: 1,\r\n      head: 'eget semper rutrum',\r\n      para: 'Curabitur in libero ut massa volutpat convallis. Morbi odio odio, elementum eu, interdum eu, tincidunt in, leo. Maecenas pulvinar lobortis est.'\r\n    },\r\n    {\r\n      id: 9,\r\n      product: 'Air Jordan',\r\n      companyName: 'Air Jordan is a line of basketball shoes produced by Nike',\r\n      productImage: '/images/apps/ecommerce/product-9.png',\r\n      reviewer: 'Gisela Leppard',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      date: '4/20/2020',\r\n      status: 'Published',\r\n      review: 2,\r\n      head: 'ut mauris',\r\n      para: 'Fusce consequat. Nulla nisl. Nunc nisl.'\r\n    },\r\n    {\r\n      id: 10,\r\n      product: 'VISKABACKA',\r\n      companyName: 'Armchair, Skartofta black/light grey',\r\n      productImage: '/images/apps/ecommerce/product-10.png',\r\n      reviewer: 'Hilario Wheldon',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/2.png',\r\n      date: '8/21/2020',\r\n      status: 'Pending',\r\n      review: 2,\r\n      head: 'amet consectetuer adipiscing elit proin',\r\n      para: 'Maecenas ut massa quis augue luctus tincidunt. Nulla mollis molestie lorem. Quisque ut erat.'\r\n    },\r\n    {\r\n      id: 11,\r\n      product: 'Nintendo Switch',\r\n      companyName: 'TV Mode, Tabletop Mode, Handheld Mode',\r\n      productImage: '/images/apps/ecommerce/product-11.png',\r\n      reviewer: 'Ivie McGlaughn',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/3.png',\r\n      date: '4/13/2020',\r\n      status: 'Pending',\r\n      review: 4,\r\n      head: 'eget nunc donec',\r\n      para: 'Curabitur in libero ut massa volutpat convallis. Morbi odio odio, elementum eu, interdum eu, tincidunt in, leo. Maecenas pulvinar lobortis est.'\r\n    },\r\n    {\r\n      id: 12,\r\n      product: 'PlayStation 5',\r\n      companyName: 'Marvel at incredible graphics and experience',\r\n      productImage: '/images/apps/ecommerce/product-12.png',\r\n      reviewer: 'Neel Kingscott',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/4.png',\r\n      date: '12/27/2020',\r\n      status: 'Published',\r\n      review: 1,\r\n      head: 'lacus at velit',\r\n      para: 'Phasellus in felis. Donec semper sapien a libero. Nam dui.'\r\n    },\r\n    {\r\n      id: 13,\r\n      product: 'Amazon Fire TV',\r\n      companyName: '4K UHD smart TV, stream live TV without cable',\r\n      productImage: '/images/apps/ecommerce/product-13.png',\r\n      reviewer: 'Tracey Ventham',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/5.png',\r\n      date: '3/17/2021',\r\n      status: 'Published',\r\n      review: 3,\r\n      head: 'at nunc commodo placerat praesent',\r\n      para: 'Aenean fermentum. Donec ut mauris eget massa tempor convallis. Nulla neque libero, convallis eget, eleifend luctus, ultricies eu, nibh.'\r\n    },\r\n    {\r\n      id: 14,\r\n      product: 'Smiletag Ceramic Vase',\r\n      companyName: 'Modern Farmhouse Decor Vase Set of 3',\r\n      productImage: '/images/apps/ecommerce/product-14.png',\r\n      reviewer: 'Rollo Truckell',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/6.png',\r\n      date: '2/23/2020',\r\n      status: 'Published',\r\n      review: 5,\r\n      head: 'in hac',\r\n      para: 'Morbi non lectus. Aliquam sit amet diam in magna bibendum imperdiet. Nullam orci pede, venenatis non, sodales sed, tincidunt eu, felis.'\r\n    },\r\n    {\r\n      id: 15,\r\n      product: 'Apple iPad',\r\n      companyName: '10.2-inch Retina Display, 64GB',\r\n      productImage: '/images/apps/ecommerce/product-15.png',\r\n      reviewer: 'Jabez Heggs',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/7.png',\r\n      date: '4/21/2020',\r\n      status: 'Published',\r\n      review: 1,\r\n      head: 'ac consequat',\r\n      para: 'Curabitur at ipsum ac tellus semper interdum. Mauris ullamcorper purus sit amet nulla. Quisque arcu libero, rutrum ac, lobortis vel, dapibus at, diam.'\r\n    },\r\n    {\r\n      id: 16,\r\n      product: 'BANGE Anti Theft Backpack',\r\n      companyName: 'Smart Business Laptop Fits 15.6 Inch Notebook',\r\n      productImage: '/images/apps/ecommerce/product-16.png',\r\n      reviewer: 'Micaela Rowesby',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      date: '12/11/2021',\r\n      status: 'Published',\r\n      review: 1,\r\n      head: 'mattis egestas metus',\r\n      para: 'Morbi porttitor lorem id ligula. Suspendisse ornare consequat lectus. In est risus, auctor sed, tristique in, tempus sit amet, sem.'\r\n    },\r\n    {\r\n      id: 17,\r\n      product: 'Xbox Series X/S',\r\n      companyName: 'Dual Controller Charger Station Dock',\r\n      productImage: '/images/apps/ecommerce/product-17.png',\r\n      reviewer: 'Blakelee Benza',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/2.png',\r\n      date: '4/26/2021',\r\n      status: 'Published',\r\n      review: 1,\r\n      head: 'sapien placerat',\r\n      para: 'Etiam vel augue. Vestibulum rutrum rutrum neque. Aenean auctor gravida sem.'\r\n    },\r\n    {\r\n      id: 18,\r\n      product: 'Canon EOS Rebel T7',\r\n      companyName: '18-55mm Lens | Built-in Wi-Fi | 24.1 MP CMOS Sensor',\r\n      productImage: '/images/apps/ecommerce/product-18.png',\r\n      reviewer: 'Emery Breitling',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      date: '12/1/2020',\r\n      status: 'Pending',\r\n      review: 5,\r\n      head: 'nec nisi vulputate',\r\n      para: 'Proin interdum mauris non ligula pellentesque ultrices. Phasellus id sapien in sapien iaculis congue. Vivamus metus arcu, adipiscing molestie, hendrerit at, vulputate vitae, nisl.'\r\n    },\r\n    {\r\n      id: 19,\r\n      product: 'Honiway Wall Mirror',\r\n      companyName: 'Decorative 12 inch Rustic Wood Mirror Sunburst Boho',\r\n      productImage: '/images/apps/ecommerce/product-19.png',\r\n      reviewer: 'Wilona Fields',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      date: '3/30/2020',\r\n      status: 'Published',\r\n      review: 1,\r\n      head: 'parturient montes nascetur ridiculus',\r\n      para: 'Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Proin risus. Praesent lectus.'\r\n    },\r\n    {\r\n      id: 20,\r\n      product: 'Tommaso Veloce Shoes',\r\n      companyName: 'Peloton Shoes Triathlon Road Bike Indoor Cycling',\r\n      productImage: '/images/apps/ecommerce/product-20.png',\r\n      reviewer: 'Janey Lamprecht',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      date: '9/16/2020',\r\n      status: 'Pending',\r\n      review: 1,\r\n      head: 'maecenas ut massa quis augue',\r\n      para: 'In quis justo. Maecenas rhoncus aliquam lacus. Morbi quis tortor id nulla ultrices aliquet.'\r\n    },\r\n    {\r\n      id: 21,\r\n      product: 'Zoolab',\r\n      companyName: 'Cruickshank-Jones',\r\n      productImage: '/images/apps/ecommerce/product-1.png',\r\n      reviewer: 'Rosene Walsh',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      date: '7/17/2021',\r\n      status: 'Published',\r\n      review: 1,\r\n      head: 'convallis nulla',\r\n      para: 'In sagittis dui vel nisl. Duis ac nibh. Fusce lacus purus, aliquet at, feugiat non, pretium quis, lectus.'\r\n    },\r\n    {\r\n      id: 22,\r\n      product: 'Viva',\r\n      companyName: 'Ferry Group',\r\n      productImage: '/images/apps/ecommerce/product-2.png',\r\n      reviewer: 'Buffy Sellen',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      date: '1/9/2021',\r\n      status: 'Pending',\r\n      review: 3,\r\n      head: 'nunc viverra dapibus',\r\n      para: 'Duis consequat dui nec nisi volutpat eleifend. Donec ut dolor. Morbi vel lectus in quam fringilla rhoncus.'\r\n    },\r\n    {\r\n      id: 23,\r\n      product: 'Transcof',\r\n      companyName: 'Bruen-Heathcote',\r\n      productImage: '/images/apps/ecommerce/product-3.png',\r\n      reviewer: 'Alvis Szymanzyk',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      date: '6/11/2020',\r\n      status: 'Pending',\r\n      review: 1,\r\n      head: 'nullam porttitor',\r\n      para: 'Vestibulum quam sapien, varius ut, blandit non, interdum in, ante. Vestibulum ante ipsum primis in faucibus orci luctus et ultrices posuere cubilia Curae; Duis faucibus accumsan odio. Curabitur convallis.'\r\n    },\r\n    {\r\n      id: 24,\r\n      product: 'Uerified',\r\n      companyName: 'Koch Group',\r\n      productImage: '/images/apps/ecommerce/product-4.png',\r\n      reviewer: 'Hatty Morsley',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      date: '2/12/2021',\r\n      status: 'Published',\r\n      review: 2,\r\n      head: 'metus sapien ut',\r\n      para: 'Cras mi pede, malesuada in, imperdiet et, commodo vulputate, justo. In blandit ultrices enim. Lorem ipsum dolor sit amet, consectetuer adipiscing elit.'\r\n    },\r\n    {\r\n      id: 25,\r\n      product: 'Y-find',\r\n      companyName: 'Emmerich and Sons',\r\n      productImage: '/images/apps/ecommerce/product-5.png',\r\n      reviewer: 'Jabez Pudner',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      date: '10/14/2021',\r\n      status: 'Pending',\r\n      review: 4,\r\n      head: 'orci luctus et',\r\n      para: 'Nulla ut erat id mauris vulputate elementum. Nullam varius. Nulla facilisi.'\r\n    },\r\n    {\r\n      id: 26,\r\n      product: 'Wigtax',\r\n      companyName: 'Zulauf-Prohaska',\r\n      productImage: '/images/apps/ecommerce/product-6.png',\r\n      reviewer: 'Ida Ovill',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      date: '11/18/2020',\r\n      status: 'Published',\r\n      review: 2,\r\n      head: 'vestibulum ante ipsum',\r\n      para: 'Duis consequat dui nec nisi volutpat eleifend. Donec ut dolor. Morbi vel lectus in quam fringilla rhoncus.'\r\n    },\r\n    {\r\n      id: 27,\r\n      product: 'Tempsoft',\r\n      companyName: 'VonRueden, Rogahn and Kris',\r\n      productImage: '/images/apps/ecommerce/product-7.png',\r\n      reviewer: 'Suzanne Breckin',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      date: '7/26/2020',\r\n      status: 'Published',\r\n      review: 2,\r\n      head: 'vel enim',\r\n      para: 'In hac habitasse platea dictumst. Morbi vestibulum, velit id pretium iaculis, diam erat fermentum justo, nec condimentum neque sapien placerat ante. Nulla justo.'\r\n    },\r\n    {\r\n      id: 28,\r\n      product: 'Rt',\r\n      companyName: \"Romaguera, O'Connell and Abernathy\",\r\n      productImage: '/images/apps/ecommerce/product-8.png',\r\n      reviewer: 'Morgana Coote',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      date: '8/29/2021',\r\n      status: 'Pending',\r\n      review: 5,\r\n      head: 'cubilia curae mauris',\r\n      para: 'Nullam porttitor lacus at turpis. Donec posuere metus vitae ipsum. Aliquam non mauris.'\r\n    },\r\n    {\r\n      id: 29,\r\n      product: 'Zontrax',\r\n      companyName: 'Mills, Hagenes and Bednar',\r\n      productImage: '/images/apps/ecommerce/product-9.png',\r\n      reviewer: 'Wesley Murra',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      date: '3/20/2021',\r\n      status: 'Pending',\r\n      review: 4,\r\n      head: 'gravida nisi at',\r\n      para: 'Integer tincidunt ante vel ipsum. Praesent blandit lacinia erat. Vestibulum sed magna at nunc commodo placerat.'\r\n    },\r\n    {\r\n      id: 30,\r\n      product: 'Keylex',\r\n      companyName: 'Sanford, Harvey and Parisian',\r\n      productImage: '/images/apps/ecommerce/product-10.png',\r\n      reviewer: 'Jobye Varnam',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      date: '11/24/2020',\r\n      status: 'Pending',\r\n      review: 2,\r\n      head: 'nec sem',\r\n      para: 'In sagittis dui vel nisl. Duis ac nibh. Fusce lacus purus, aliquet at, feugiat non, pretium quis, lectus.'\r\n    },\r\n    {\r\n      id: 31,\r\n      product: 'Trippledex',\r\n      companyName: 'Conroy-Bergstrom',\r\n      productImage: '/images/apps/ecommerce/product-11.png',\r\n      reviewer: \"Bibbye O'Dowd\",\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      date: '7/7/2020',\r\n      status: 'Published',\r\n      review: 5,\r\n      head: 'odio elementum eu',\r\n      para: 'Morbi non lectus. Aliquam sit amet diam in magna bibendum imperdiet. Nullam orci pede, venenatis non, sodales sed, tincidunt eu, felis.'\r\n    },\r\n    {\r\n      id: 32,\r\n      product: 'Opela',\r\n      companyName: 'Langosh Inc',\r\n      productImage: '/images/apps/ecommerce/product-12.png',\r\n      reviewer: 'Baldwin Bodimeade',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      date: '3/21/2020',\r\n      status: 'Published',\r\n      review: 2,\r\n      head: 'in imperdiet et commodo',\r\n      para: 'Morbi porttitor lorem id ligula. Suspendisse ornare consequat lectus. In est risus, auctor sed, tristique in, tempus sit amet, sem.'\r\n    },\r\n    {\r\n      id: 33,\r\n      product: 'Span',\r\n      companyName: 'Jerde-Walsh',\r\n      productImage: '/images/apps/ecommerce/product-13.png',\r\n      reviewer: 'Rozalin Allan',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      date: '1/23/2020',\r\n      status: 'Published',\r\n      review: 4,\r\n      head: 'pellentesque at',\r\n      para: 'Cras non velit nec nisi vulputate nonummy. Maecenas tincidunt lacus at velit. Vivamus vel nulla eget eros elementum pellentesque.'\r\n    },\r\n    {\r\n      id: 34,\r\n      product: 'Rank',\r\n      companyName: 'Barrows, Quitzon and Roberts',\r\n      productImage: '/images/apps/ecommerce/product-14.png',\r\n      reviewer: 'Patsy Bowlas',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      date: '10/7/2020',\r\n      status: 'Pending',\r\n      review: 5,\r\n      head: 'congue etiam',\r\n      para: 'Cras non velit nec nisi vulputate nonummy. Maecenas tincidunt lacus at velit. Vivamus vel nulla eget eros elementum pellentesque.'\r\n    },\r\n    {\r\n      id: 35,\r\n      product: 'Tempsoft',\r\n      companyName: 'Russel-Grant',\r\n      productImage: '/images/apps/ecommerce/product-15.png',\r\n      reviewer: 'Zsazsa Jansens',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      date: '8/7/2021',\r\n      status: 'Pending',\r\n      review: 1,\r\n      head: 'eget eros',\r\n      para: 'In hac habitasse platea dictumst. Etiam faucibus cursus urna. Ut tellus.'\r\n    },\r\n    {\r\n      id: 36,\r\n      product: 'Ventosanzap',\r\n      companyName: \"O'Conner-Zboncak\",\r\n      productImage: '/images/apps/ecommerce/product-16.png',\r\n      reviewer: 'Denny MacGettigen',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      date: '2/17/2020',\r\n      status: 'Published',\r\n      review: 1,\r\n      head: 'vel dapibus',\r\n      para: 'Phasellus sit amet erat. Nulla tempus. Vivamus in felis eu sapien cursus vestibulum.'\r\n    },\r\n    {\r\n      id: 37,\r\n      product: 'Mat Lam Tam',\r\n      companyName: 'Rutherford, Heller and Bashirian',\r\n      productImage: '/images/apps/ecommerce/product-17.png',\r\n      reviewer: 'Leia Braunroth',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      date: '1/28/2021',\r\n      status: 'Published',\r\n      review: 4,\r\n      head: 'sit amet consectetuer',\r\n      para: 'Quisque id justo sit amet sapien dignissim vestibulum. Vestibulum ante ipsum primis in faucibus orci luctus et ultrices posuere cubilia Curae; Nulla dapibus dolor vel est. Donec odio justo, sollicitudin ut, suscipit a, feugiat et, eros.'\r\n    },\r\n    {\r\n      id: 38,\r\n      product: 'Zamit',\r\n      companyName: 'Swift-Altenwerth',\r\n      productImage: '/images/apps/ecommerce/product-18.png',\r\n      reviewer: 'Nil Vasilic',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      date: '1/2/2020',\r\n      status: 'Published',\r\n      review: 1,\r\n      head: 'blandit non',\r\n      para: 'Maecenas ut massa quis augue luctus tincidunt. Nulla mollis molestie lorem. Quisque ut erat.'\r\n    },\r\n    {\r\n      id: 39,\r\n      product: 'Tresom',\r\n      companyName: \"O'Kon, Waelchi and Lesch\",\r\n      productImage: '/images/apps/ecommerce/product-19.png',\r\n      reviewer: 'Mandie Forseith',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      date: '7/2/2020',\r\n      status: 'Published',\r\n      review: 1,\r\n      head: 'in ante vestibulum ante',\r\n      para: 'Aenean lectus. Pellentesque eget nunc. Donec quis orci eget orci vehicula condimentum.'\r\n    },\r\n    {\r\n      id: 40,\r\n      product: 'Viva',\r\n      companyName: 'Johnston, Anderson and Metz',\r\n      productImage: '/images/apps/ecommerce/product-20.png',\r\n      reviewer: 'Audra Pinks',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      date: '1/6/2020',\r\n      status: 'Pending',\r\n      review: 2,\r\n      head: 'ante ipsum primis in',\r\n      para: 'Quisque id justo sit amet sapien dignissim vestibulum. Vestibulum ante ipsum primis in faucibus orci luctus et ultrices posuere cubilia Curae; Nulla dapibus dolor vel est. Donec odio justo, sollicitudin ut, suscipit a, feugiat et, eros.'\r\n    },\r\n    {\r\n      id: 41,\r\n      product: 'Matsoft',\r\n      companyName: \"O'Conner, Paucek and Braun\",\r\n      productImage: '/images/apps/ecommerce/product-1.png',\r\n      reviewer: 'Damita Jarad',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      date: '10/30/2021',\r\n      status: 'Pending',\r\n      review: 4,\r\n      head: 'mus etiam vel augue',\r\n      para: 'Vestibulum ac est lacinia nisi venenatis tristique. Fusce congue, diam id ornare imperdiet, sapien urna pretium nisl, ut volutpat sapien arcu sed augue. Aliquam erat volutpat.'\r\n    },\r\n    {\r\n      id: 42,\r\n      product: 'Wiodex',\r\n      companyName: 'Wisoky-Kassulke',\r\n      productImage: '/images/apps/ecommerce/product-2.png',\r\n      reviewer: 'Fowler Drury',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      date: '2/11/2020',\r\n      status: 'Published',\r\n      review: 3,\r\n      head: 'dictumst aliquam augue quam',\r\n      para: 'Nam ultrices, libero non mattis pulvinar, nulla pede ullamcorper augue, a suscipit nulla elit ac nulla. Sed vel enim sit amet nunc viverra dapibus. Nulla suscipit ligula in lacus.'\r\n    },\r\n    {\r\n      id: 43,\r\n      product: 'Keylex',\r\n      companyName: 'Haag, Bruen and Reichel',\r\n      productImage: '/images/apps/ecommerce/product-3.png',\r\n      reviewer: 'Anette Jouen',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      date: '12/11/2020',\r\n      status: 'Published',\r\n      review: 3,\r\n      head: 'mauris non ligula pellentesque ultrices',\r\n      para: 'Cras non velit nec nisi vulputate nonummy. Maecenas tincidunt lacus at velit. Vivamus vel nulla eget eros elementum pellentesque.'\r\n    },\r\n    {\r\n      id: 44,\r\n      product: 'Konklux',\r\n      companyName: 'Ankunding Inc',\r\n      productImage: '/images/apps/ecommerce/product-4.png',\r\n      reviewer: 'Candace Fossey',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      date: '2/10/2021',\r\n      status: 'Pending',\r\n      review: 1,\r\n      head: 'vel augue vestibulum ante',\r\n      para: 'Cras non velit nec nisi vulputate nonummy. Maecenas tincidunt lacus at velit. Vivamus vel nulla eget eros elementum pellentesque.'\r\n    },\r\n    {\r\n      id: 45,\r\n      product: 'Tresom',\r\n      companyName: 'Deckow and Sons',\r\n      productImage: '/images/apps/ecommerce/product-5.png',\r\n      reviewer: 'Persis Loades',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      date: '9/11/2020',\r\n      status: 'Pending',\r\n      review: 5,\r\n      head: 'convallis nulla neque',\r\n      para: 'Cras non velit nec nisi vulputate nonummy. Maecenas tincidunt lacus at velit. Vivamus vel nulla eget eros elementum pellentesque.'\r\n    },\r\n    {\r\n      id: 46,\r\n      product: 'Quo Lux',\r\n      companyName: 'Kreiger, Reynolds and Sporer',\r\n      productImage: '/images/apps/ecommerce/product-1.png',\r\n      reviewer: 'Kim Carrel',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      date: '5/26/2020',\r\n      status: 'Pending',\r\n      review: 3,\r\n      head: 'quam turpis adipiscing lorem',\r\n      para: 'In hac habitasse platea dictumst. Morbi vestibulum, velit id pretium iaculis, diam erat fermentum justo, nec condimentum neque sapien placerat ante. Nulla justo.'\r\n    },\r\n    {\r\n      id: 47,\r\n      product: 'Roldlamis',\r\n      companyName: 'Kuphal-Abbott',\r\n      productImage: '/images/apps/ecommerce/product-6.png',\r\n      reviewer: 'Rodger Broz',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      date: '10/5/2020',\r\n      status: 'Pending',\r\n      review: 1,\r\n      head: 'morbi non',\r\n      para: 'Nullam porttitor lacus at turpis. Donec posuere metus vitae ipsum. Aliquam non mauris.'\r\n    },\r\n    {\r\n      id: 48,\r\n      product: 'Tampflex',\r\n      companyName: 'Romaguera, Schmeler and Volkman',\r\n      productImage: '/images/apps/ecommerce/product-7.png',\r\n      reviewer: 'Lauri Shearsby',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      date: '10/18/2020',\r\n      status: 'Pending',\r\n      review: 5,\r\n      head: 'vel dapibus at diam',\r\n      para: 'Cum sociis natoque penatibus et magnis dis parturient montes, nascetur ridiculus mus. Vivamus vestibulum sagittis sapien. Cum sociis natoque penatibus et magnis dis parturient montes, nascetur ridiculus mus.'\r\n    },\r\n    {\r\n      id: 49,\r\n      product: 'Span',\r\n      companyName: 'Hane-Romaguera',\r\n      productImage: '/images/apps/ecommerce/product-8.png',\r\n      reviewer: 'Hannah Drohun',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      date: '9/14/2020',\r\n      status: 'Pending',\r\n      review: 4,\r\n      head: 'morbi porttitor lorem',\r\n      para: 'Integer ac leo. Pellentesque ultrices mattis odio. Donec vitae nisi.'\r\n    },\r\n    {\r\n      id: 50,\r\n      product: 'Zamit',\r\n      companyName: 'Hoeger-Powlowski',\r\n      productImage: '/images/apps/ecommerce/product-9.png',\r\n      reviewer: 'Celesta Hadden',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      date: '4/15/2020',\r\n      status: 'Published',\r\n      review: 5,\r\n      head: 'non sodales',\r\n      para: 'Morbi non lectus. Aliquam sit amet diam in magna bibendum imperdiet. Nullam orci pede, venenatis non, sodales sed, tincidunt eu, felis.'\r\n    },\r\n    {\r\n      id: 51,\r\n      product: 'Witchip',\r\n      companyName: 'Heidenreich, Keeling and Kuhn',\r\n      productImage: '/images/apps/ecommerce/product-10.png',\r\n      reviewer: 'Sollie Dowling',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      date: '9/7/2020',\r\n      status: 'Pending',\r\n      review: 1,\r\n      head: 'nam congue risus semper porta',\r\n      para: 'Pellentesque at nulla. Suspendisse potenti. Cras in purus eu magna vulputate luctus.'\r\n    },\r\n    {\r\n      id: 52,\r\n      product: 'Ratity',\r\n      companyName: 'Beier and Sons',\r\n      productImage: '/images/apps/ecommerce/product-11.png',\r\n      reviewer: 'Esma Tamsett',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      date: '12/21/2020',\r\n      status: 'Pending',\r\n      review: 2,\r\n      head: 'rutrum rutrum neque aenean auctor',\r\n      para: 'In quis justo. Maecenas rhoncus aliquam lacus. Morbi quis tortor id nulla ultrices aliquet.'\r\n    },\r\n    {\r\n      id: 53,\r\n      product: 'Voltsillam',\r\n      companyName: 'Jones and Sons',\r\n      productImage: '/images/apps/ecommerce/product-12.png',\r\n      reviewer: 'Fee Pieche',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      date: '4/28/2020',\r\n      status: 'Pending',\r\n      review: 5,\r\n      head: 'non mi',\r\n      para: 'Duis bibendum. Morbi non quam nec dui luctus rutrum. Nulla tellus.'\r\n    },\r\n    {\r\n      id: 54,\r\n      product: 'Voltsillam',\r\n      companyName: 'Mohr and Sons',\r\n      productImage: '/images/apps/ecommerce/product-13.png',\r\n      reviewer: 'Frankie Davis',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      date: '3/16/2021',\r\n      status: 'Published',\r\n      review: 5,\r\n      head: 'maecenas pulvinar lobortis est phasellus',\r\n      para: 'In sagittis dui vel nisl. Duis ac nibh. Fusce lacus purus, aliquet at, feugiat non, pretium quis, lectus.'\r\n    },\r\n    {\r\n      id: 55,\r\n      product: 'Matsoft',\r\n      companyName: 'Kling-Hayes',\r\n      productImage: '/images/apps/ecommerce/product-1.png',\r\n      reviewer: 'Byram Wimlet',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      date: '7/13/2021',\r\n      status: 'Pending',\r\n      review: 2,\r\n      head: 'tortor sollicitudin',\r\n      para: 'Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Proin risus. Praesent lectus.'\r\n    },\r\n    {\r\n      id: 56,\r\n      product: 'Rt',\r\n      companyName: 'Brekke-Lubowitz',\r\n      productImage: '/images/apps/ecommerce/product-14.png',\r\n      reviewer: 'Maurita Hutchin',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      date: '11/11/2020',\r\n      status: 'Pending',\r\n      review: 1,\r\n      head: 'odio cras mi pede malesuada',\r\n      para: 'Integer tincidunt ante vel ipsum. Praesent blandit lacinia erat. Vestibulum sed magna at nunc commodo placerat.'\r\n    },\r\n    {\r\n      id: 57,\r\n      product: 'Konklab',\r\n      companyName: 'Kiehn LLC',\r\n      productImage: '/images/apps/ecommerce/product-15.png',\r\n      reviewer: 'Gorden Leftley',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      date: '9/19/2021',\r\n      status: 'Published',\r\n      review: 3,\r\n      head: 'sed nisl nunc rhoncus',\r\n      para: 'Duis bibendum, felis sed interdum venenatis, turpis enim blandit mi, in porttitor pede justo eu massa. Donec dapibus. Duis at velit eu est congue elementum.'\r\n    },\r\n    {\r\n      id: 58,\r\n      product: 'Lotstring',\r\n      companyName: 'Windler-Corwin',\r\n      productImage: '/images/apps/ecommerce/product-16.png',\r\n      reviewer: 'Raviv Critcher',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      date: '4/20/2020',\r\n      status: 'Published',\r\n      review: 5,\r\n      head: 'bibendum imperdiet nullam orci',\r\n      para: 'Proin interdum mauris non ligula pellentesque ultrices. Phasellus id sapien in sapien iaculis congue. Vivamus metus arcu, adipiscing molestie, hendrerit at, vulputate vitae, nisl.'\r\n    },\r\n    {\r\n      id: 59,\r\n      product: 'Keylex',\r\n      companyName: 'Reynolds, Buckridge and Schmeler',\r\n      productImage: '/images/apps/ecommerce/product-17.png',\r\n      reviewer: 'Cinda Tersay',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      date: '3/31/2021',\r\n      status: 'Published',\r\n      review: 4,\r\n      head: 'curabitur at',\r\n      para: 'Phasellus sit amet erat. Nulla tempus. Vivamus in felis eu sapien cursus vestibulum.'\r\n    },\r\n    {\r\n      id: 60,\r\n      product: 'Transcof',\r\n      companyName: 'Jacobs-Farrell',\r\n      productImage: '/images/apps/ecommerce/product-18.png',\r\n      reviewer: 'Raynell Rosenauer',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      date: '6/3/2020',\r\n      status: 'Published',\r\n      review: 2,\r\n      head: 'massa donec dapibus duis at',\r\n      para: 'Aenean lectus. Pellentesque eget nunc. Donec quis orci eget orci vehicula condimentum.'\r\n    },\r\n    {\r\n      id: 61,\r\n      product: 'Opela',\r\n      companyName: 'Beier-Bergstrom',\r\n      productImage: '/images/apps/ecommerce/product-19.png',\r\n      reviewer: 'Aurelia Cooley',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      date: '7/27/2020',\r\n      status: 'Pending',\r\n      review: 1,\r\n      head: 'dictumst maecenas',\r\n      para: 'Praesent id massa id nisl venenatis lacinia. Aenean sit amet justo. Morbi ut odio.'\r\n    },\r\n    {\r\n      id: 62,\r\n      product: 'Rlowdesk',\r\n      companyName: 'Roob and Sons',\r\n      productImage: '/images/apps/ecommerce/product-20.png',\r\n      reviewer: 'Silvester Vittel',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      date: '3/2/2021',\r\n      status: 'Pending',\r\n      review: 5,\r\n      head: 'elit ac nulla',\r\n      para: 'Suspendisse potenti. In eleifend quam a odio. In hac habitasse platea dictumst.'\r\n    },\r\n    {\r\n      id: 63,\r\n      product: 'Kanlam',\r\n      companyName: 'Hauck Group',\r\n      productImage: '/images/apps/ecommerce/product-2.png',\r\n      reviewer: 'Nester Oliffe',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      date: '3/31/2021',\r\n      status: 'Published',\r\n      review: 4,\r\n      head: 'sagittis nam congue',\r\n      para: 'Duis bibendum. Morbi non quam nec dui luctus rutrum. Nulla tellus.'\r\n    },\r\n    {\r\n      id: 64,\r\n      product: 'Rembucket',\r\n      companyName: 'Reynolds-Lindgren',\r\n      productImage: '/images/apps/ecommerce/product-2.png',\r\n      reviewer: 'Cheryl Growcott',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      date: '10/29/2021',\r\n      status: 'Pending',\r\n      review: 4,\r\n      head: 'amet diam in magna bibendum',\r\n      para: 'Proin leo odio, porttitor id, consequat in, consequat ut, nulla. Sed accumsan felis. Ut at dolor quis odio consequat varius.'\r\n    },\r\n    {\r\n      id: 65,\r\n      product: 'Tin',\r\n      companyName: 'Stroman and Sons',\r\n      productImage: '/images/apps/ecommerce/product-2.png',\r\n      reviewer: 'Calhoun Perot',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      date: '10/15/2020',\r\n      status: 'Published',\r\n      review: 4,\r\n      head: 'enim blandit mi',\r\n      para: 'Pellentesque at nulla. Suspendisse potenti. Cras in purus eu magna vulputate luctus.'\r\n    },\r\n    {\r\n      id: 66,\r\n      product: 'Trippledex',\r\n      companyName: 'Kihn-Wisoky',\r\n      productImage: '/images/apps/ecommerce/product-2.png',\r\n      reviewer: 'Winnah Tivenan',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      date: '5/27/2021',\r\n      status: 'Published',\r\n      review: 3,\r\n      head: 'pede ullamcorper augue a suscipit',\r\n      para: 'Quisque porta volutpat erat. Quisque erat eros, viverra eget, congue eget, semper rutrum, nulla. Nunc purus.'\r\n    },\r\n    {\r\n      id: 67,\r\n      product: 'Redhold',\r\n      companyName: 'Konopelski-Hauck',\r\n      productImage: '/images/apps/ecommerce/product-2.png',\r\n      reviewer: 'Faydra Perceval',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      date: '10/2/2020',\r\n      status: 'Published',\r\n      review: 2,\r\n      head: 'porta volutpat',\r\n      para: 'In congue. Etiam justo. Etiam pretium iaculis justo.'\r\n    },\r\n    {\r\n      id: 68,\r\n      product: 'Pannier',\r\n      companyName: 'Rau Inc',\r\n      productImage: '/images/apps/ecommerce/product-3.png',\r\n      reviewer: 'Shauna Runge',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      date: '12/17/2021',\r\n      status: 'Published',\r\n      review: 3,\r\n      head: 'aliquam lacus morbi quis tortor',\r\n      para: 'Curabitur in libero ut massa volutpat convallis. Morbi odio odio, elementum eu, interdum eu, tincidunt in, leo. Maecenas pulvinar lobortis est.'\r\n    },\r\n    {\r\n      id: 69,\r\n      product: 'Rlexidy',\r\n      companyName: 'Torp-Lebsack',\r\n      productImage: '/images/apps/ecommerce/product-3.png',\r\n      reviewer: 'Udell Laurand',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      date: '3/14/2021',\r\n      status: 'Pending',\r\n      review: 5,\r\n      head: 'vestibulum velit id pretium',\r\n      para: 'Nullam porttitor lacus at turpis. Donec posuere metus vitae ipsum. Aliquam non mauris.'\r\n    },\r\n    {\r\n      id: 70,\r\n      product: 'Keylex',\r\n      companyName: 'Hane-Bednar',\r\n      productImage: '/images/apps/ecommerce/product-3.png',\r\n      reviewer: 'Charyl Mordaunt',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      date: '4/11/2021',\r\n      status: 'Pending',\r\n      review: 1,\r\n      head: 'amet eros suspendisse accumsan tortor',\r\n      para: 'Praesent blandit. Nam nulla. Integer pede justo, lacinia eget, tincidunt eget, tempus vel, pede.'\r\n    },\r\n    {\r\n      id: 71,\r\n      product: 'Kuobam',\r\n      companyName: 'Rice Group',\r\n      productImage: '/images/apps/ecommerce/product-3.png',\r\n      reviewer: 'Becki Petit',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      date: '8/9/2021',\r\n      status: 'Published',\r\n      review: 5,\r\n      head: 'blandit lacinia erat vestibulum sed',\r\n      para: 'Praesent blandit. Nam nulla. Integer pede justo, lacinia eget, tincidunt eget, tempus vel, pede.'\r\n    },\r\n    {\r\n      id: 72,\r\n      product: 'Ulphazap',\r\n      companyName: 'West, White and Rau',\r\n      productImage: '/images/apps/ecommerce/product-3.png',\r\n      reviewer: 'Gallagher Goldes',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      date: '10/21/2020',\r\n      status: 'Pending',\r\n      review: 4,\r\n      head: 'vitae ipsum aliquam',\r\n      para: 'Fusce posuere felis sed lacus. Morbi sem mauris, laoreet ut, rhoncus aliquet, pulvinar sed, nisl. Nunc rhoncus dui vel sem.'\r\n    },\r\n    {\r\n      id: 73,\r\n      product: 'Wiodex',\r\n      companyName: 'Keeling-Dicki',\r\n      productImage: '/images/apps/ecommerce/product-4.png',\r\n      reviewer: 'Gunilla Painter',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      date: '12/11/2021',\r\n      status: 'Published',\r\n      review: 4,\r\n      head: 'tortor duis mattis egestas',\r\n      para: 'Morbi non lectus. Aliquam sit amet diam in magna bibendum imperdiet. Nullam orci pede, venenatis non, sodales sed, tincidunt eu, felis.'\r\n    },\r\n    {\r\n      id: 74,\r\n      product: 'Veribet',\r\n      companyName: 'Gerlach, Bernier and Jenkins',\r\n      productImage: '/images/apps/ecommerce/product-4.png',\r\n      reviewer: 'Greggory Illingworth',\r\n      email: 'gillingworth21@lis',\r\n      avatar: '/images/avatars/1.png',\r\n      date: '8/8/2020',\r\n      status: 'Pending',\r\n      review: 4,\r\n      head: 'pede justo lacinia eget tincidunt',\r\n      para: 'Pellentesque at nulla. Suspendisse potenti. Cras in purus eu magna vulputate luctus.'\r\n    },\r\n    {\r\n      id: 75,\r\n      product: 'Rix San',\r\n      companyName: 'Kessler and Sons',\r\n      productImage: '/images/apps/ecommerce/product-4.png',\r\n      reviewer: 'Amabel Reah',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      date: '11/22/2021',\r\n      status: 'Published',\r\n      review: 3,\r\n      head: 'sit amet lobortis sapien',\r\n      para: 'In hac habitasse platea dictumst. Morbi vestibulum, velit id pretium iaculis, diam erat fermentum justo, nec condimentum neque sapien placerat ante. Nulla justo.'\r\n    },\r\n    {\r\n      id: 76,\r\n      product: 'Zoolab',\r\n      companyName: 'Goldner, Lind and Hansen',\r\n      productImage: '/images/apps/ecommerce/product-4.png',\r\n      reviewer: 'Eziechiele Littlejohns',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      date: '8/17/2020',\r\n      status: 'Pending',\r\n      review: 4,\r\n      head: 'cras non velit',\r\n      para: 'Nullam porttitor lacus at turpis. Donec posuere metus vitae ipsum. Aliquam non mauris.'\r\n    },\r\n    {\r\n      id: 77,\r\n      product: 'Rob',\r\n      companyName: 'Trantow Group',\r\n      productImage: '/images/apps/ecommerce/product-4.png',\r\n      reviewer: 'Faythe Hance',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      date: '7/1/2021',\r\n      status: 'Published',\r\n      review: 5,\r\n      head: 'luctus tincidunt nulla mollis molestie',\r\n      para: 'In quis justo. Maecenas rhoncus aliquam lacus. Morbi quis tortor id nulla ultrices aliquet.'\r\n    },\r\n    {\r\n      id: 78,\r\n      product: 'Zamit',\r\n      companyName: 'Reichel, Hagenes and Nader',\r\n      productImage: '/images/apps/ecommerce/product-5.png',\r\n      reviewer: 'Marie Hazelton',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      date: '5/31/2021',\r\n      status: 'Published',\r\n      review: 1,\r\n      head: 'ut odio cras',\r\n      para: 'Aliquam quis turpis eget elit sodales scelerisque. Mauris sit amet eros. Suspendisse accumsan tortor quis turpis.'\r\n    },\r\n    {\r\n      id: 79,\r\n      product: 'Zoolab',\r\n      companyName: 'Baumbach-Renner',\r\n      productImage: '/images/apps/ecommerce/product-5.png',\r\n      reviewer: 'Vincenz Izsak',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      date: '3/15/2021',\r\n      status: 'Pending',\r\n      review: 2,\r\n      head: 'gravida sem',\r\n      para: 'Proin interdum mauris non ligula pellentesque ultrices. Phasellus id sapien in sapien iaculis congue. Vivamus metus arcu, adipiscing molestie, hendrerit at, vulputate vitae, nisl.'\r\n    },\r\n    {\r\n      id: 80,\r\n      product: 'Stronghold',\r\n      companyName: 'Ullrich, Jacobson and Tillman',\r\n      productImage: '/images/apps/ecommerce/product-5.png',\r\n      reviewer: 'Roch Dehmel',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      date: '4/21/2020',\r\n      status: 'Pending',\r\n      review: 3,\r\n      head: 'ligula pellentesque ultrices phasellus',\r\n      para: 'Sed ante. Vivamus tortor. Duis mattis egestas metus.'\r\n    },\r\n    {\r\n      id: 81,\r\n      product: 'Rintone',\r\n      companyName: 'VonRueden, Kuphal and Lindgren',\r\n      productImage: '/images/apps/ecommerce/product-5.png',\r\n      reviewer: 'Marylin Thewlis',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      date: '5/26/2021',\r\n      status: 'Pending',\r\n      review: 1,\r\n      head: 'elementum nullam varius nulla',\r\n      para: 'In sagittis dui vel nisl. Duis ac nibh. Fusce lacus purus, aliquet at, feugiat non, pretium quis, lectus.'\r\n    },\r\n    {\r\n      id: 82,\r\n      product: 'Temp',\r\n      companyName: 'Wintheiser, Bergstrom and Schimmel',\r\n      productImage: '/images/apps/ecommerce/product-5.png',\r\n      reviewer: 'Annissa Mapham',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      date: '6/10/2021',\r\n      status: 'Published',\r\n      review: 4,\r\n      head: 'odio porttitor',\r\n      para: 'Cras mi pede, malesuada in, imperdiet et, commodo vulputate, justo. In blandit ultrices enim. Lorem ipsum dolor sit amet, consectetuer adipiscing elit.'\r\n    },\r\n    {\r\n      id: 83,\r\n      product: 'Rlexidy',\r\n      companyName: 'Kuhn and Sons',\r\n      productImage: '/images/apps/ecommerce/product-6.png',\r\n      reviewer: 'Lori Prosek',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      date: '7/16/2021',\r\n      status: 'Published',\r\n      review: 1,\r\n      head: 'lacinia sapien quis',\r\n      para: 'Suspendisse potenti. In eleifend quam a odio. In hac habitasse platea dictumst.'\r\n    },\r\n    {\r\n      id: 84,\r\n      product: 'Ronstring',\r\n      companyName: 'Goldner, Nitzsche and Rau',\r\n      productImage: '/images/apps/ecommerce/product-6.png',\r\n      reviewer: 'Zelma Jado',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      date: '7/13/2021',\r\n      status: 'Pending',\r\n      review: 4,\r\n      head: 'mauris sit amet eros suspendisse',\r\n      para: 'In hac habitasse platea dictumst. Etiam faucibus cursus urna. Ut tellus.'\r\n    },\r\n    {\r\n      id: 85,\r\n      product: 'Rixflex',\r\n      companyName: 'Bayer-Beer',\r\n      productImage: '/images/apps/ecommerce/product-6.png',\r\n      reviewer: 'Alfreda Tuffley',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      date: '3/25/2020',\r\n      status: 'Pending',\r\n      review: 2,\r\n      head: 'molestie hendrerit at vulputate vitae',\r\n      para: 'Aliquam quis turpis eget elit sodales scelerisque. Mauris sit amet eros. Suspendisse accumsan tortor quis turpis.'\r\n    },\r\n    {\r\n      id: 86,\r\n      product: 'Uerified',\r\n      companyName: 'Rolfson-Witting',\r\n      productImage: '/images/apps/ecommerce/product-6.png',\r\n      reviewer: 'Arnold Rate',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      date: '7/22/2021',\r\n      status: 'Pending',\r\n      review: 1,\r\n      head: 'nisi venenatis tristique fusce',\r\n      para: 'Phasellus in felis. Donec semper sapien a libero. Nam dui.'\r\n    },\r\n    {\r\n      id: 87,\r\n      product: 'Stringtough',\r\n      companyName: 'Kunde-Gibson',\r\n      productImage: '/images/apps/ecommerce/product-7.png',\r\n      reviewer: 'Felizio Macieiczyk',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      date: '8/27/2020',\r\n      status: 'Published',\r\n      review: 4,\r\n      head: 'augue quam sollicitudin',\r\n      para: 'Duis consequat dui nec nisi volutpat eleifend. Donec ut dolor. Morbi vel lectus in quam fringilla rhoncus.'\r\n    },\r\n    {\r\n      id: 88,\r\n      product: 'Qookley',\r\n      companyName: 'Kshlerin-Klocko',\r\n      productImage: '/images/apps/ecommerce/product-7.png',\r\n      reviewer: 'Evanne Chamley',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      date: '2/1/2021',\r\n      status: 'Pending',\r\n      review: 4,\r\n      head: 'eget tincidunt',\r\n      para: 'Curabitur gravida nisi at nibh. In hac habitasse platea dictumst. Aliquam augue quam, sollicitudin vitae, consectetuer eget, rutrum at, lorem.'\r\n    },\r\n    {\r\n      id: 89,\r\n      product: 'Zamit',\r\n      companyName: 'Reilly, Marvin and Ondricka',\r\n      productImage: '/images/apps/ecommerce/product-7.png',\r\n      reviewer: 'Dacy Goodlatt',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      date: '4/15/2021',\r\n      status: 'Pending',\r\n      review: 4,\r\n      head: 'eu interdum eu',\r\n      para: 'In congue. Etiam justo. Etiam pretium iaculis justo.'\r\n    },\r\n    {\r\n      id: 90,\r\n      product: 'Mat Lam Tam',\r\n      companyName: 'Ratke-Bauch',\r\n      productImage: '/images/apps/ecommerce/product-7.png',\r\n      reviewer: 'Samantha Vickerman',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      date: '6/30/2021',\r\n      status: 'Pending',\r\n      review: 3,\r\n      head: 'leo rhoncus sed vestibulum',\r\n      para: 'Sed ante. Vivamus tortor. Duis mattis egestas metus.'\r\n    },\r\n    {\r\n      id: 91,\r\n      product: 'Rt',\r\n      companyName: 'Kautzer-Hayes',\r\n      productImage: '/images/apps/ecommerce/product-8.png',\r\n      reviewer: 'Maura Robichon',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      date: '4/12/2020',\r\n      status: 'Published',\r\n      review: 3,\r\n      head: 'dui maecenas',\r\n      para: 'Praesent blandit. Nam nulla. Integer pede justo, lacinia eget, tincidunt eget, tempus vel, pede.'\r\n    },\r\n    {\r\n      id: 92,\r\n      product: 'Stim',\r\n      companyName: 'Bernhard and Sons',\r\n      productImage: '/images/apps/ecommerce/product-8.png',\r\n      reviewer: 'Shelton Bonde',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      date: '6/1/2021',\r\n      status: 'Pending',\r\n      review: 1,\r\n      head: 'odio elementum',\r\n      para: 'Curabitur gravida nisi at nibh. In hac habitasse platea dictumst. Aliquam augue quam, sollicitudin vitae, consectetuer eget, rutrum at, lorem.'\r\n    },\r\n    {\r\n      id: 93,\r\n      product: 'Rix San',\r\n      companyName: 'Waters, Harvey and Stiedemann',\r\n      productImage: '/images/apps/ecommerce/product-8.png',\r\n      reviewer: 'Hallsy Flannigan',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      date: '6/3/2020',\r\n      status: 'Published',\r\n      review: 5,\r\n      head: 'ultrices phasellus id',\r\n      para: 'Curabitur in libero ut massa volutpat convallis. Morbi odio odio, elementum eu, interdum eu, tincidunt in, leo. Maecenas pulvinar lobortis est.'\r\n    },\r\n    {\r\n      id: 94,\r\n      product: 'Vagram',\r\n      companyName: 'Ondricka, Thompson and Kuhn',\r\n      productImage: '/images/apps/ecommerce/product-8.png',\r\n      reviewer: 'Rheta Chazelas',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      date: '2/21/2021',\r\n      status: 'Pending',\r\n      review: 1,\r\n      head: 'eleifend quam',\r\n      para: 'Praesent blandit. Nam nulla. Integer pede justo, lacinia eget, tincidunt eget, tempus vel, pede.'\r\n    },\r\n    {\r\n      id: 95,\r\n      product: 'Otcom',\r\n      companyName: 'Volkman Group',\r\n      productImage: '/images/apps/ecommerce/product-9.png',\r\n      reviewer: 'Arabelle Uc',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      date: '1/27/2021',\r\n      status: 'Published',\r\n      review: 4,\r\n      head: 'fermentum justo',\r\n      para: 'In hac habitasse platea dictumst. Etiam faucibus cursus urna. Ut tellus.'\r\n    },\r\n    {\r\n      id: 96,\r\n      product: 'Rixflex',\r\n      companyName: 'Dickinson, Spencer and Hyatt',\r\n      productImage: '/images/apps/ecommerce/product-9.png',\r\n      reviewer: 'Pauly Goulden',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      date: '10/2/2020',\r\n      status: 'Pending',\r\n      review: 2,\r\n      head: 'morbi ut',\r\n      para: 'Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Proin risus. Praesent lectus.'\r\n    },\r\n    {\r\n      id: 97,\r\n      product: 'Lotstring',\r\n      companyName: 'Marvin Inc',\r\n      productImage: '/images/apps/ecommerce/product-9.png',\r\n      reviewer: 'Wilhelmina Benezet',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      date: '8/12/2021',\r\n      status: 'Pending',\r\n      review: 4,\r\n      head: 'sapien cursus vestibulum proin',\r\n      para: 'Aenean lectus. Pellentesque eget nunc. Donec quis orci eget orci vehicula condimentum.'\r\n    },\r\n    {\r\n      id: 98,\r\n      product: 'Wiodex',\r\n      companyName: 'Hayes-Greenholt',\r\n      productImage: '/images/apps/ecommerce/product-9.png',\r\n      reviewer: 'Wallie Paolone',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      date: '7/15/2021',\r\n      status: 'Published',\r\n      review: 2,\r\n      head: 'tincidunt in leo maecenas',\r\n      para: 'Cras non velit nec nisi vulputate nonummy. Maecenas tincidunt lacus at velit. Vivamus vel nulla eget eros elementum pellentesque.'\r\n    },\r\n    {\r\n      id: 99,\r\n      product: 'Komainer',\r\n      companyName: 'Gislason, Greenfelder and Wisozk',\r\n      productImage: '/images/apps/ecommerce/product-10.png',\r\n      reviewer: 'Ezmeralda Normavill',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      date: '8/4/2021',\r\n      status: 'Pending',\r\n      review: 3,\r\n      head: 'pharetra magna ac',\r\n      para: 'In quis justo. Maecenas rhoncus aliquam lacus. Morbi quis tortor id nulla ultrices aliquet.'\r\n    },\r\n    {\r\n      id: 100,\r\n      product: 'Ulpha',\r\n      companyName: 'Kunde Group',\r\n      productImage: '/images/apps/ecommerce/product-10.png',\r\n      reviewer: 'Lew Dudman',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      date: '11/12/2020',\r\n      status: 'Published',\r\n      review: 3,\r\n      head: 'suscipit ligula in lacus',\r\n      para: 'In quis justo. Maecenas rhoncus aliquam lacus. Morbi quis tortor id nulla ultrices aliquet.'\r\n    }\r\n  ],\r\n  referrals: [\r\n    {\r\n      id: 1,\r\n      user: 'Koressa Leyfield',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      referredId: 3398,\r\n      status: 'Unpaid',\r\n      value: '$6655.92',\r\n      earning: '$380.17'\r\n    },\r\n    {\r\n      id: 2,\r\n      user: 'Tania Brotherhood',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/5.png',\r\n      referredId: 6740,\r\n      status: 'Unpaid',\r\n      value: '$2113.04',\r\n      earning: '$716.72'\r\n    },\r\n    {\r\n      id: 3,\r\n      user: 'Clemmie Montgomery',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      referredId: 2749,\r\n      status: 'Unpaid',\r\n      value: '$6717.09',\r\n      earning: '$699.02'\r\n    },\r\n    {\r\n      id: 4,\r\n      user: 'Job Jope',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/7.png',\r\n      referredId: 1413,\r\n      status: 'Paid',\r\n      value: '$9465.13',\r\n      earning: '$98.23'\r\n    },\r\n    {\r\n      id: 5,\r\n      user: 'Christoffer Derell',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      referredId: 9176,\r\n      status: 'Paid',\r\n      value: '$6202.81',\r\n      earning: '$882.51'\r\n    },\r\n    {\r\n      id: 6,\r\n      user: 'Herminia Eyree',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/8.png',\r\n      referredId: 6975,\r\n      status: 'Unpaid',\r\n      value: '$9802.40',\r\n      earning: '$219.52'\r\n    },\r\n    {\r\n      id: 7,\r\n      user: 'Dela Lathwell',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      referredId: 4552,\r\n      status: 'Paid',\r\n      value: '$6470.46',\r\n      earning: '$831.45'\r\n    },\r\n    {\r\n      id: 8,\r\n      user: 'Kirbie Greenhow',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      referredId: 4131,\r\n      status: 'Unpaid',\r\n      value: '$6199.28',\r\n      earning: '$856.00'\r\n    },\r\n    {\r\n      id: 9,\r\n      user: 'Adrienne Tourne',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      referredId: 4072,\r\n      status: 'Unpaid',\r\n      value: '$6774.33',\r\n      earning: '$821.78'\r\n    },\r\n    {\r\n      id: 10,\r\n      user: 'Vanya Hearons',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      referredId: 3070,\r\n      status: 'Unpaid',\r\n      value: '$1067.14',\r\n      earning: '$804.91'\r\n    },\r\n    {\r\n      id: 11,\r\n      user: 'Garnette Abramcik',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/3.png',\r\n      referredId: 7828,\r\n      status: 'Unpaid',\r\n      value: '$5375.10',\r\n      earning: '$447.01'\r\n    },\r\n    {\r\n      id: 12,\r\n      user: 'Akim Korba',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/3.png',\r\n      referredId: 8561,\r\n      status: 'Unpaid',\r\n      value: '$3104.91',\r\n      earning: '$552.75'\r\n    },\r\n    {\r\n      id: 13,\r\n      user: 'Cull Scipsey',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/2.png',\r\n      referredId: 9287,\r\n      status: 'Paid',\r\n      value: '$9375.13',\r\n      earning: '$690.75'\r\n    },\r\n    {\r\n      id: 14,\r\n      user: 'Anabal Hakking',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/7.png',\r\n      referredId: 4892,\r\n      status: 'Paid',\r\n      value: '$8797.12',\r\n      earning: '$679.71'\r\n    },\r\n    {\r\n      id: 15,\r\n      user: 'Linzy Swiers',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/5.png',\r\n      referredId: 9180,\r\n      status: 'Unpaid',\r\n      value: '$2996.63',\r\n      earning: '$610.27'\r\n    },\r\n    {\r\n      id: 16,\r\n      user: 'Willy Espinet',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      referredId: 9102,\r\n      status: 'Paid',\r\n      value: '$7048.18',\r\n      earning: '$369.06'\r\n    },\r\n    {\r\n      id: 17,\r\n      user: 'Carter Gommowe',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      referredId: 7049,\r\n      status: 'Unpaid',\r\n      value: '$6049.95',\r\n      earning: '$642.78'\r\n    },\r\n    {\r\n      id: 18,\r\n      user: 'Andre Kenway',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/6.png',\r\n      referredId: 9826,\r\n      status: 'Paid',\r\n      value: '$2221.71',\r\n      earning: '$347.19'\r\n    },\r\n    {\r\n      id: 19,\r\n      user: 'Quintina Endacott',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      referredId: 4555,\r\n      status: 'Paid',\r\n      value: '$5918.70',\r\n      earning: '$543.44'\r\n    },\r\n    {\r\n      id: 20,\r\n      user: 'Shurwood Cabble',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/4.png',\r\n      referredId: 5591,\r\n      status: 'Paid',\r\n      value: '$9073.50',\r\n      earning: '$980.62'\r\n    },\r\n    {\r\n      id: 21,\r\n      user: 'Thatch Borchardt',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      referredId: 4491,\r\n      status: 'Unpaid',\r\n      value: '$8389.56',\r\n      earning: '$746.81'\r\n    },\r\n    {\r\n      id: 22,\r\n      user: \"Fawne O'Scanlan\",\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/8.png',\r\n      referredId: 2946,\r\n      status: 'Paid',\r\n      value: '$7471.34',\r\n      earning: '$747.24'\r\n    },\r\n    {\r\n      id: 23,\r\n      user: 'Ode Birts',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/2.png',\r\n      referredId: 2328,\r\n      status: 'Paid',\r\n      value: '$8484.83',\r\n      earning: '$815.79'\r\n    },\r\n    {\r\n      id: 24,\r\n      user: 'Bella Michelle',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/2.png',\r\n      referredId: 5725,\r\n      status: 'Paid',\r\n      value: '$7088.56',\r\n      earning: '$329.64'\r\n    },\r\n    {\r\n      id: 25,\r\n      user: 'Aurora Skpsey',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/6.png',\r\n      referredId: 2821,\r\n      status: 'Unpaid',\r\n      value: '$2938.87',\r\n      earning: '$317.42'\r\n    },\r\n    {\r\n      id: 26,\r\n      user: 'Neddie Maunders',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/7.png',\r\n      referredId: 1661,\r\n      status: 'Unpaid',\r\n      value: '$6256.70',\r\n      earning: '$521.01'\r\n    },\r\n    {\r\n      id: 27,\r\n      user: 'Andria Chisnell',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/4.png',\r\n      referredId: 3363,\r\n      status: 'Unpaid',\r\n      value: '$9106.99',\r\n      earning: '$705.15'\r\n    },\r\n    {\r\n      id: 28,\r\n      user: 'Reggy Arnao',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/3.png',\r\n      referredId: 7814,\r\n      status: 'Rejected',\r\n      value: '$6300.60',\r\n      earning: '$234.28'\r\n    },\r\n    {\r\n      id: 29,\r\n      user: 'Shaylah Hasselby',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/4.png',\r\n      referredId: 8324,\r\n      status: 'Paid',\r\n      value: '$1874.21',\r\n      earning: '$899.72'\r\n    },\r\n    {\r\n      id: 30,\r\n      user: 'Althea Dayce',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/8.png',\r\n      referredId: 6069,\r\n      status: 'Paid',\r\n      value: '$6098.09',\r\n      earning: '$269.32'\r\n    },\r\n    {\r\n      id: 31,\r\n      user: 'Hector Biaggioli',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/5.png',\r\n      referredId: 5286,\r\n      status: 'Paid',\r\n      value: '$4752.66',\r\n      earning: '$546.63'\r\n    },\r\n    {\r\n      id: 32,\r\n      user: 'Mycah Gotcher',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      referredId: 7944,\r\n      status: 'Unpaid',\r\n      value: '$5959.05',\r\n      earning: '$888.10'\r\n    },\r\n    {\r\n      id: 33,\r\n      user: 'Garv Scruton',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/5.png',\r\n      referredId: 6876,\r\n      status: 'Unpaid',\r\n      value: '$6588.37',\r\n      earning: '$680.51'\r\n    },\r\n    {\r\n      id: 34,\r\n      user: 'Renell Gurnett',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/2.png',\r\n      referredId: 7802,\r\n      status: 'Rejected',\r\n      value: '$7542.30',\r\n      earning: '$208.96'\r\n    },\r\n    {\r\n      id: 35,\r\n      user: 'Toinette Kilgrew',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/7.png',\r\n      referredId: 6946,\r\n      status: 'Paid',\r\n      value: '$4447.48',\r\n      earning: '$410.48'\r\n    },\r\n    {\r\n      id: 36,\r\n      user: 'Corinne Cockshtt',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/7.png',\r\n      referredId: 1372,\r\n      status: 'Paid',\r\n      value: '$3700.16',\r\n      earning: '$858.94'\r\n    },\r\n    {\r\n      id: 37,\r\n      user: 'Isis Yurkiewicz',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/4.png',\r\n      referredId: 2384,\r\n      status: 'Unpaid',\r\n      value: '$7456.86',\r\n      earning: '$280.52'\r\n    },\r\n    {\r\n      id: 38,\r\n      user: 'Gerianna Nott',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/5.png',\r\n      referredId: 1971,\r\n      status: 'Paid',\r\n      value: '$5563.94',\r\n      earning: '$515.34'\r\n    },\r\n    {\r\n      id: 39,\r\n      user: 'Calli Mewes',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/5.png',\r\n      referredId: 7323,\r\n      status: 'Unpaid',\r\n      value: '$7400.29',\r\n      earning: '$167.44'\r\n    },\r\n    {\r\n      id: 40,\r\n      user: 'Sonnnie Keeltagh',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/7.png',\r\n      referredId: 5719,\r\n      status: 'Paid',\r\n      value: '$1977.34',\r\n      earning: '$652.01'\r\n    },\r\n    {\r\n      id: 41,\r\n      user: 'Penelope Hause',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/3.png',\r\n      referredId: 9347,\r\n      status: 'Paid',\r\n      value: '$2155.12',\r\n      earning: '$101.49'\r\n    },\r\n    {\r\n      id: 42,\r\n      user: 'Dannie Romeo',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      referredId: 1559,\r\n      status: 'Rejected',\r\n      value: '$7110.30',\r\n      earning: '$95.40'\r\n    },\r\n    {\r\n      id: 43,\r\n      user: 'Keely Giannazzi',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/8.png',\r\n      referredId: 3307,\r\n      status: 'Paid',\r\n      value: '$2178.00',\r\n      earning: '$173.10'\r\n    },\r\n    {\r\n      id: 44,\r\n      user: 'Kassia Mottini',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/7.png',\r\n      referredId: 4426,\r\n      status: 'Rejected',\r\n      value: '$6921.60',\r\n      earning: '$365.93'\r\n    },\r\n    {\r\n      id: 45,\r\n      user: 'Burr Scrauniage',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/8.png',\r\n      referredId: 3570,\r\n      status: 'Paid',\r\n      value: '$6891.09',\r\n      earning: '$900.25'\r\n    }\r\n  ],\r\n  orderData: [\r\n    {\r\n      id: 1,\r\n      order: '5434',\r\n      customer: 'Gabrielle Feyer',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/8.png',\r\n      payment: 1,\r\n      status: 'Delivered',\r\n      spent: 73.98,\r\n      method: 'paypalLogo',\r\n      date: '5/16/2022',\r\n      time: '2:11 AM',\r\n      methodNumber: 6522\r\n    },\r\n    {\r\n      id: 2,\r\n      order: '6745',\r\n      customer: 'Jackson Deignan',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/8.png',\r\n      payment: 3,\r\n      status: 'Delivered',\r\n      spent: 100.39,\r\n      method: 'paypalLogo',\r\n      date: '5/3/2023',\r\n      time: '7:26 PM',\r\n      methodNumber: 7538\r\n    },\r\n    {\r\n      id: 3,\r\n      order: '6087',\r\n      customer: 'Tanya Crum',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/7.png',\r\n      payment: 4,\r\n      status: 'Ready to Pickup',\r\n      spent: 809.26,\r\n      method: 'mastercard',\r\n      date: '12/15/2022',\r\n      time: '6:51 PM',\r\n      methodNumber: 5170\r\n    },\r\n    {\r\n      id: 4,\r\n      order: '7825',\r\n      customer: 'Dallis Dillestone',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/4.png',\r\n      payment: 3,\r\n      status: 'Ready to Pickup',\r\n      spent: 617.64,\r\n      method: 'paypalLogo',\r\n      date: '8/5/2022',\r\n      time: '9:18 PM',\r\n      methodNumber: 1748\r\n    },\r\n    {\r\n      id: 5,\r\n      order: '5604',\r\n      customer: 'Conan Kennham',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      payment: 3,\r\n      status: 'Delivered',\r\n      spent: 384.41,\r\n      method: 'mastercard',\r\n      date: '6/18/2022',\r\n      time: '3:34 AM',\r\n      methodNumber: 6425\r\n    },\r\n    {\r\n      id: 6,\r\n      order: '5390',\r\n      customer: 'Daven Brocket',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/5.png',\r\n      payment: 3,\r\n      status: 'Out for Delivery',\r\n      spent: 162.85,\r\n      method: 'paypalLogo',\r\n      date: '10/14/2022',\r\n      time: '6:12 PM',\r\n      methodNumber: 1694\r\n    },\r\n    {\r\n      id: 7,\r\n      order: '7279',\r\n      customer: 'Rex Farbrace',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/2.png',\r\n      payment: 2,\r\n      status: 'Out for Delivery',\r\n      spent: 591.47,\r\n      method: 'mastercard',\r\n      date: '8/8/2022',\r\n      time: '6:09 PM',\r\n      methodNumber: 1883\r\n    },\r\n    {\r\n      id: 8,\r\n      order: '7003',\r\n      customer: 'Tann Biaggetti',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/8.png',\r\n      payment: 4,\r\n      status: 'Delivered',\r\n      spent: 664.51,\r\n      method: 'mastercard',\r\n      date: '6/10/2022',\r\n      time: '12:59 PM',\r\n      methodNumber: 5047\r\n    },\r\n    {\r\n      id: 9,\r\n      order: '8632',\r\n      customer: 'Abagael Drogan',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/3.png',\r\n      payment: 4,\r\n      status: 'Dispatched',\r\n      spent: 717.72,\r\n      method: 'paypalLogo',\r\n      date: '10/25/2022',\r\n      time: '10:48 AM',\r\n      methodNumber: 1945\r\n    },\r\n    {\r\n      id: 10,\r\n      order: '8501',\r\n      customer: 'Esme Sangwin',\r\n      email: '<EMAIL>',\r\n      avatar: '',\r\n      payment: 3,\r\n      status: 'Ready to Pickup',\r\n      spent: 477.42,\r\n      method: 'mastercard',\r\n      date: '11/2/2022',\r\n      time: '2:19 PM',\r\n      methodNumber: 3526\r\n    },\r\n    {\r\n      id: 11,\r\n      order: '6498',\r\n      customer: 'Jarib Siverns',\r\n      email: '<EMAIL>',\r\n      avatar: '',\r\n      payment: 4,\r\n      status: 'Out for Delivery',\r\n      spent: 71.42,\r\n      method: 'mastercard',\r\n      date: '8/25/2022',\r\n      time: '8:15 PM',\r\n      methodNumber: 8325\r\n    },\r\n    {\r\n      id: 12,\r\n      order: '7820',\r\n      customer: 'Christie Le Moucheux',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/8.png',\r\n      payment: 1,\r\n      status: 'Delivered',\r\n      spent: 894.55,\r\n      method: 'paypalLogo',\r\n      date: '11/3/2022',\r\n      time: '11:31 AM',\r\n      methodNumber: 2034\r\n    },\r\n    {\r\n      id: 13,\r\n      order: '8229',\r\n      customer: 'Debby Albury',\r\n      email: '<EMAIL>',\r\n      avatar: '',\r\n      payment: 2,\r\n      status: 'Delivered',\r\n      spent: 279.8,\r\n      method: 'mastercard',\r\n      date: '3/21/2023',\r\n      time: '3:28 PM',\r\n      methodNumber: 2751\r\n    },\r\n    {\r\n      id: 14,\r\n      order: '9076',\r\n      customer: 'Alexia Speaks',\r\n      email: '<EMAIL>',\r\n      avatar: '',\r\n      payment: 2,\r\n      status: 'Dispatched',\r\n      spent: 156.41,\r\n      method: 'paypalLogo',\r\n      date: '11/26/2022',\r\n      time: '9:16 PM',\r\n      methodNumber: 3234\r\n    },\r\n    {\r\n      id: 15,\r\n      order: '6045',\r\n      customer: 'Orel Leamy',\r\n      email: '<EMAIL>',\r\n      avatar: '',\r\n      payment: 2,\r\n      status: 'Delivered',\r\n      spent: 614.39,\r\n      method: 'mastercard',\r\n      date: '11/20/2022',\r\n      time: '11:58 PM',\r\n      methodNumber: 5209\r\n    },\r\n    {\r\n      id: 16,\r\n      order: '8005',\r\n      customer: 'Maurits Nealey',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/7.png',\r\n      payment: 1,\r\n      status: 'Delivered',\r\n      spent: 203.72,\r\n      method: 'mastercard',\r\n      date: '4/22/2023',\r\n      time: '3:01 PM',\r\n      methodNumber: 1555\r\n    },\r\n    {\r\n      id: 17,\r\n      order: '6917',\r\n      customer: 'Emmalee Mason',\r\n      email: '<EMAIL>',\r\n      avatar: '',\r\n      payment: 4,\r\n      status: 'Dispatched',\r\n      spent: 491.83,\r\n      method: 'mastercard',\r\n      date: '9/1/2022',\r\n      time: '10:31 PM',\r\n      methodNumber: 7013\r\n    },\r\n    {\r\n      id: 18,\r\n      order: '6918',\r\n      customer: 'Tibold Schops',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/7.png',\r\n      payment: 4,\r\n      status: 'Out for Delivery',\r\n      spent: 708.43,\r\n      method: 'paypalLogo',\r\n      date: '6/15/2022',\r\n      time: '11:03 AM',\r\n      methodNumber: 4636\r\n    },\r\n    {\r\n      id: 19,\r\n      order: '8733',\r\n      customer: 'Godwin Greatbanks',\r\n      email: '<EMAIL>',\r\n      avatar: '',\r\n      payment: 2,\r\n      status: 'Out for Delivery',\r\n      spent: 849.78,\r\n      method: 'paypalLogo',\r\n      date: '8/31/2022',\r\n      time: '10:02 AM',\r\n      methodNumber: 6846\r\n    },\r\n    {\r\n      id: 20,\r\n      order: '6630',\r\n      customer: 'Conn Cathery',\r\n      email: '<EMAIL>',\r\n      avatar: '',\r\n      payment: 2,\r\n      status: 'Dispatched',\r\n      spent: 855.31,\r\n      method: 'paypalLogo',\r\n      date: '1/2/2023',\r\n      time: '4:35 PM',\r\n      methodNumber: 4813\r\n    },\r\n    {\r\n      id: 21,\r\n      order: '8963',\r\n      customer: 'Riccardo McKerton',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/2.png',\r\n      payment: 2,\r\n      status: 'Dispatched',\r\n      spent: 458.76,\r\n      method: 'paypalLogo',\r\n      date: '9/17/2022',\r\n      time: '6:00 AM',\r\n      methodNumber: 8197\r\n    },\r\n    {\r\n      id: 22,\r\n      order: '6916',\r\n      customer: 'Isa Cartmel',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/2.png',\r\n      payment: 3,\r\n      status: 'Dispatched',\r\n      spent: 914.48,\r\n      method: 'paypalLogo',\r\n      date: '12/21/2022',\r\n      time: '8:35 PM',\r\n      methodNumber: 2844\r\n    },\r\n    {\r\n      id: 23,\r\n      order: '6647',\r\n      customer: 'Yoko Beetles',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/7.png',\r\n      payment: 1,\r\n      status: 'Delivered',\r\n      spent: 948.07,\r\n      method: 'mastercard',\r\n      date: '1/24/2023',\r\n      time: '12:01 AM',\r\n      methodNumber: 2562\r\n    },\r\n    {\r\n      id: 24,\r\n      order: '8044',\r\n      customer: 'Nowell Cornford',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/5.png',\r\n      payment: 4,\r\n      status: 'Ready to Pickup',\r\n      spent: 525.6,\r\n      method: 'paypalLogo',\r\n      date: '8/22/2022',\r\n      time: '6:36 PM',\r\n      methodNumber: 2030\r\n    },\r\n    {\r\n      id: 25,\r\n      order: '9879',\r\n      customer: 'Brandy McIlvenna',\r\n      email: '<EMAIL>',\r\n      avatar: '',\r\n      payment: 1,\r\n      status: 'Out for Delivery',\r\n      spent: 100.18,\r\n      method: 'mastercard',\r\n      date: '12/23/2022',\r\n      time: '7:14 AM',\r\n      methodNumber: 4686\r\n    },\r\n    {\r\n      id: 26,\r\n      order: '5551',\r\n      customer: 'Zondra Klimkin',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      payment: 3,\r\n      status: 'Delivered',\r\n      spent: 463.32,\r\n      method: 'mastercard',\r\n      date: '12/20/2022',\r\n      time: '12:01 PM',\r\n      methodNumber: 6209\r\n    },\r\n    {\r\n      id: 27,\r\n      order: '5905',\r\n      customer: 'Elyn Aizic',\r\n      email: '<EMAIL>',\r\n      avatar: '',\r\n      payment: 4,\r\n      status: 'Dispatched',\r\n      spent: 581.81,\r\n      method: 'mastercard',\r\n      date: '6/1/2022',\r\n      time: '2:31 AM',\r\n      methodNumber: 7031\r\n    },\r\n    {\r\n      id: 28,\r\n      order: '7616',\r\n      customer: 'Leoine Talbot',\r\n      email: '<EMAIL>',\r\n      avatar: '',\r\n      payment: 1,\r\n      status: 'Delivered',\r\n      spent: 118.75,\r\n      method: 'paypalLogo',\r\n      date: '10/13/2022',\r\n      time: '12:57 AM',\r\n      methodNumber: 4387\r\n    },\r\n    {\r\n      id: 29,\r\n      order: '6624',\r\n      customer: 'Fayre Screech',\r\n      email: '<EMAIL>',\r\n      avatar: '',\r\n      payment: 3,\r\n      status: 'Out for Delivery',\r\n      spent: 774.91,\r\n      method: 'mastercard',\r\n      date: '4/17/2023',\r\n      time: '6:43 PM',\r\n      methodNumber: 2077\r\n    },\r\n    {\r\n      id: 30,\r\n      order: '8653',\r\n      customer: 'Roxanne Rablen',\r\n      email: '<EMAIL>',\r\n      avatar: '',\r\n      payment: 1,\r\n      status: 'Delivered',\r\n      spent: 212.75,\r\n      method: 'mastercard',\r\n      date: '3/23/2023',\r\n      time: '10:07 PM',\r\n      methodNumber: 2696\r\n    },\r\n    {\r\n      id: 31,\r\n      order: '8076',\r\n      customer: 'Rebekkah Newsham',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/2.png',\r\n      payment: 2,\r\n      status: 'Ready to Pickup',\r\n      spent: 778.56,\r\n      method: 'mastercard',\r\n      date: '7/1/2022',\r\n      time: '10:37 PM',\r\n      methodNumber: 8071\r\n    },\r\n    {\r\n      id: 32,\r\n      order: '7972',\r\n      customer: 'Crawford Beart',\r\n      email: '<EMAIL>',\r\n      avatar: '',\r\n      payment: 3,\r\n      status: 'Delivered',\r\n      spent: 378.74,\r\n      method: 'mastercard',\r\n      date: '11/23/2022',\r\n      time: '6:45 AM',\r\n      methodNumber: 3993\r\n    },\r\n    {\r\n      id: 33,\r\n      order: '6979',\r\n      customer: 'Cristine Easom',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      payment: 2,\r\n      status: 'Out for Delivery',\r\n      spent: 595.84,\r\n      method: 'mastercard',\r\n      date: '4/15/2023',\r\n      time: '10:21 PM',\r\n      methodNumber: 2356\r\n    },\r\n    {\r\n      id: 34,\r\n      order: '9438',\r\n      customer: 'Bessy Vasechkin',\r\n      email: '<EMAIL>',\r\n      avatar: '',\r\n      payment: 1,\r\n      status: 'Dispatched',\r\n      spent: 257.18,\r\n      method: 'paypalLogo',\r\n      date: '11/10/2022',\r\n      time: '8:12 PM',\r\n      methodNumber: 1776\r\n    },\r\n    {\r\n      id: 35,\r\n      order: '5666',\r\n      customer: 'Joanne Morl',\r\n      email: '<EMAIL>',\r\n      avatar: '',\r\n      payment: 1,\r\n      status: 'Ready to Pickup',\r\n      spent: 368.26,\r\n      method: 'paypalLogo',\r\n      date: '11/17/2022',\r\n      time: '2:32 PM',\r\n      methodNumber: 6276\r\n    },\r\n    {\r\n      id: 36,\r\n      order: '7128',\r\n      customer: 'Cobbie Brameld',\r\n      email: '<EMAIL>',\r\n      avatar: '',\r\n      payment: 4,\r\n      status: 'Delivered',\r\n      spent: 484.14,\r\n      method: 'paypalLogo',\r\n      date: '6/13/2022',\r\n      time: '9:36 PM',\r\n      methodNumber: 3876\r\n    },\r\n    {\r\n      id: 37,\r\n      order: '5834',\r\n      customer: 'Turner Braban',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/6.png',\r\n      payment: 2,\r\n      status: 'Delivered',\r\n      spent: 135.04,\r\n      method: 'mastercard',\r\n      date: '10/14/2022',\r\n      time: '4:39 AM',\r\n      methodNumber: 2211\r\n    },\r\n    {\r\n      id: 38,\r\n      order: '7417',\r\n      customer: 'Rudd Aisman',\r\n      email: '<EMAIL>',\r\n      avatar: '',\r\n      payment: 2,\r\n      status: 'Delivered',\r\n      spent: 598.61,\r\n      method: 'mastercard',\r\n      date: '9/29/2022',\r\n      time: '10:31 AM',\r\n      methodNumber: 1979\r\n    },\r\n    {\r\n      id: 39,\r\n      order: '5574',\r\n      customer: 'Rakel Hearle',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/8.png',\r\n      payment: 1,\r\n      status: 'Out for Delivery',\r\n      spent: 612.56,\r\n      method: 'paypalLogo',\r\n      date: '11/29/2022',\r\n      time: '2:59 AM',\r\n      methodNumber: 8328\r\n    },\r\n    {\r\n      id: 40,\r\n      order: '7834',\r\n      customer: 'Cull Otson',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/2.png',\r\n      payment: 4,\r\n      status: 'Delivered',\r\n      spent: 413.7,\r\n      method: 'mastercard',\r\n      date: '7/23/2022',\r\n      time: '3:15 PM',\r\n      methodNumber: 3901\r\n    },\r\n    {\r\n      id: 41,\r\n      order: '9877',\r\n      customer: 'Jedd Lafont',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/8.png',\r\n      payment: 1,\r\n      status: 'Ready to Pickup',\r\n      spent: 67.26,\r\n      method: 'paypalLogo',\r\n      date: '11/1/2022',\r\n      time: '8:05 AM',\r\n      methodNumber: 7245\r\n    },\r\n    {\r\n      id: 42,\r\n      order: '5781',\r\n      customer: 'Maribeth Roffe',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/6.png',\r\n      payment: 1,\r\n      status: 'Out for Delivery',\r\n      spent: 697.13,\r\n      method: 'paypalLogo',\r\n      date: '9/30/2022',\r\n      time: '8:03 PM',\r\n      methodNumber: 8102\r\n    },\r\n    {\r\n      id: 43,\r\n      order: '5299',\r\n      customer: 'Ximenez Callaghan',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/6.png',\r\n      payment: 2,\r\n      status: 'Dispatched',\r\n      spent: 528.17,\r\n      method: 'mastercard',\r\n      date: '12/30/2022',\r\n      time: '12:21 AM',\r\n      methodNumber: 3075\r\n    },\r\n    {\r\n      id: 44,\r\n      order: '6622',\r\n      customer: 'Oliy Seton',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/7.png',\r\n      payment: 2,\r\n      status: 'Delivered',\r\n      spent: 662.07,\r\n      method: 'paypalLogo',\r\n      date: '12/29/2022',\r\n      time: '8:45 PM',\r\n      methodNumber: 5021\r\n    },\r\n    {\r\n      id: 45,\r\n      order: '7387',\r\n      customer: 'Conroy Conan',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/8.png',\r\n      payment: 1,\r\n      status: 'Delivered',\r\n      spent: 65.73,\r\n      method: 'paypalLogo',\r\n      date: '6/11/2022',\r\n      time: '10:11 AM',\r\n      methodNumber: 3954\r\n    },\r\n    {\r\n      id: 46,\r\n      order: '5078',\r\n      customer: 'Elianore Russ',\r\n      email: '<EMAIL>',\r\n      avatar: '',\r\n      payment: 2,\r\n      status: 'Ready to Pickup',\r\n      spent: 741.28,\r\n      method: 'mastercard',\r\n      date: '8/28/2022',\r\n      time: '3:45 PM',\r\n      methodNumber: 2128\r\n    },\r\n    {\r\n      id: 47,\r\n      order: '9631',\r\n      customer: 'Farlee Gerard',\r\n      email: '<EMAIL>',\r\n      avatar: '',\r\n      payment: 2,\r\n      status: 'Ready to Pickup',\r\n      spent: 161.3,\r\n      method: 'paypalLogo',\r\n      date: '6/8/2022',\r\n      time: '4:16 PM',\r\n      methodNumber: 6781\r\n    },\r\n    {\r\n      id: 48,\r\n      order: '7869',\r\n      customer: 'Gino Fairbrass',\r\n      email: '<EMAIL>',\r\n      avatar: '',\r\n      payment: 4,\r\n      status: 'Ready to Pickup',\r\n      spent: 644.9,\r\n      method: 'paypalLogo',\r\n      date: '6/23/2022',\r\n      time: '10:36 AM',\r\n      methodNumber: 5470\r\n    },\r\n    {\r\n      id: 49,\r\n      order: '8643',\r\n      customer: 'Dory Carter',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/2.png',\r\n      payment: 3,\r\n      status: 'Delivered',\r\n      spent: 462.45,\r\n      method: 'mastercard',\r\n      date: '11/10/2022',\r\n      time: '2:45 AM',\r\n      methodNumber: 4647\r\n    },\r\n    {\r\n      id: 50,\r\n      order: '7395',\r\n      customer: 'Shane Galbreth',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/8.png',\r\n      payment: 4,\r\n      status: 'Delivered',\r\n      spent: 731.58,\r\n      method: 'mastercard',\r\n      date: '5/20/2022',\r\n      time: '8:09 PM',\r\n      methodNumber: 4113\r\n    },\r\n    {\r\n      id: 51,\r\n      order: '7168',\r\n      customer: 'Alicea Macci',\r\n      email: '<EMAIL>',\r\n      avatar: '',\r\n      payment: 1,\r\n      status: 'Ready to Pickup',\r\n      spent: 556.94,\r\n      method: 'mastercard',\r\n      date: '6/10/2022',\r\n      time: '4:00 PM',\r\n      methodNumber: 6798\r\n    },\r\n    {\r\n      id: 52,\r\n      order: '5775',\r\n      customer: 'Terrijo Copello',\r\n      email: '<EMAIL>',\r\n      avatar: '',\r\n      payment: 3,\r\n      status: 'Dispatched',\r\n      spent: 687.27,\r\n      method: 'paypalLogo',\r\n      date: '6/23/2022',\r\n      time: '6:41 PM',\r\n      methodNumber: 3529\r\n    },\r\n    {\r\n      id: 53,\r\n      order: '6558',\r\n      customer: 'Bambi Yerby',\r\n      email: '<EMAIL>',\r\n      avatar: '',\r\n      payment: 1,\r\n      status: 'Out for Delivery',\r\n      spent: 309.15,\r\n      method: 'paypalLogo',\r\n      date: '10/18/2022',\r\n      time: '8:40 PM',\r\n      methodNumber: 1664\r\n    },\r\n    {\r\n      id: 54,\r\n      order: '7766',\r\n      customer: 'Corny Linstead',\r\n      email: '<EMAIL>',\r\n      avatar: '',\r\n      payment: 4,\r\n      status: 'Dispatched',\r\n      spent: 805.73,\r\n      method: 'paypalLogo',\r\n      date: '6/25/2022',\r\n      time: '8:01 AM',\r\n      methodNumber: 7931\r\n    },\r\n    {\r\n      id: 55,\r\n      order: '9305',\r\n      customer: 'Pauline Pfaffe',\r\n      email: '<EMAIL>',\r\n      avatar: '',\r\n      payment: 4,\r\n      status: 'Ready to Pickup',\r\n      spent: 769.47,\r\n      method: 'paypalLogo',\r\n      date: '4/17/2023',\r\n      time: '8:05 AM',\r\n      methodNumber: 8412\r\n    },\r\n    {\r\n      id: 56,\r\n      order: '7886',\r\n      customer: 'Ilka Adanet',\r\n      email: '<EMAIL>',\r\n      avatar: '',\r\n      payment: 4,\r\n      status: 'Ready to Pickup',\r\n      spent: 899.35,\r\n      method: 'paypalLogo',\r\n      date: '2/2/2023',\r\n      time: '6:13 PM',\r\n      methodNumber: 3946\r\n    },\r\n    {\r\n      id: 57,\r\n      order: '8333',\r\n      customer: 'Charlena Sabberton',\r\n      email: '<EMAIL>',\r\n      avatar: '',\r\n      payment: 3,\r\n      status: 'Out for Delivery',\r\n      spent: 201.84,\r\n      method: 'paypalLogo',\r\n      date: '6/11/2022',\r\n      time: '10:15 PM',\r\n      methodNumber: 3294\r\n    },\r\n    {\r\n      id: 58,\r\n      order: '7044',\r\n      customer: 'Harwell Vallack',\r\n      email: '<EMAIL>',\r\n      avatar: '',\r\n      payment: 2,\r\n      status: 'Dispatched',\r\n      spent: 547.07,\r\n      method: 'paypalLogo',\r\n      date: '6/1/2022',\r\n      time: '1:29 PM',\r\n      methodNumber: 5571\r\n    },\r\n    {\r\n      id: 59,\r\n      order: '5414',\r\n      customer: 'Juliette Douthwaite',\r\n      email: '<EMAIL>',\r\n      avatar: '',\r\n      payment: 2,\r\n      status: 'Dispatched',\r\n      spent: 89.46,\r\n      method: 'mastercard',\r\n      date: '9/26/2022',\r\n      time: '11:40 AM',\r\n      methodNumber: 4380\r\n    },\r\n    {\r\n      id: 60,\r\n      order: '7102',\r\n      customer: 'Nydia Brandel',\r\n      email: '<EMAIL>',\r\n      avatar: '',\r\n      payment: 3,\r\n      status: 'Out for Delivery',\r\n      spent: 417.49,\r\n      method: 'paypalLogo',\r\n      date: '2/5/2023',\r\n      time: '11:42 PM',\r\n      methodNumber: 5856\r\n    },\r\n    {\r\n      id: 61,\r\n      order: '8784',\r\n      customer: 'Gaby Edy',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      payment: 4,\r\n      status: 'Out for Delivery',\r\n      spent: 589.37,\r\n      method: 'mastercard',\r\n      date: '2/5/2023',\r\n      time: '8:46 AM',\r\n      methodNumber: 1923\r\n    },\r\n    {\r\n      id: 62,\r\n      order: '9885',\r\n      customer: 'Lacey Swenson',\r\n      email: '<EMAIL>',\r\n      avatar: '',\r\n      payment: 2,\r\n      status: 'Ready to Pickup',\r\n      spent: 62.71,\r\n      method: 'mastercard',\r\n      date: '9/11/2022',\r\n      time: '7:41 PM',\r\n      methodNumber: 4367\r\n    },\r\n    {\r\n      id: 63,\r\n      order: '5387',\r\n      customer: 'Bradan Edgworth',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/7.png',\r\n      payment: 4,\r\n      status: 'Out for Delivery',\r\n      spent: 54.45,\r\n      method: 'paypalLogo',\r\n      date: '6/2/2022',\r\n      time: '11:05 AM',\r\n      methodNumber: 8829\r\n    },\r\n    {\r\n      id: 64,\r\n      order: '5459',\r\n      customer: 'Ilyssa Egan',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/8.png',\r\n      payment: 3,\r\n      status: 'Dispatched',\r\n      spent: 756.16,\r\n      method: 'paypalLogo',\r\n      date: '5/20/2022',\r\n      time: '12:39 PM',\r\n      methodNumber: 6971\r\n    },\r\n    {\r\n      id: 65,\r\n      order: '8812',\r\n      customer: 'Duke Jahnel',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/7.png',\r\n      payment: 2,\r\n      status: 'Dispatched',\r\n      spent: 103.71,\r\n      method: 'mastercard',\r\n      date: '3/1/2023',\r\n      time: '10:25 PM',\r\n      methodNumber: 4305\r\n    },\r\n    {\r\n      id: 66,\r\n      order: '7123',\r\n      customer: 'Christen Dillow',\r\n      email: '<EMAIL>',\r\n      avatar: '',\r\n      payment: 1,\r\n      status: 'Ready to Pickup',\r\n      spent: 357.17,\r\n      method: 'mastercard',\r\n      date: '2/1/2023',\r\n      time: '4:11 AM',\r\n      methodNumber: 7385\r\n    },\r\n    {\r\n      id: 67,\r\n      order: '8964',\r\n      customer: 'Hildegaard Ormshaw',\r\n      email: '<EMAIL>',\r\n      avatar: '',\r\n      payment: 1,\r\n      status: 'Dispatched',\r\n      spent: 191.57,\r\n      method: 'mastercard',\r\n      date: '6/15/2022',\r\n      time: '7:28 PM',\r\n      methodNumber: 6469\r\n    },\r\n    {\r\n      id: 68,\r\n      order: '8020',\r\n      customer: 'Merrill Sangwin',\r\n      email: '<EMAIL>',\r\n      avatar: '',\r\n      payment: 3,\r\n      status: 'Delivered',\r\n      spent: 80.47,\r\n      method: 'paypalLogo',\r\n      date: '9/15/2022',\r\n      time: '9:35 PM',\r\n      methodNumber: 1464\r\n    },\r\n    {\r\n      id: 69,\r\n      order: '7192',\r\n      customer: 'Niel Kitchingman',\r\n      email: '<EMAIL>',\r\n      avatar: '',\r\n      payment: 1,\r\n      status: 'Delivered',\r\n      spent: 759.98,\r\n      method: 'mastercard',\r\n      date: '11/24/2022',\r\n      time: '12:51 PM',\r\n      methodNumber: 3957\r\n    },\r\n    {\r\n      id: 70,\r\n      order: '9941',\r\n      customer: 'Zacharias Stonhard',\r\n      email: '<EMAIL>',\r\n      avatar: '',\r\n      payment: 4,\r\n      status: 'Delivered',\r\n      spent: 333.83,\r\n      method: 'paypalLogo',\r\n      date: '6/20/2022',\r\n      time: '11:11 AM',\r\n      methodNumber: 3907\r\n    },\r\n    {\r\n      id: 71,\r\n      order: '7786',\r\n      customer: 'Hirsch Garwood',\r\n      email: '<EMAIL>',\r\n      avatar: '',\r\n      payment: 1,\r\n      status: 'Delivered',\r\n      spent: 993.07,\r\n      method: 'paypalLogo',\r\n      date: '1/30/2023',\r\n      time: '8:13 AM',\r\n      methodNumber: 3210\r\n    },\r\n    {\r\n      id: 72,\r\n      order: '6890',\r\n      customer: 'Etienne Duke',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      payment: 4,\r\n      status: 'Dispatched',\r\n      spent: 651.14,\r\n      method: 'mastercard',\r\n      date: '8/1/2022',\r\n      time: '7:24 AM',\r\n      methodNumber: 3507\r\n    },\r\n    {\r\n      id: 73,\r\n      order: '6672',\r\n      customer: 'Galen Bent',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/2.png',\r\n      payment: 4,\r\n      status: 'Out for Delivery',\r\n      spent: 483.86,\r\n      method: 'mastercard',\r\n      date: '5/10/2022',\r\n      time: '7:51 PM',\r\n      methodNumber: 7538\r\n    },\r\n    {\r\n      id: 74,\r\n      order: '5531',\r\n      customer: 'Cletus Arias',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/1.png',\r\n      payment: 3,\r\n      status: 'Delivered',\r\n      spent: 609.47,\r\n      method: 'mastercard',\r\n      date: '8/20/2022',\r\n      time: '3:21 AM',\r\n      methodNumber: 5851\r\n    },\r\n    {\r\n      id: 75,\r\n      order: '9041',\r\n      customer: 'Gilbertina Manjin',\r\n      email: '<EMAIL>',\r\n      avatar: '',\r\n      payment: 2,\r\n      status: 'Dispatched',\r\n      spent: 593.65,\r\n      method: 'mastercard',\r\n      date: '9/19/2022',\r\n      time: '5:23 AM',\r\n      methodNumber: 8332\r\n    },\r\n    {\r\n      id: 76,\r\n      order: '9521',\r\n      customer: 'Helena Airds',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/7.png',\r\n      payment: 4,\r\n      status: 'Out for Delivery',\r\n      spent: 897.84,\r\n      method: 'mastercard',\r\n      date: '1/13/2023',\r\n      time: '1:41 PM',\r\n      methodNumber: 8564\r\n    },\r\n    {\r\n      id: 77,\r\n      order: '9793',\r\n      customer: 'Bonny Tebbutt',\r\n      email: '<EMAIL>',\r\n      avatar: '',\r\n      payment: 3,\r\n      status: 'Ready to Pickup',\r\n      spent: 856.58,\r\n      method: 'paypalLogo',\r\n      date: '1/23/2023',\r\n      time: '6:10 AM',\r\n      methodNumber: 2150\r\n    },\r\n    {\r\n      id: 78,\r\n      order: '6741',\r\n      customer: 'Garreth Rubinowitz',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/8.png',\r\n      payment: 2,\r\n      status: 'Ready to Pickup',\r\n      spent: 191.99,\r\n      method: 'paypalLogo',\r\n      date: '8/24/2022',\r\n      time: '2:01 PM',\r\n      methodNumber: 4148\r\n    },\r\n    {\r\n      id: 79,\r\n      order: '6602',\r\n      customer: 'Lotta Martinie',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/8.png',\r\n      payment: 2,\r\n      status: 'Out for Delivery',\r\n      spent: 790.09,\r\n      method: 'paypalLogo',\r\n      date: '6/25/2022',\r\n      time: '12:54 AM',\r\n      methodNumber: 4538\r\n    },\r\n    {\r\n      id: 80,\r\n      order: '9682',\r\n      customer: 'Danna Goldis',\r\n      email: '<EMAIL>',\r\n      avatar: '',\r\n      payment: 2,\r\n      status: 'Dispatched',\r\n      spent: 121.21,\r\n      method: 'mastercard',\r\n      date: '1/11/2023',\r\n      time: '4:33 PM',\r\n      methodNumber: 1974\r\n    },\r\n    {\r\n      id: 81,\r\n      order: '6256',\r\n      customer: 'Ronica McDuffie',\r\n      email: '<EMAIL>',\r\n      avatar: '',\r\n      payment: 4,\r\n      status: 'Delivered',\r\n      spent: 783.05,\r\n      method: 'mastercard',\r\n      date: '7/12/2022',\r\n      time: '1:54 AM',\r\n      methodNumber: 6563\r\n    },\r\n    {\r\n      id: 82,\r\n      order: '6265',\r\n      customer: 'Clarice Biesty',\r\n      email: '<EMAIL>',\r\n      avatar: '',\r\n      payment: 2,\r\n      status: 'Dispatched',\r\n      spent: 905.31,\r\n      method: 'paypalLogo',\r\n      date: '9/7/2022',\r\n      time: '5:58 AM',\r\n      methodNumber: 7367\r\n    },\r\n    {\r\n      id: 83,\r\n      order: '7330',\r\n      customer: 'Georgetta Hawkins',\r\n      email: '<EMAIL>',\r\n      avatar: '',\r\n      payment: 4,\r\n      status: 'Out for Delivery',\r\n      spent: 670.5,\r\n      method: 'mastercard',\r\n      date: '12/9/2022',\r\n      time: '4:22 AM',\r\n      methodNumber: 4789\r\n    },\r\n    {\r\n      id: 84,\r\n      order: '6342',\r\n      customer: 'Hamid Gosford',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/7.png',\r\n      payment: 2,\r\n      status: 'Out for Delivery',\r\n      spent: 520.17,\r\n      method: 'paypalLogo',\r\n      date: '5/26/2022',\r\n      time: '3:15 PM',\r\n      methodNumber: 2733\r\n    },\r\n    {\r\n      id: 85,\r\n      order: '9620',\r\n      customer: 'Marnia Scamwell',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/3.png',\r\n      payment: 4,\r\n      status: 'Ready to Pickup',\r\n      spent: 77.59,\r\n      method: 'paypalLogo',\r\n      date: '9/10/2022',\r\n      time: '11:40 AM',\r\n      methodNumber: 3822\r\n    },\r\n    {\r\n      id: 86,\r\n      order: '5699',\r\n      customer: 'Casie Cratere',\r\n      email: '<EMAIL>',\r\n      avatar: '',\r\n      payment: 3,\r\n      status: 'Delivered',\r\n      spent: 429.8,\r\n      method: 'mastercard',\r\n      date: '9/22/2022',\r\n      time: '11:52 PM',\r\n      methodNumber: 2925\r\n    },\r\n    {\r\n      id: 87,\r\n      order: '7289',\r\n      customer: 'Edik Whytock',\r\n      email: '<EMAIL>',\r\n      avatar: '',\r\n      payment: 3,\r\n      status: 'Ready to Pickup',\r\n      spent: 838.25,\r\n      method: 'mastercard',\r\n      date: '8/4/2022',\r\n      time: '9:12 PM',\r\n      methodNumber: 6240\r\n    },\r\n    {\r\n      id: 88,\r\n      order: '9780',\r\n      customer: 'Wylie Marryatt',\r\n      email: '<EMAIL>',\r\n      avatar: '',\r\n      payment: 3,\r\n      status: 'Out for Delivery',\r\n      spent: 308.07,\r\n      method: 'mastercard',\r\n      date: '3/2/2023',\r\n      time: '10:00 AM',\r\n      methodNumber: 7909\r\n    },\r\n    {\r\n      id: 89,\r\n      order: '5859',\r\n      customer: 'Eydie Vogelein',\r\n      email: '<EMAIL>',\r\n      avatar: '',\r\n      payment: 4,\r\n      status: 'Ready to Pickup',\r\n      spent: 447.29,\r\n      method: 'paypalLogo',\r\n      date: '4/29/2023',\r\n      time: '9:52 AM',\r\n      methodNumber: 5475\r\n    },\r\n    {\r\n      id: 90,\r\n      order: '9957',\r\n      customer: 'Milt Whitear',\r\n      email: '<EMAIL>',\r\n      avatar: '',\r\n      payment: 4,\r\n      status: 'Dispatched',\r\n      spent: 59.28,\r\n      method: 'mastercard',\r\n      date: '11/29/2022',\r\n      time: '6:53 PM',\r\n      methodNumber: 4371\r\n    },\r\n    {\r\n      id: 91,\r\n      order: '7094',\r\n      customer: 'Damara Figgins',\r\n      email: '<EMAIL>',\r\n      avatar: '',\r\n      payment: 2,\r\n      status: 'Delivered',\r\n      spent: 62.62,\r\n      method: 'mastercard',\r\n      date: '6/29/2022',\r\n      time: '6:51 AM',\r\n      methodNumber: 8321\r\n    },\r\n    {\r\n      id: 92,\r\n      order: '7280',\r\n      customer: 'Sibley Braithwait',\r\n      email: '<EMAIL>',\r\n      avatar: '',\r\n      payment: 1,\r\n      status: 'Ready to Pickup',\r\n      spent: 554.91,\r\n      method: 'mastercard',\r\n      date: '12/6/2022',\r\n      time: '2:11 AM',\r\n      methodNumber: 8535\r\n    },\r\n    {\r\n      id: 93,\r\n      order: '7931',\r\n      customer: 'Octavius Whitchurch',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/7.png',\r\n      payment: 3,\r\n      status: 'Dispatched',\r\n      spent: 383.52,\r\n      method: 'mastercard',\r\n      date: '12/26/2022',\r\n      time: '9:49 AM',\r\n      methodNumber: 8585\r\n    },\r\n    {\r\n      id: 94,\r\n      order: '8767',\r\n      customer: 'Lyndsey Dorey',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/2.png',\r\n      payment: 3,\r\n      status: 'Ready to Pickup',\r\n      spent: 738.42,\r\n      method: 'mastercard',\r\n      date: '8/29/2022',\r\n      time: '5:24 AM',\r\n      methodNumber: 3432\r\n    },\r\n    {\r\n      id: 95,\r\n      order: '6111',\r\n      customer: 'Chad Cock',\r\n      email: '<EMAIL>',\r\n      avatar: '',\r\n      payment: 4,\r\n      status: 'Ready to Pickup',\r\n      spent: 669.45,\r\n      method: 'mastercard',\r\n      date: '3/11/2023',\r\n      time: '10:43 AM',\r\n      methodNumber: 1014\r\n    },\r\n    {\r\n      id: 96,\r\n      order: '5911',\r\n      customer: 'Hilliard Merck',\r\n      email: '<EMAIL>',\r\n      avatar: '',\r\n      payment: 4,\r\n      status: 'Out for Delivery',\r\n      spent: 237.91,\r\n      method: 'paypalLogo',\r\n      date: '8/14/2022',\r\n      time: '3:26 PM',\r\n      methodNumber: 3196\r\n    },\r\n    {\r\n      id: 97,\r\n      order: '7064',\r\n      customer: 'Carmon Vasiljevic',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/8.png',\r\n      payment: 3,\r\n      status: 'Delivered',\r\n      spent: 595.25,\r\n      method: 'paypalLogo',\r\n      date: '3/20/2023',\r\n      time: '3:11 PM',\r\n      methodNumber: 4892\r\n    },\r\n    {\r\n      id: 98,\r\n      order: '8114',\r\n      customer: 'Ulysses Goodlife',\r\n      email: '<EMAIL>',\r\n      avatar: '/images/avatars/2.png',\r\n      payment: 3,\r\n      status: 'Ready to Pickup',\r\n      spent: 746.38,\r\n      method: 'mastercard',\r\n      date: '4/8/2023',\r\n      time: '3:39 AM',\r\n      methodNumber: 4509\r\n    },\r\n    {\r\n      id: 99,\r\n      order: '7189',\r\n      customer: 'Boycie Hartmann',\r\n      email: '<EMAIL>',\r\n      avatar: '',\r\n      payment: 3,\r\n      status: 'Out for Delivery',\r\n      spent: 704.86,\r\n      method: 'paypalLogo',\r\n      date: '1/2/2023',\r\n      time: '8:55 PM',\r\n      methodNumber: 6424\r\n    },\r\n    {\r\n      id: 100,\r\n      order: '9042',\r\n      customer: 'Chere Schofield',\r\n      email: '<EMAIL>',\r\n      avatar: '',\r\n      payment: 2,\r\n      status: 'Ready to Pickup',\r\n      spent: 815.77,\r\n      method: 'mastercard',\r\n      date: '2/1/2023',\r\n      time: '4:12 PM',\r\n      methodNumber: 3949\r\n    }\r\n  ],\r\n  customerData: [\r\n    {\r\n      id: 1,\r\n      customer: 'Stanfield Baser',\r\n      customerId: '879861',\r\n      email: '<EMAIL>',\r\n      country: 'Australia',\r\n      countryFlag: '/images/cards/australia.png',\r\n      countryCode: 'lk',\r\n      order: 157,\r\n      totalSpent: 2074.22,\r\n      avatar: '/images/avatars/1.png'\r\n    },\r\n    {\r\n      id: 2,\r\n      customer: 'Laurie Dax',\r\n      customerId: '178408',\r\n      email: '<EMAIL>',\r\n      country: 'Australia',\r\n      countryFlag: '/images/cards/australia.png',\r\n      countryCode: 'ru',\r\n      order: 663,\r\n      totalSpent: 2404.19,\r\n      avatar: '/images/avatars/2.png'\r\n    },\r\n    {\r\n      id: 3,\r\n      customer: 'Maxine Kenrick',\r\n      customerId: '221092',\r\n      email: '<EMAIL>',\r\n      country: 'Australia',\r\n      countryFlag: '/images/cards/australia.png',\r\n      countryCode: 'gt',\r\n      order: 64,\r\n      totalSpent: 8821.4,\r\n      avatar: '/images/avatars/3.png'\r\n    },\r\n    {\r\n      id: 4,\r\n      customer: 'Harman Burkill',\r\n      customerId: '645579',\r\n      email: '<EMAIL>',\r\n      country: 'United States',\r\n      countryFlag: '/images/cards/us.png',\r\n      countryCode: 'pt',\r\n      order: 640,\r\n      totalSpent: 5294.35,\r\n      avatar: '/images/avatars/4.png'\r\n    },\r\n    {\r\n      id: 5,\r\n      customer: 'Aubrey Borrow',\r\n      customerId: '288765',\r\n      email: '<EMAIL>',\r\n      country: 'France',\r\n      countryFlag: '/images/cards/france.png',\r\n      countryCode: 'fr',\r\n      order: 184,\r\n      totalSpent: 1003.3,\r\n      avatar: '/images/avatars/5.png'\r\n    },\r\n    {\r\n      id: 6,\r\n      customer: 'Nester Fridd',\r\n      customerId: '321942',\r\n      email: '<EMAIL>',\r\n      country: 'China',\r\n      countryFlag: '/images/cards/china.png',\r\n      countryCode: 'cn',\r\n      order: 965,\r\n      totalSpent: 3876.92,\r\n      avatar: '/images/avatars/6.png'\r\n    },\r\n    {\r\n      id: 7,\r\n      customer: 'Lizzie Nicholes',\r\n      customerId: '516109',\r\n      email: '<EMAIL>',\r\n      country: 'Brazil',\r\n      countryFlag: '/images/cards/brazil.png',\r\n      countryCode: 'br',\r\n      order: 514,\r\n      totalSpent: 7936.85,\r\n      avatar: '/images/avatars/7.png'\r\n    },\r\n    {\r\n      id: 8,\r\n      customer: 'Amabel Scullion',\r\n      customerId: '403666',\r\n      email: '<EMAIL>',\r\n      country: 'Australia',\r\n      countryFlag: '/images/cards/australia.png',\r\n      countryCode: 'gt',\r\n      order: 584,\r\n      totalSpent: 4150.97,\r\n      avatar: '/images/avatars/8.png'\r\n    },\r\n    {\r\n      id: 9,\r\n      customer: 'Zeke Arton',\r\n      customerId: '895280',\r\n      email: '<EMAIL>',\r\n      country: 'Australia',\r\n      countryFlag: '/images/cards/australia.png',\r\n      countryCode: 'ua',\r\n      order: 539,\r\n      totalSpent: 3430.05,\r\n      avatar: '/images/avatars/1.png'\r\n    },\r\n    {\r\n      id: 10,\r\n      customer: 'Rosy Medlicott',\r\n      customerId: '199195',\r\n      email: '<EMAIL>',\r\n      country: 'Australia',\r\n      countryFlag: '/images/cards/australia.png',\r\n      countryCode: 'ru',\r\n      order: 4,\r\n      totalSpent: 8646.75,\r\n      avatar: '/images/avatars/2.png'\r\n    },\r\n    {\r\n      id: 11,\r\n      customer: 'Elene Esp',\r\n      customerId: '317063',\r\n      email: '<EMAIL>',\r\n      country: 'China',\r\n      countryFlag: '/images/cards/china.png',\r\n      countryCode: 'cn',\r\n      order: 602,\r\n      totalSpent: 5807.99,\r\n      avatar: '/images/avatars/3.png'\r\n    },\r\n    {\r\n      id: 12,\r\n      customer: 'Cal Lavington',\r\n      customerId: '999318',\r\n      email: '<EMAIL>',\r\n      country: 'China',\r\n      countryFlag: '/images/cards/china.png',\r\n      countryCode: 'bo',\r\n      order: 779,\r\n      totalSpent: 6677.41,\r\n      avatar: '/images/avatars/4.png'\r\n    },\r\n    {\r\n      id: 13,\r\n      customer: 'Merrick Antcliffe',\r\n      customerId: '804513',\r\n      email: '<EMAIL>',\r\n      country: 'China',\r\n      countryFlag: '/images/cards/china.png',\r\n      countryCode: 'cn',\r\n      order: 267,\r\n      totalSpent: 3340.41,\r\n      avatar: '/images/avatars/5.png'\r\n    },\r\n    {\r\n      id: 14,\r\n      customer: 'Price Tremathack',\r\n      customerId: '155681',\r\n      email: '<EMAIL>',\r\n      country: 'Australia',\r\n      countryFlag: '/images/cards/australia.png',\r\n      countryCode: 'ru',\r\n      order: 611,\r\n      totalSpent: 5768.17,\r\n      avatar: '/images/avatars/6.png'\r\n    },\r\n    {\r\n      id: 15,\r\n      customer: 'Leesa Habershaw',\r\n      customerId: '490182',\r\n      email: '<EMAIL>',\r\n      country: 'China',\r\n      countryFlag: '/images/cards/china.png',\r\n      countryCode: 'jp',\r\n      order: 90,\r\n      totalSpent: 4488.03,\r\n      avatar: '/images/avatars/7.png'\r\n    },\r\n    {\r\n      id: 16,\r\n      customer: 'Jeana Quincey',\r\n      customerId: '760428',\r\n      email: '<EMAIL>',\r\n      country: 'Australia',\r\n      countryFlag: '/images/cards/australia.png',\r\n      countryCode: 'ua',\r\n      order: 597,\r\n      totalSpent: 6936.49,\r\n      avatar: '/images/avatars/1.png'\r\n    },\r\n    {\r\n      id: 17,\r\n      customer: 'Emmott Hise',\r\n      customerId: '675190',\r\n      email: '<EMAIL>',\r\n      country: 'China',\r\n      countryFlag: '/images/cards/china.png',\r\n      countryCode: 'cn',\r\n      order: 30,\r\n      totalSpent: 7994.11,\r\n      avatar: '/images/avatars/2.png'\r\n    },\r\n    {\r\n      id: 18,\r\n      customer: 'Griffith Weeke',\r\n      customerId: '601134',\r\n      email: '<EMAIL>',\r\n      country: 'United States',\r\n      countryFlag: '/images/cards/us.png',\r\n      countryCode: 'id',\r\n      order: 322,\r\n      totalSpent: 5710.25,\r\n      avatar: '/images/avatars/3.png'\r\n    },\r\n    {\r\n      id: 19,\r\n      customer: 'Ali Barnardo',\r\n      customerId: '908144',\r\n      email: '<EMAIL>',\r\n      country: 'France',\r\n      countryFlag: '/images/cards/france.png',\r\n      countryCode: 'mx',\r\n      order: 863,\r\n      totalSpent: 7537.74,\r\n      avatar: '/images/avatars/4.png'\r\n    },\r\n    {\r\n      id: 20,\r\n      customer: 'Powell Wornham',\r\n      customerId: '528288',\r\n      email: '<EMAIL>',\r\n      country: 'France',\r\n      countryFlag: '/images/cards/france.png',\r\n      countryCode: 'cz',\r\n      order: 812,\r\n      totalSpent: 7801.46,\r\n      avatar: '/images/avatars/5.png'\r\n    },\r\n    {\r\n      id: 21,\r\n      customer: 'Miltie Ganniclifft',\r\n      customerId: '573210',\r\n      email: '<EMAIL>',\r\n      country: 'Australia',\r\n      countryFlag: '/images/cards/australia.png',\r\n      countryCode: 'ru',\r\n      order: 705,\r\n      totalSpent: 1371.44,\r\n      avatar: '/images/avatars/6.png'\r\n    },\r\n    {\r\n      id: 22,\r\n      customer: 'Tabbatha Duinbleton',\r\n      customerId: '473511',\r\n      email: '<EMAIL>',\r\n      country: 'United States',\r\n      countryFlag: '/images/cards/us.png',\r\n      countryCode: 'us',\r\n      order: 956,\r\n      totalSpent: 8632.52,\r\n      avatar: '/images/avatars/7.png'\r\n    },\r\n    {\r\n      id: 23,\r\n      customer: 'Maurizia Abel',\r\n      customerId: '676743',\r\n      email: '<EMAIL>',\r\n      country: 'Australia',\r\n      countryFlag: '/images/cards/australia.png',\r\n      countryCode: 'my',\r\n      order: 326,\r\n      totalSpent: 7241.74,\r\n      avatar: '/images/avatars/8.png'\r\n    },\r\n    {\r\n      id: 24,\r\n      customer: 'Amargo Fliege',\r\n      customerId: '381698',\r\n      email: '<EMAIL>',\r\n      country: 'United States',\r\n      countryFlag: '/images/cards/us.png',\r\n      countryCode: 'id',\r\n      order: 748,\r\n      totalSpent: 5821.27,\r\n      avatar: '/images/avatars/1.png'\r\n    },\r\n    {\r\n      id: 25,\r\n      customer: 'Shayla Tarplee',\r\n      customerId: '865989',\r\n      email: '<EMAIL>',\r\n      country: 'India',\r\n      countryFlag: '/images/cards/india.png',\r\n      countryCode: 'ng',\r\n      order: 535,\r\n      totalSpent: 900.54,\r\n      avatar: '/images/avatars/2.png'\r\n    },\r\n    {\r\n      id: 26,\r\n      customer: 'Kassey Cutting',\r\n      customerId: '545661',\r\n      email: '<EMAIL>',\r\n      country: 'United States',\r\n      countryFlag: '/images/cards/us.png',\r\n      countryCode: 'id',\r\n      order: 645,\r\n      totalSpent: 3200.38,\r\n      avatar: '/images/avatars/3.png'\r\n    },\r\n    {\r\n      id: 27,\r\n      customer: 'Blaire Hillaby',\r\n      customerId: '408852',\r\n      email: '<EMAIL>',\r\n      country: 'China',\r\n      countryFlag: '/images/cards/china.png',\r\n      countryCode: 'cl',\r\n      order: 709,\r\n      totalSpent: 376.46,\r\n      avatar: '/images/avatars/4.png'\r\n    },\r\n    {\r\n      id: 28,\r\n      customer: 'Taryn Ducker',\r\n      customerId: '486325',\r\n      email: '<EMAIL>',\r\n      country: 'Australia',\r\n      countryFlag: '/images/cards/australia.png',\r\n      countryCode: 'bt',\r\n      order: 535,\r\n      totalSpent: 3654.39,\r\n      avatar: '/images/avatars/5.png'\r\n    },\r\n    {\r\n      id: 29,\r\n      customer: 'Maddie Witherop',\r\n      customerId: '137049',\r\n      email: '<EMAIL>',\r\n      country: 'China',\r\n      countryFlag: '/images/cards/china.png',\r\n      countryCode: 'cn',\r\n      order: 763,\r\n      totalSpent: 1136.68,\r\n      avatar: '/images/avatars/6.png'\r\n    },\r\n    {\r\n      id: 30,\r\n      customer: 'Brooke Pattemore',\r\n      customerId: '985599',\r\n      email: '<EMAIL>',\r\n      country: 'Brazil',\r\n      countryFlag: '/images/cards/brazil.png',\r\n      countryCode: 'br',\r\n      order: 63,\r\n      totalSpent: 1955.91,\r\n      avatar: '/images/avatars/7.png'\r\n    },\r\n    {\r\n      id: 31,\r\n      customer: 'Mordy Dockerty',\r\n      customerId: '178466',\r\n      email: '<EMAIL>',\r\n      country: 'Brazil',\r\n      countryFlag: '/images/cards/brazil.png',\r\n      countryCode: 'se',\r\n      order: 452,\r\n      totalSpent: 191.11,\r\n      avatar: '/images/avatars/1.png'\r\n    },\r\n    {\r\n      id: 32,\r\n      customer: 'Clemmie Trowel',\r\n      customerId: '871402',\r\n      email: '<EMAIL>',\r\n      country: 'China',\r\n      countryFlag: '/images/cards/china.png',\r\n      countryCode: 'cl',\r\n      order: 415,\r\n      totalSpent: 5285.45,\r\n      avatar: '/images/avatars/7.png'\r\n    },\r\n    {\r\n      id: 33,\r\n      customer: 'Dehlia Shellard',\r\n      customerId: '642834',\r\n      email: '<EMAIL>',\r\n      country: 'France',\r\n      countryFlag: '/images/cards/france.png',\r\n      countryCode: 'cz',\r\n      order: 651,\r\n      totalSpent: 4284.88,\r\n      avatar: '/images/avatars/2.png'\r\n    },\r\n    {\r\n      id: 34,\r\n      customer: 'Neila Juggings',\r\n      customerId: '471692',\r\n      email: '<EMAIL>',\r\n      country: 'Brazil',\r\n      countryFlag: '/images/cards/brazil.png',\r\n      countryCode: 'ke',\r\n      order: 219,\r\n      totalSpent: 6698.44,\r\n      avatar: '/images/avatars/3.png'\r\n    },\r\n    {\r\n      id: 35,\r\n      customer: 'Ellsworth Dunnan',\r\n      customerId: '295906',\r\n      email: '<EMAIL>',\r\n      country: 'Brazil',\r\n      countryFlag: '/images/cards/brazil.png',\r\n      countryCode: 'br',\r\n      order: 11,\r\n      totalSpent: 3496.34,\r\n      avatar: '/images/avatars/4.png'\r\n    },\r\n    {\r\n      id: 36,\r\n      customer: 'Kassandra Cossentine',\r\n      customerId: '979702',\r\n      email: '<EMAIL>',\r\n      country: 'United States',\r\n      countryFlag: '/images/cards/us.png',\r\n      countryCode: 'id',\r\n      order: 316,\r\n      totalSpent: 5328.02,\r\n      avatar: '/images/avatars/7.png'\r\n    },\r\n    {\r\n      id: 37,\r\n      customer: 'Hugibert Merigeau',\r\n      customerId: '231745',\r\n      email: '<EMAIL>',\r\n      country: 'Australia',\r\n      countryFlag: '/images/cards/australia.png',\r\n      countryCode: 'pe',\r\n      order: 931,\r\n      totalSpent: 5868.06,\r\n      avatar: '/images/avatars/5.png'\r\n    },\r\n    {\r\n      id: 38,\r\n      customer: 'Constantina Charter',\r\n      customerId: '259786',\r\n      email: '<EMAIL>',\r\n      country: 'France',\r\n      countryFlag: '/images/cards/france.png',\r\n      countryCode: 'cz',\r\n      order: 30,\r\n      totalSpent: 4134.97,\r\n      avatar: '/images/avatars/1.png'\r\n    },\r\n    {\r\n      id: 39,\r\n      customer: 'Charleen Langsbury',\r\n      customerId: '794373',\r\n      email: '<EMAIL>',\r\n      country: 'Brazil',\r\n      countryFlag: '/images/cards/brazil.png',\r\n      countryCode: 'br',\r\n      order: 215,\r\n      totalSpent: 1869.06,\r\n      avatar: '/images/avatars/6.png'\r\n    },\r\n    {\r\n      id: 40,\r\n      customer: 'Sande Ferrar',\r\n      customerId: '949483',\r\n      email: '<EMAIL>',\r\n      countryFlag: '/images/cards/china.png',\r\n      country: 'China',\r\n      countryCode: 'bo',\r\n      order: 696,\r\n      totalSpent: 2585.57,\r\n      avatar: '/images/avatars/7.png'\r\n    },\r\n    {\r\n      id: 41,\r\n      customer: 'Lonnard Najara',\r\n      customerId: '225529',\r\n      email: '<EMAIL>',\r\n      country: 'Australia',\r\n      countryFlag: '/images/cards/australia.png',\r\n      countryCode: 'bd',\r\n      order: 956,\r\n      totalSpent: 1741.83,\r\n      avatar: '/images/avatars/8.png'\r\n    },\r\n    {\r\n      id: 42,\r\n      customer: 'Niko Sharpling',\r\n      customerId: '184711',\r\n      email: '<EMAIL>',\r\n      country: 'China',\r\n      countryFlag: '/images/cards/china.png',\r\n      countryCode: 'cn',\r\n      order: 172,\r\n      totalSpent: 1733.66,\r\n      avatar: '/images/avatars/1.png'\r\n    },\r\n    {\r\n      id: 43,\r\n      customer: 'Malinde Derricoat',\r\n      customerId: '272711',\r\n      email: '<EMAIL>',\r\n      country: 'India',\r\n      countryFlag: '/images/cards/india.png',\r\n      countryCode: 'ng',\r\n      order: 822,\r\n      totalSpent: 3930.51,\r\n      avatar: '/images/avatars/2.png'\r\n    },\r\n    {\r\n      id: 44,\r\n      customer: 'Kelsey Muskett',\r\n      customerId: '236093',\r\n      email: '<EMAIL>',\r\n      country: 'India',\r\n      countryFlag: '/images/cards/india.png',\r\n      countryCode: 'ca',\r\n      order: 51,\r\n      totalSpent: 4638.94,\r\n      avatar: '/images/avatars/3.png'\r\n    },\r\n    {\r\n      id: 45,\r\n      customer: 'Darcey Gorghetto',\r\n      customerId: '582408',\r\n      email: '<EMAIL>',\r\n      country: 'China',\r\n      countryFlag: '/images/cards/china.png',\r\n      countryCode: 'cn',\r\n      order: 559,\r\n      totalSpent: 3614.0,\r\n      avatar: '/images/avatars/4.png'\r\n    },\r\n    {\r\n      id: 46,\r\n      customer: 'Jody Stace',\r\n      customerId: '343364',\r\n      email: '<EMAIL>',\r\n      country: 'China',\r\n      countryFlag: '/images/cards/china.png',\r\n      countryCode: 'cn',\r\n      order: 945,\r\n      totalSpent: 5413.53,\r\n      avatar: '/images/avatars/5.png'\r\n    },\r\n    {\r\n      id: 47,\r\n      customer: 'Rudyard Prangnell',\r\n      customerId: '811348',\r\n      email: '<EMAIL>',\r\n      country: 'Australia',\r\n      countryFlag: '/images/cards/australia.png',\r\n      countryCode: 'ru',\r\n      order: 149,\r\n      totalSpent: 589.72,\r\n      avatar: '/images/avatars/1.png'\r\n    },\r\n    {\r\n      id: 48,\r\n      customer: 'Tanner Irdale',\r\n      customerId: '855725',\r\n      email: '<EMAIL>',\r\n      country: 'China',\r\n      countryFlag: '/images/cards/china.png',\r\n      countryCode: 'cn',\r\n      order: 438,\r\n      totalSpent: 8949.26,\r\n      avatar: '/images/avatars/6.png'\r\n    },\r\n    {\r\n      id: 49,\r\n      customer: 'Eran Galgey',\r\n      customerId: '804218',\r\n      email: '<EMAIL>',\r\n      country: 'United States',\r\n      countryFlag: '/images/cards/us.png',\r\n      countryCode: 'id',\r\n      order: 716,\r\n      totalSpent: 4466.54,\r\n      avatar: '/images/avatars/7.png'\r\n    },\r\n    {\r\n      id: 50,\r\n      customer: 'Julianne Lavalde',\r\n      customerId: '670044',\r\n      email: '<EMAIL>',\r\n      country: 'United States',\r\n      countryFlag: '/images/cards/us.png',\r\n      countryCode: 'pl',\r\n      order: 307,\r\n      totalSpent: 4382.72,\r\n      avatar: '/images/avatars/1.png'\r\n    },\r\n    {\r\n      id: 51,\r\n      customer: 'Hernando Stolte',\r\n      customerId: '804269',\r\n      email: '<EMAIL>',\r\n      country: 'United States',\r\n      countryFlag: '/images/cards/us.png',\r\n      countryCode: 'us',\r\n      order: 684,\r\n      totalSpent: 4671.06,\r\n      avatar: '/images/avatars/2.png'\r\n    },\r\n    {\r\n      id: 52,\r\n      customer: 'Mommy Beardsdale',\r\n      customerId: '711203',\r\n      email: '<EMAIL>',\r\n      country: 'United States',\r\n      countryFlag: '/images/cards/us.png',\r\n      countryCode: 'pt',\r\n      order: 315,\r\n      totalSpent: 6261.53,\r\n      avatar: '/images/avatars/2.png'\r\n    },\r\n    {\r\n      id: 53,\r\n      customer: 'Edsel Wildbore',\r\n      customerId: '745457',\r\n      email: '<EMAIL>',\r\n      country: 'United States',\r\n      countryFlag: '/images/cards/us.png',\r\n      countryCode: 'id',\r\n      order: 797,\r\n      totalSpent: 741.89,\r\n      avatar: '/images/avatars/3.png'\r\n    },\r\n    {\r\n      id: 54,\r\n      customer: 'Iseabal Idney',\r\n      customerId: '560446',\r\n      email: '<EMAIL>',\r\n      country: 'China',\r\n      countryFlag: '/images/cards/china.png',\r\n      countryCode: 'cn',\r\n      order: 145,\r\n      totalSpent: 4360.35,\r\n      avatar: '/images/avatars/4.png'\r\n    },\r\n    {\r\n      id: 55,\r\n      customer: 'Barbi Jest',\r\n      customerId: '519637',\r\n      email: '<EMAIL>',\r\n      country: 'Australia',\r\n      countryFlag: '/images/cards/australia.png',\r\n      countryCode: 'co',\r\n      order: 574,\r\n      totalSpent: 8328.19,\r\n      avatar: '/images/avatars/5.png'\r\n    },\r\n    {\r\n      id: 56,\r\n      customer: 'Paddie Grogan',\r\n      customerId: '915392',\r\n      country: 'India',\r\n      countryFlag: '/images/cards/india.png',\r\n      email: '<EMAIL>',\r\n      countryCode: 'eg',\r\n      order: 948,\r\n      totalSpent: 9899.06,\r\n      avatar: '/images/avatars/6.png'\r\n    },\r\n    {\r\n      id: 57,\r\n      customer: 'Lem Exell',\r\n      customerId: '856323',\r\n      email: '<EMAIL>',\r\n      country: 'Australia',\r\n      countryFlag: '/images/cards/australia.png',\r\n      countryCode: 'tz',\r\n      order: 541,\r\n      totalSpent: 9285.65,\r\n      avatar: '/images/avatars/7.png'\r\n    },\r\n    {\r\n      id: 58,\r\n      customer: 'Starlin Baldassi',\r\n      customerId: '696538',\r\n      email: '<EMAIL>',\r\n      country: 'Australia',\r\n      countryFlag: '/images/cards/australia.png',\r\n      countryCode: 'ru',\r\n      order: 99,\r\n      totalSpent: 3660.8,\r\n      avatar: '/images/avatars/8.png'\r\n    },\r\n    {\r\n      id: 59,\r\n      customer: 'Marjie Badman',\r\n      customerId: '875646',\r\n      email: '<EMAIL>',\r\n      country: 'Australia',\r\n      countryFlag: '/images/cards/australia.png',\r\n      countryCode: 'ru',\r\n      order: 108,\r\n      totalSpent: 1978.61,\r\n      avatar: '/images/avatars/1.png'\r\n    },\r\n    {\r\n      id: 60,\r\n      customer: 'Flossi McLaverty',\r\n      customerId: '617163',\r\n      email: '<EMAIL>',\r\n      country: 'China',\r\n      countryFlag: '/images/cards/china.png',\r\n      countryCode: 'cn',\r\n      order: 483,\r\n      totalSpent: 772.98,\r\n      avatar: '/images/avatars/2.png'\r\n    },\r\n    {\r\n      id: 61,\r\n      customer: 'Norri Dillinton',\r\n      customerId: '123210',\r\n      email: '<EMAIL>',\r\n      country: 'China',\r\n      countryFlag: '/images/cards/china.png',\r\n      countryCode: 'mk',\r\n      order: 69,\r\n      totalSpent: 4227.77,\r\n      avatar: '/images/avatars/3.png'\r\n    },\r\n    {\r\n      id: 62,\r\n      customer: 'Aloysius Lukas',\r\n      customerId: '766292',\r\n      email: '<EMAIL>',\r\n      country: 'France',\r\n      countryFlag: '/images/cards/france.png',\r\n      countryCode: 'fr',\r\n      order: 147,\r\n      totalSpent: 6637.38,\r\n      avatar: '/images/avatars/4.png'\r\n    },\r\n    {\r\n      id: 63,\r\n      customer: 'Rochell Cockill',\r\n      customerId: '100696',\r\n      email: '<EMAIL>',\r\n      country: 'United States',\r\n      countryFlag: '/images/cards/us.png',\r\n      countryCode: 'id',\r\n      order: 444,\r\n      totalSpent: 1730.64,\r\n      avatar: '/images/avatars/5.png'\r\n    },\r\n    {\r\n      id: 64,\r\n      customer: 'Emma Greensall',\r\n      customerId: '792768',\r\n      email: '<EMAIL>',\r\n      country: 'Australia',\r\n      countryFlag: '/images/cards/australia.png',\r\n      countryCode: 'ru',\r\n      order: 831,\r\n      totalSpent: 9996.22,\r\n      avatar: '/images/avatars/6.png'\r\n    },\r\n    {\r\n      id: 65,\r\n      customer: 'Jodi Malyan',\r\n      customerId: '996390',\r\n      email: '<EMAIL>',\r\n      country: 'China',\r\n      countryFlag: '/images/cards/china.png',\r\n      countryCode: 'fi',\r\n      order: 311,\r\n      totalSpent: 3459.82,\r\n      avatar: '/images/avatars/7.png'\r\n    },\r\n    {\r\n      id: 66,\r\n      customer: 'Zed Rawe',\r\n      customerId: '343593',\r\n      email: '<EMAIL>',\r\n      country: 'China',\r\n      countryFlag: '/images/cards/china.png',\r\n      countryCode: 'ly',\r\n      order: 473,\r\n      totalSpent: 5218.22,\r\n      avatar: '/images/avatars/2.png'\r\n    },\r\n    {\r\n      id: 67,\r\n      customer: 'Thomasine Vasentsov',\r\n      customerId: '988015',\r\n      email: '<EMAIL>',\r\n      country: 'China',\r\n      countryFlag: '/images/cards/china.png',\r\n      countryCode: 'ar',\r\n      order: 752,\r\n      totalSpent: 5984.53,\r\n      avatar: '/images/avatars/3.png'\r\n    },\r\n    {\r\n      id: 68,\r\n      customer: 'Janice Large',\r\n      customerId: '270658',\r\n      email: '<EMAIL>',\r\n      country: 'China',\r\n      countryFlag: '/images/cards/china.png',\r\n      countryCode: 'no',\r\n      order: 582,\r\n      totalSpent: 5565.85,\r\n      avatar: '/images/avatars/4.png'\r\n    },\r\n    {\r\n      id: 69,\r\n      customer: 'Tadeo Blasio',\r\n      customerId: '208862',\r\n      email: '<EMAIL>',\r\n      country: 'China',\r\n      countryFlag: '/images/cards/china.png',\r\n      countryCode: 'cn',\r\n      order: 751,\r\n      totalSpent: 9042.56,\r\n      avatar: '/images/avatars/5.png'\r\n    },\r\n    {\r\n      id: 70,\r\n      customer: 'Raul Onele',\r\n      customerId: '895818',\r\n      email: '<EMAIL>',\r\n      country: 'China',\r\n      countryFlag: '/images/cards/china.png',\r\n      countryCode: 'pe',\r\n      order: 689,\r\n      totalSpent: 4508.42,\r\n      avatar: '/images/avatars/6.png'\r\n    },\r\n    {\r\n      id: 71,\r\n      customer: 'Rolf Comellini',\r\n      customerId: '292654',\r\n      email: '<EMAIL>',\r\n      country: 'United States',\r\n      countryFlag: '/images/cards/us.png',\r\n      countryCode: 'id',\r\n      order: 837,\r\n      totalSpent: 6379.88,\r\n      avatar: '/images/avatars/7.png'\r\n    },\r\n    {\r\n      id: 72,\r\n      customer: 'Feliza Birchenough',\r\n      customerId: '974560',\r\n      email: '<EMAIL>',\r\n      country: 'China',\r\n      countryFlag: '/images/cards/china.png',\r\n      countryCode: 'ec',\r\n      order: 724,\r\n      totalSpent: 2933.59,\r\n      avatar: '/images/avatars/2.png'\r\n    },\r\n    {\r\n      id: 73,\r\n      customer: 'Elsinore Daltry',\r\n      customerId: '152193',\r\n      email: '<EMAIL>',\r\n      country: 'Brazil',\r\n      countryFlag: '/images/cards/brazil.png',\r\n      countryCode: 'br',\r\n      order: 455,\r\n      totalSpent: 724.68,\r\n      avatar: '/images/avatars/2.png'\r\n    },\r\n    {\r\n      id: 74,\r\n      customer: 'Roseann Serck',\r\n      customerId: '772228',\r\n      email: '<EMAIL>',\r\n      country: 'India',\r\n      countryFlag: '/images/cards/india.png',\r\n      countryCode: 'rs',\r\n      order: 51,\r\n      totalSpent: 8287.03,\r\n      avatar: '/images/avatars/3.png'\r\n    },\r\n    {\r\n      id: 75,\r\n      customer: 'Yank Luddy',\r\n      customerId: '586615',\r\n      email: '<EMAIL>',\r\n      country: 'United States',\r\n      countryFlag: '/images/cards/us.png',\r\n      countryCode: 'pt',\r\n      order: 462,\r\n      totalSpent: 9157.04,\r\n      avatar: '/images/avatars/4.png'\r\n    },\r\n    {\r\n      id: 76,\r\n      customer: 'Sloan Huskisson',\r\n      customerId: '762754',\r\n      email: '<EMAIL>',\r\n      country: 'China',\r\n      countryFlag: '/images/cards/china.png',\r\n      countryCode: 'do',\r\n      order: 952,\r\n      totalSpent: 6106.41,\r\n      avatar: '/images/avatars/5.png'\r\n    },\r\n    {\r\n      id: 77,\r\n      customer: 'Livy Lattimore',\r\n      customerId: '258911',\r\n      email: '<EMAIL>',\r\n      country: 'United States',\r\n      countryFlag: '/images/cards/us.png',\r\n      countryCode: 'id',\r\n      order: 794,\r\n      totalSpent: 9053.56,\r\n      avatar: '/images/avatars/6.png'\r\n    },\r\n    {\r\n      id: 78,\r\n      customer: 'Lanette Deble',\r\n      customerId: '890051',\r\n      email: '<EMAIL>',\r\n      country: 'China',\r\n      countryFlag: '/images/cards/china.png',\r\n      countryCode: 'hk',\r\n      order: 454,\r\n      totalSpent: 8180.2,\r\n      avatar: '/images/avatars/7.png'\r\n    },\r\n    {\r\n      id: 79,\r\n      customer: 'Juliet Gypps',\r\n      customerId: '493646',\r\n      email: '<EMAIL>',\r\n      country: 'United States',\r\n      countryFlag: '/images/cards/us.png',\r\n      countryCode: 'id',\r\n      order: 320,\r\n      totalSpent: 210.84,\r\n      avatar: '/images/avatars/8.png'\r\n    },\r\n    {\r\n      id: 80,\r\n      customer: 'Tome Joliffe',\r\n      customerId: '356230',\r\n      email: '<EMAIL>',\r\n      country: 'France',\r\n      countryFlag: '/images/cards/france.png',\r\n      countryCode: 'mx',\r\n      order: 515,\r\n      totalSpent: 8571.28,\r\n      avatar: '/images/avatars/1.png'\r\n    },\r\n    {\r\n      id: 81,\r\n      customer: 'Joel Hamil',\r\n      customerId: '337022',\r\n      email: '<EMAIL>',\r\n      country: 'China',\r\n      countryFlag: '/images/cards/china.png',\r\n      countryCode: 'se',\r\n      order: 906,\r\n      totalSpent: 620.57,\r\n      avatar: '/images/avatars/2.png'\r\n    },\r\n    {\r\n      id: 82,\r\n      customer: 'Hagen Digance',\r\n      customerId: '864064',\r\n      email: '<EMAIL>',\r\n      country: 'United States',\r\n      countryFlag: '/images/cards/us.png',\r\n      countryCode: 'id',\r\n      order: 519,\r\n      totalSpent: 332.44,\r\n      avatar: '/images/avatars/3.png'\r\n    },\r\n    {\r\n      id: 83,\r\n      customer: 'Kristo Wagstaff',\r\n      customerId: '550008',\r\n      email: '<EMAIL>',\r\n      country: 'China',\r\n      countryFlag: '/images/cards/china.png',\r\n      countryCode: 'cn',\r\n      order: 313,\r\n      totalSpent: 2481.6,\r\n      avatar: '/images/avatars/4.png'\r\n    },\r\n    {\r\n      id: 84,\r\n      customer: 'Gibbie Dysert',\r\n      customerId: '778429',\r\n      email: '<EMAIL>',\r\n      country: 'China',\r\n      countryFlag: '/images/cards/china.png',\r\n      countryCode: 'ni',\r\n      order: 623,\r\n      totalSpent: 8466.96,\r\n      avatar: '/images/avatars/5.png'\r\n    },\r\n    {\r\n      id: 85,\r\n      customer: 'Michale Britton',\r\n      customerId: '158581',\r\n      email: '<EMAIL>',\r\n      country: 'China',\r\n      countryFlag: '/images/cards/china.png',\r\n      countryCode: 'cn',\r\n      order: 835,\r\n      totalSpent: 9048.31,\r\n      avatar: '/images/avatars/6.png'\r\n    },\r\n    {\r\n      id: 86,\r\n      customer: 'Hiram Hoys',\r\n      customerId: '747948',\r\n      email: '<EMAIL>',\r\n      country: 'India',\r\n      countryFlag: '/images/cards/india.png',\r\n      countryCode: 'eg',\r\n      order: 361,\r\n      totalSpent: 9159.23,\r\n      avatar: '/images/avatars/7.png'\r\n    },\r\n    {\r\n      id: 87,\r\n      customer: 'Tobin Bassick',\r\n      customerId: '165827',\r\n      email: '<EMAIL>',\r\n      country: 'India',\r\n      countryFlag: '/images/cards/india.png',\r\n      countryCode: 'jo',\r\n      order: 527,\r\n      totalSpent: 9289.92,\r\n      avatar: '/images/avatars/1.png'\r\n    },\r\n    {\r\n      id: 88,\r\n      customer: 'Mikol Caskey',\r\n      customerId: '533641',\r\n      email: '<EMAIL>',\r\n      country: 'India',\r\n      countryFlag: '/images/cards/india.png',\r\n      countryCode: 'in',\r\n      order: 25,\r\n      totalSpent: 4920.68,\r\n      avatar: '/images/avatars/2.png'\r\n    },\r\n    {\r\n      id: 89,\r\n      customer: 'Cris Donkersley',\r\n      customerId: '997638',\r\n      email: '<EMAIL>',\r\n      country: 'China',\r\n      countryFlag: '/images/cards/china.png',\r\n      countryCode: 'cn',\r\n      order: 404,\r\n      totalSpent: 7369.58,\r\n      avatar: '/images/avatars/3.png'\r\n    },\r\n    {\r\n      id: 90,\r\n      customer: 'Valenka Turbill',\r\n      customerId: '179914',\r\n      email: '<EMAIL>',\r\n      country: 'Australia',\r\n      countryFlag: '/images/cards/australia.png',\r\n      countryCode: 'tm',\r\n      order: 550,\r\n      totalSpent: 9083.15,\r\n      avatar: '/images/avatars/4.png'\r\n    },\r\n    {\r\n      id: 91,\r\n      customer: 'Cherice Fairclough',\r\n      customerId: '467280',\r\n      email: '<EMAIL>',\r\n      country: 'United States',\r\n      countryFlag: '/images/cards/us.png',\r\n      countryCode: 'us',\r\n      order: 792,\r\n      totalSpent: 2634.36,\r\n      avatar: '/images/avatars/5.png'\r\n    },\r\n    {\r\n      id: 92,\r\n      customer: 'Lauritz Ramble',\r\n      customerId: '140146',\r\n      email: '<EMAIL>',\r\n      country: 'Australia',\r\n      countryFlag: '/images/cards/australia.png',\r\n      countryCode: 'ru',\r\n      order: 605,\r\n      totalSpent: 9381.83,\r\n      avatar: '/images/avatars/6.png'\r\n    },\r\n    {\r\n      id: 93,\r\n      customer: 'Goddard Fosher',\r\n      customerId: '398102',\r\n      email: '<EMAIL>',\r\n      country: 'China',\r\n      countryFlag: '/images/cards/china.png',\r\n      countryCode: 'cn',\r\n      order: 892,\r\n      totalSpent: 3957.06,\r\n      avatar: '/images/avatars/7.png'\r\n    },\r\n    {\r\n      id: 94,\r\n      customer: 'Darby Leming',\r\n      customerId: '178939',\r\n      email: '<EMAIL>',\r\n      country: 'United States',\r\n      countryFlag: '/images/cards/us.png',\r\n      countryCode: 'pl',\r\n      order: 894,\r\n      totalSpent: 1450.01,\r\n      avatar: '/images/avatars/3.png'\r\n    },\r\n    {\r\n      id: 95,\r\n      customer: 'Paulie Floch',\r\n      customerId: '855358',\r\n      email: '<EMAIL>',\r\n      country: 'United States',\r\n      countryFlag: '/images/cards/us.png',\r\n      countryCode: 'id',\r\n      order: 866,\r\n      totalSpent: 8713.73,\r\n      avatar: '/images/avatars/4.png'\r\n    },\r\n    {\r\n      id: 96,\r\n      customer: 'Raffaello Reaney',\r\n      customerId: '533341',\r\n      email: '<EMAIL>',\r\n      country: 'China',\r\n      countryFlag: '/images/cards/china.png',\r\n      countryCode: 'cn',\r\n      order: 145,\r\n      totalSpent: 8589.4,\r\n      avatar: '/images/avatars/5.png'\r\n    },\r\n    {\r\n      id: 97,\r\n      customer: 'Inger Weadick',\r\n      customerId: '902643',\r\n      email: '<EMAIL>',\r\n      country: 'United States',\r\n      countryFlag: '/images/cards/us.png',\r\n      countryCode: 'id',\r\n      order: 766,\r\n      totalSpent: 7119.15,\r\n      avatar: '/images/avatars/6.png'\r\n    },\r\n    {\r\n      id: 98,\r\n      customer: 'Brooke Tegler',\r\n      customerId: '137230',\r\n      email: '<EMAIL>',\r\n      country: 'Australia',\r\n      countryFlag: '/images/cards/australia.png',\r\n      countryCode: 'kp',\r\n      order: 70,\r\n      totalSpent: 4403.22,\r\n      avatar: '/images/avatars/7.png'\r\n    },\r\n    {\r\n      id: 99,\r\n      customer: 'Erny Picard',\r\n      customerId: '960955',\r\n      email: '<EMAIL>',\r\n      country: 'France',\r\n      countryFlag: '/images/cards/france.png',\r\n      countryCode: 'cz',\r\n      order: 471,\r\n      totalSpent: 7696.67,\r\n      avatar: '/images/avatars/1.png'\r\n    },\r\n    {\r\n      id: 100,\r\n      customer: 'Manon Fossick',\r\n      customerId: '478426',\r\n      email: '<EMAIL>',\r\n      country: 'China',\r\n      countryFlag: '/images/cards/china.png',\r\n      countryCode: 'jp',\r\n      order: 181,\r\n      totalSpent: 2838.35,\r\n      avatar: '/images/avatars/7.png'\r\n    }\r\n  ]\r\n}\r\n"], "names": [], "mappings": ";;;AAAO,MAAM,KAAK;IAChB,UAAU;QACR;YACE,IAAI;YACJ,aAAa;YACb,UAAU;YACV,OAAO;YACP,KAAK;YACL,OAAO;YACP,KAAK;YACL,QAAQ;YACR,OAAO;YACP,cAAc;QAChB;QACA;YACE,IAAI;YACJ,aAAa;YACb,UAAU;YACV,OAAO;YACP,KAAK;YACL,OAAO;YACP,KAAK;YACL,QAAQ;YACR,OAAO;YACP,cAAc;QAChB;QACA;YACE,IAAI;YACJ,aAAa;YACb,UAAU;YACV,OAAO;YACP,KAAK;YACL,OAAO;YACP,KAAK;YACL,QAAQ;YACR,OAAO;YACP,cAAc;QAChB;QACA;YACE,IAAI;YACJ,aAAa;YACb,UAAU;YACV,OAAO;YAC<PERSON>,KAAK;YACL,OAAO;YACP,KAAK;YACL,QAAQ;YACR,OAAO;YACP,cAAc;QAChB;QACA;YACE,IAAI;YACJ,aAAa;YACb,UAAU;YACV,OAAO;YACP,KAAK;YACL,OAAO;YACP,KAAK;YACL,QAAQ;YACR,OAAO;YACP,cAAc;QAChB;QACA;YACE,IAAI;YACJ,aAAa;YACb,UAAU;YACV,OAAO;YACP,KAAK;YACL,OAAO;YACP,KAAK;YACL,QAAQ;YACR,OAAO;YACP,cAAc;QAChB;QACA;YACE,IAAI;YACJ,aAAa;YACb,UAAU;YACV,OAAO;YACP,KAAK;YACL,OAAO;YACP,KAAK;YACL,QAAQ;YACR,OAAO;YACP,cAAc;QAChB;QACA;YACE,IAAI;YACJ,aAAa;YACb,UAAU;YACV,OAAO;YACP,KAAK;YACL,OAAO;YACP,KAAK;YACL,QAAQ;YACR,OAAO;YACP,cAAc;QAChB;QACA;YACE,IAAI;YACJ,aAAa;YACb,UAAU;YACV,OAAO;YACP,KAAK;YACL,OAAO;YACP,KAAK;YACL,QAAQ;YACR,OAAO;YACP,cAAc;QAChB;QACA;YACE,IAAI;YACJ,aAAa;YACb,UAAU;YACV,OAAO;YACP,KAAK;YACL,OAAO;YACP,KAAK;YACL,QAAQ;YACR,OAAO;YACP,cAAc;QAChB;QACA;YACE,IAAI;YACJ,aAAa;YACb,UAAU;YACV,OAAO;YACP,KAAK;YACL,OAAO;YACP,KAAK;YACL,QAAQ;YACR,OAAO;YACP,cAAc;QAChB;QACA;YACE,IAAI;YACJ,aAAa;YACb,UAAU;YACV,OAAO;YACP,KAAK;YACL,OAAO;YACP,KAAK;YACL,QAAQ;YACR,OAAO;YACP,cAAc;QAChB;QACA;YACE,IAAI;YACJ,aAAa;YACb,UAAU;YACV,OAAO;YACP,KAAK;YACL,OAAO;YACP,KAAK;YACL,QAAQ;YACR,OAAO;YACP,cAAc;QAChB;QACA;YACE,IAAI;YACJ,aAAa;YACb,UAAU;YACV,OAAO;YACP,KAAK;YACL,OAAO;YACP,KAAK;YACL,QAAQ;YACR,OAAO;YACP,cAAc;QAChB;QACA;YACE,IAAI;YACJ,aAAa;YACb,UAAU;YACV,OAAO;YACP,KAAK;YACL,OAAO;YACP,KAAK;YACL,QAAQ;YACR,OAAO;YACP,cAAc;QAChB;QACA;YACE,IAAI;YACJ,aAAa;YACb,UAAU;YACV,OAAO;YACP,KAAK;YACL,OAAO;YACP,KAAK;YACL,QAAQ;YACR,OAAO;YACP,cAAc;QAChB;QACA;YACE,IAAI;YACJ,aAAa;YACb,UAAU;YACV,OAAO;YACP,KAAK;YACL,OAAO;YACP,KAAK;YACL,QAAQ;YACR,OAAO;YACP,cAAc;QAChB;QACA;YACE,IAAI;YACJ,aAAa;YACb,UAAU;YACV,OAAO;YACP,KAAK;YACL,OAAO;YACP,KAAK;YACL,QAAQ;YACR,OAAO;YACP,cAAc;QAChB;QACA;YACE,IAAI;YACJ,aAAa;YACb,UAAU;YACV,OAAO;YACP,KAAK;YACL,OAAO;YACP,KAAK;YACL,QAAQ;YACR,OAAO;YACP,cAAc;QAChB;QACA;YACE,IAAI;YACJ,aAAa;YACb,UAAU;YACV,OAAO;YACP,KAAK;YACL,OAAO;YACP,KAAK;YACL,QAAQ;YACR,OAAO;YACP,cAAc;QAChB;QACA;YACE,IAAI;YACJ,aAAa;YACb,UAAU;YACV,OAAO;YACP,KAAK;YACL,OAAO;YACP,KAAK;YACL,QAAQ;YACR,OAAO;YACP,cAAc;QAChB;QACA;YACE,IAAI;YACJ,aAAa;YACb,UAAU;YACV,OAAO;YACP,KAAK;YACL,OAAO;YACP,KAAK;YACL,QAAQ;YACR,OAAO;YACP,cAAc;QAChB;QACA;YACE,IAAI;YACJ,aAAa;YACb,UAAU;YACV,OAAO;YACP,KAAK;YACL,OAAO;YACP,KAAK;YACL,QAAQ;YACR,OAAO;YACP,cAAc;QAChB;QACA;YACE,IAAI;YACJ,aAAa;YACb,UAAU;YACV,OAAO;YACP,KAAK;YACL,OAAO;YACP,KAAK;YACL,QAAQ;YACR,OAAO;YACP,cAAc;QAChB;QACA;YACE,IAAI;YACJ,aAAa;YACb,UAAU;YACV,OAAO;YACP,KAAK;YACL,OAAO;YACP,KAAK;YACL,QAAQ;YACR,OAAO;YACP,cAAc;QAChB;QACA;YACE,IAAI;YACJ,aAAa;YACb,UAAU;YACV,OAAO;YACP,KAAK;YACL,OAAO;YACP,KAAK;YACL,QAAQ;YACR,OAAO;YACP,cAAc;QAChB;QACA;YACE,IAAI;YACJ,aAAa;YACb,UAAU;YACV,OAAO;YACP,KAAK;YACL,OAAO;YACP,KAAK;YACL,QAAQ;YACR,OAAO;YACP,cAAc;QAChB;QACA;YACE,IAAI;YACJ,aAAa;YACb,UAAU;YACV,OAAO;YACP,KAAK;YACL,OAAO;YACP,KAAK;YACL,QAAQ;YACR,OAAO;YACP,cAAc;QAChB;QACA;YACE,IAAI;YACJ,aAAa;YACb,UAAU;YACV,OAAO;YACP,KAAK;YACL,OAAO;YACP,KAAK;YACL,QAAQ;YACR,OAAO;YACP,cAAc;QAChB;QACA;YACE,IAAI;YACJ,aAAa;YACb,UAAU;YACV,OAAO;YACP,KAAK;YACL,OAAO;YACP,KAAK;YACL,QAAQ;YACR,OAAO;YACP,cAAc;QAChB;QACA;YACE,IAAI;YACJ,aAAa;YACb,UAAU;YACV,OAAO;YACP,KAAK;YACL,OAAO;YACP,KAAK;YACL,QAAQ;YACR,OAAO;YACP,cAAc;QAChB;QACA;YACE,IAAI;YACJ,aAAa;YACb,UAAU;YACV,OAAO;YACP,KAAK;YACL,OAAO;YACP,KAAK;YACL,QAAQ;YACR,OAAO;YACP,cAAc;QAChB;QACA;YACE,IAAI;YACJ,aAAa;YACb,UAAU;YACV,OAAO;YACP,KAAK;YACL,OAAO;YACP,KAAK;YACL,QAAQ;YACR,OAAO;YACP,cAAc;QAChB;QACA;YACE,IAAI;YACJ,aAAa;YACb,UAAU;YACV,OAAO;YACP,KAAK;YACL,OAAO;YACP,KAAK;YACL,QAAQ;YACR,OAAO;YACP,cAAc;QAChB;QACA;YACE,IAAI;YACJ,aAAa;YACb,UAAU;YACV,OAAO;YACP,KAAK;YACL,OAAO;YACP,KAAK;YACL,QAAQ;YACR,OAAO;YACP,cAAc;QAChB;QACA;YACE,IAAI;YACJ,aAAa;YACb,UAAU;YACV,OAAO;YACP,KAAK;YACL,OAAO;YACP,KAAK;YACL,QAAQ;YACR,OAAO;YACP,cAAc;QAChB;QACA;YACE,IAAI;YACJ,aAAa;YACb,UAAU;YACV,OAAO;YACP,KAAK;YACL,OAAO;YACP,KAAK;YACL,QAAQ;YACR,OAAO;YACP,cAAc;QAChB;QACA;YACE,IAAI;YACJ,aAAa;YACb,UAAU;YACV,OAAO;YACP,KAAK;YACL,OAAO;YACP,KAAK;YACL,QAAQ;YACR,OAAO;YACP,cAAc;QAChB;QACA;YACE,IAAI;YACJ,aAAa;YACb,UAAU;YACV,OAAO;YACP,KAAK;YACL,OAAO;YACP,KAAK;YACL,QAAQ;YACR,OAAO;YACP,cAAc;QAChB;QACA;YACE,IAAI;YACJ,aAAa;YACb,UAAU;YACV,OAAO;YACP,KAAK;YACL,OAAO;YACP,KAAK;YACL,QAAQ;YACR,OAAO;YACP,cAAc;QAChB;QACA;YACE,IAAI;YACJ,aAAa;YACb,UAAU;YACV,OAAO;YACP,KAAK;YACL,OAAO;YACP,KAAK;YACL,QAAQ;YACR,OAAO;YACP,cAAc;QAChB;QACA;YACE,IAAI;YACJ,aAAa;YACb,UAAU;YACV,OAAO;YACP,KAAK;YACL,OAAO;YACP,KAAK;YACL,QAAQ;YACR,OAAO;YACP,cAAc;QAChB;QACA;YACE,IAAI;YACJ,aAAa;YACb,UAAU;YACV,OAAO;YACP,KAAK;YACL,OAAO;YACP,KAAK;YACL,QAAQ;YACR,OAAO;YACP,cAAc;QAChB;QACA;YACE,IAAI;YACJ,aAAa;YACb,UAAU;YACV,OAAO;YACP,KAAK;YACL,OAAO;YACP,KAAK;YACL,QAAQ;YACR,OAAO;YACP,cAAc;QAChB;QACA;YACE,IAAI;YACJ,aAAa;YACb,UAAU;YACV,OAAO;YACP,KAAK;YACL,OAAO;YACP,KAAK;YACL,QAAQ;YACR,OAAO;YACP,cAAc;QAChB;QACA;YACE,IAAI;YACJ,aAAa;YACb,UAAU;YACV,OAAO;YACP,KAAK;YACL,OAAO;YACP,KAAK;YACL,QAAQ;YACR,OAAO;YACP,cAAc;QAChB;QACA;YACE,IAAI;YACJ,aAAa;YACb,UAAU;YACV,OAAO;YACP,KAAK;YACL,OAAO;YACP,KAAK;YACL,QAAQ;YACR,OAAO;YACP,cAAc;QAChB;QACA;YACE,IAAI;YACJ,aAAa;YACb,UAAU;YACV,OAAO;YACP,KAAK;YACL,OAAO;YACP,KAAK;YACL,QAAQ;YACR,OAAO;YACP,cAAc;QAChB;QACA;YACE,IAAI;YACJ,aAAa;YACb,UAAU;YACV,OAAO;YACP,KAAK;YACL,OAAO;YACP,KAAK;YACL,QAAQ;YACR,OAAO;YACP,cAAc;QAChB;QACA;YACE,IAAI;YACJ,aAAa;YACb,UAAU;YACV,OAAO;YACP,KAAK;YACL,OAAO;YACP,KAAK;YACL,QAAQ;YACR,OAAO;YACP,cAAc;QAChB;KACD;IACD,SAAS;QACP;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,cAAc;YACd,UAAU;YACV,OAAO;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,MAAM;QACR;KACD;IACD,WAAW;QACT;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,QAAQ;YACR,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,QAAQ;YACR,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,QAAQ;YACR,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,QAAQ;YACR,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,QAAQ;YACR,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,QAAQ;YACR,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,QAAQ;YACR,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,QAAQ;YACR,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,QAAQ;YACR,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,QAAQ;YACR,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,QAAQ;YACR,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,QAAQ;YACR,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,QAAQ;YACR,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,QAAQ;YACR,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,QAAQ;YACR,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,QAAQ;YACR,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,QAAQ;YACR,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,QAAQ;YACR,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,QAAQ;YACR,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,QAAQ;YACR,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,QAAQ;YACR,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,QAAQ;YACR,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,QAAQ;YACR,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,QAAQ;YACR,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,QAAQ;YACR,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,QAAQ;YACR,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,QAAQ;YACR,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,QAAQ;YACR,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,QAAQ;YACR,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,QAAQ;YACR,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,QAAQ;YACR,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,QAAQ;YACR,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,QAAQ;YACR,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,QAAQ;YACR,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,QAAQ;YACR,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,QAAQ;YACR,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,QAAQ;YACR,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,QAAQ;YACR,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,QAAQ;YACR,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,QAAQ;YACR,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,QAAQ;YACR,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,QAAQ;YACR,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,QAAQ;YACR,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,QAAQ;YACR,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,QAAQ;YACR,OAAO;YACP,SAAS;QACX;KACD;IACD,WAAW;QACT;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,MAAM;YACN,MAAM;YACN,cAAc;QAChB;KACD;IACD,cAAc;QACZ;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,aAAa;YACb,SAAS;YACT,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,SAAS;YACT,aAAa;YACb,OAAO;YACP,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,OAAO;YACP,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;YACP,YAAY;YACZ,QAAQ;QACV;KACD;AACH", "debugId": null}}, {"offset": {"line": 5150, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/fake-db/apps/userList.js"], "sourcesContent": ["export const db = [\r\n  {\r\n    id: 1,\r\n    fullName: '<PERSON>lix<PERSON>',\r\n    company: 'Yotz PVT LTD',\r\n    role: 'editor',\r\n    username: 'gslixby0',\r\n    country: 'El Salvador',\r\n    contact: '(479) 232-9151',\r\n    email: '<EMAIL>',\r\n    currentPlan: 'enterprise',\r\n    status: 'inactive',\r\n    avatar: '',\r\n    avatarColor: 'primary',\r\n    billing: 'Auto Debit'\r\n  },\r\n  {\r\n    id: 2,\r\n    fullName: '<PERSON>sey Redmore',\r\n    company: 'Skinder PVT LTD',\r\n    role: 'author',\r\n    username: 'hredmore1',\r\n    country: 'Albania',\r\n    contact: '(472) 607-9137',\r\n    email: '<EMAIL>',\r\n    currentPlan: 'team',\r\n    status: 'pending',\r\n    avatar: '/images/avatars/3.png',\r\n    billing: 'Auto Debit'\r\n  },\r\n  {\r\n    id: 3,\r\n    fullName: '<PERSON><PERSON><PERSON>',\r\n    company: 'Oozz PVT LTD',\r\n    role: 'maintainer',\r\n    username: 'msicely2',\r\n    country: 'Russia',\r\n    contact: '(321) 264-4599',\r\n    email: '<EMAIL>',\r\n    currentPlan: 'enterprise',\r\n    status: 'active',\r\n    avatar: '/images/avatars/1.png',\r\n    billing: 'Auto Debit'\r\n  },\r\n  {\r\n    id: 4,\r\n    fullName: 'Cyrill Risby',\r\n    company: 'Oozz PVT LTD',\r\n    role: 'maintainer',\r\n    username: 'crisby3',\r\n    country: 'China',\r\n    contact: '(923) 690-6806',\r\n    email: '<EMAIL>',\r\n    currentPlan: 'team',\r\n    status: 'inactive',\r\n    avatar: '/images/avatars/3.png',\r\n    billing: 'Manual Paypal'\r\n  },\r\n  {\r\n    id: 5,\r\n    fullName: 'Maggy Hurran',\r\n    company: 'Aimbo PVT LTD',\r\n    role: 'subscriber',\r\n    username: 'mhurran4',\r\n    country: 'Pakistan',\r\n    contact: '(669) 914-1078',\r\n    email: '<EMAIL>',\r\n    currentPlan: 'enterprise',\r\n    status: 'pending',\r\n    avatar: '/images/avatars/1.png',\r\n    billing: 'Manual Cash'\r\n  },\r\n  {\r\n    id: 6,\r\n    fullName: 'Silvain Halstead',\r\n    company: 'Jaxbean PVT LTD',\r\n    role: 'author',\r\n    username: 'shalstead5',\r\n    country: 'China',\r\n    contact: '(958) 973-3093',\r\n    email: '<EMAIL>',\r\n    currentPlan: 'company',\r\n    status: 'active',\r\n    avatar: '',\r\n    avatarColor: 'error',\r\n    billing: 'Manual Cash'\r\n  },\r\n  {\r\n    id: 7,\r\n    fullName: 'Breena Gallemore',\r\n    company: 'Jazzy PVT LTD',\r\n    role: 'subscriber',\r\n    username: 'bgallemore6',\r\n    country: 'Canada',\r\n    contact: '(825) 977-8152',\r\n    email: '<EMAIL>',\r\n    currentPlan: 'company',\r\n    status: 'pending',\r\n    avatar: '',\r\n    avatarColor: 'warning',\r\n    billing: 'Auto Debit'\r\n  },\r\n  {\r\n    id: 8,\r\n    fullName: 'Kathryne Liger',\r\n    company: 'Pixoboo PVT LTD',\r\n    role: 'author',\r\n    username: 'kliger7',\r\n    country: 'France',\r\n    contact: '(187) 440-0934',\r\n    email: '<EMAIL>',\r\n    currentPlan: 'enterprise',\r\n    status: 'pending',\r\n    avatar: '/images/avatars/4.png',\r\n    billing: 'Manual Paypal'\r\n  },\r\n  {\r\n    id: 9,\r\n    fullName: 'Franz Scotfurth',\r\n    company: 'Tekfly PVT LTD',\r\n    role: 'subscriber',\r\n    username: 'fscotfurth8',\r\n    country: 'China',\r\n    contact: '(978) 146-5443',\r\n    email: '<EMAIL>',\r\n    currentPlan: 'team',\r\n    status: 'pending',\r\n    avatar: '/images/avatars/2.png',\r\n    billing: 'Auto Debit'\r\n  },\r\n  {\r\n    id: 10,\r\n    fullName: 'Jillene Bellany',\r\n    company: 'Gigashots PVT LTD',\r\n    role: 'maintainer',\r\n    username: 'jbellany9',\r\n    country: 'Jamaica',\r\n    contact: '(589) 284-6732',\r\n    email: '<EMAIL>',\r\n    currentPlan: 'company',\r\n    status: 'inactive',\r\n    avatar: '/images/avatars/5.png',\r\n    billing: 'Manual Cash'\r\n  },\r\n  {\r\n    id: 11,\r\n    fullName: 'Jonah Wharlton',\r\n    company: 'Eare PVT LTD',\r\n    role: 'subscriber',\r\n    username: 'jwharltona',\r\n    country: 'United States',\r\n    contact: '(176) 532-6824',\r\n    email: '<EMAIL>',\r\n    currentPlan: 'team',\r\n    status: 'inactive',\r\n    avatar: '/images/avatars/4.png',\r\n    billing: 'Auto Debit'\r\n  },\r\n  {\r\n    id: 12,\r\n    fullName: 'Seth Hallam',\r\n    company: 'Yakitri PVT LTD',\r\n    role: 'subscriber',\r\n    username: 'shallamb',\r\n    country: 'Peru',\r\n    contact: '(234) 464-0600',\r\n    email: '<EMAIL>',\r\n    currentPlan: 'team',\r\n    status: 'pending',\r\n    avatar: '/images/avatars/5.png',\r\n    billing: 'Manual Paypal'\r\n  },\r\n  {\r\n    id: 13,\r\n    fullName: 'Yoko Pottie',\r\n    company: 'Leenti PVT LTD',\r\n    role: 'subscriber',\r\n    username: 'ypottiec',\r\n    country: 'Philippines',\r\n    contact: '(907) 284-5083',\r\n    email: '<EMAIL>',\r\n    currentPlan: 'basic',\r\n    status: 'inactive',\r\n    avatar: '/images/avatars/7.png',\r\n    billing: 'Manual Paypal'\r\n  },\r\n  {\r\n    id: 14,\r\n    fullName: 'Maximilianus Krause',\r\n    company: 'Digitube PVT LTD',\r\n    role: 'author',\r\n    username: 'mkraused',\r\n    country: 'Democratic Republic of the Congo',\r\n    contact: '(167) 135-7392',\r\n    email: '<EMAIL>',\r\n    currentPlan: 'team',\r\n    status: 'active',\r\n    avatar: '/images/avatars/6.png',\r\n    billing: 'Auto Debit'\r\n  },\r\n  {\r\n    id: 15,\r\n    fullName: 'Zsazsa McCleverty',\r\n    company: 'Kaymbo PVT LTD',\r\n    role: 'maintainer',\r\n    username: 'zmcclevertye',\r\n    country: 'France',\r\n    contact: '(317) 409-6565',\r\n    email: '<EMAIL>',\r\n    currentPlan: 'enterprise',\r\n    status: 'active',\r\n    avatar: '/images/avatars/2.png',\r\n    billing: 'Auto Debit'\r\n  },\r\n  {\r\n    id: 16,\r\n    fullName: 'Bentlee Emblin',\r\n    company: 'Yambee PVT LTD',\r\n    role: 'author',\r\n    username: 'bemblinf',\r\n    country: 'Spain',\r\n    contact: '(590) 606-1056',\r\n    email: '<EMAIL>',\r\n    currentPlan: 'company',\r\n    status: 'active',\r\n    avatar: '/images/avatars/6.png',\r\n    billing: 'Manual Cash'\r\n  },\r\n  {\r\n    id: 17,\r\n    fullName: 'Brockie Myles',\r\n    company: 'Wikivu PVT LTD',\r\n    role: 'maintainer',\r\n    username: 'bmylesg',\r\n    country: 'Poland',\r\n    contact: '(553) 225-9905',\r\n    email: '<EMAIL>',\r\n    currentPlan: 'basic',\r\n    status: 'active',\r\n    avatar: '',\r\n    avatarColor: 'success',\r\n    billing: 'Auto Debit'\r\n  },\r\n  {\r\n    id: 18,\r\n    fullName: 'Bertha Biner',\r\n    company: 'Twinte PVT LTD',\r\n    role: 'editor',\r\n    username: 'bbinerh',\r\n    country: 'Yemen',\r\n    contact: '(901) 916-9287',\r\n    email: '<EMAIL>',\r\n    currentPlan: 'team',\r\n    status: 'active',\r\n    avatar: '/images/avatars/7.png',\r\n    billing: 'Manual Cash'\r\n  },\r\n  {\r\n    id: 19,\r\n    fullName: 'Travus Bruntjen',\r\n    company: 'Cogidoo PVT LTD',\r\n    role: 'admin',\r\n    username: 'tbruntjeni',\r\n    country: 'France',\r\n    contact: '(524) 586-6057',\r\n    email: '<EMAIL>',\r\n    currentPlan: 'enterprise',\r\n    status: 'active',\r\n    avatar: '',\r\n    avatarColor: 'primary',\r\n    billing: 'Manual Cash'\r\n  },\r\n  {\r\n    id: 20,\r\n    fullName: 'Wesley Burland',\r\n    company: 'Bubblemix PVT LTD',\r\n    role: 'editor',\r\n    username: 'wburlandj',\r\n    country: 'Honduras',\r\n    contact: '(569) 683-1292',\r\n    email: '<EMAIL>',\r\n    currentPlan: 'team',\r\n    status: 'inactive',\r\n    avatar: '/images/avatars/6.png',\r\n    billing: 'Manual paypal'\r\n  },\r\n  {\r\n    id: 21,\r\n    fullName: 'Selina Kyle',\r\n    company: 'Wayne Enterprises',\r\n    role: 'admin',\r\n    username: 'catwomen1940',\r\n    country: 'USA',\r\n    contact: '(829) 537-0057',\r\n    email: '<EMAIL>',\r\n    currentPlan: 'team',\r\n    status: 'active',\r\n    avatar: '/images/avatars/1.png',\r\n    billing: 'Manual paypal'\r\n  },\r\n  {\r\n    id: 22,\r\n    fullName: 'Jameson Lyster',\r\n    company: 'Quaxo PVT LTD',\r\n    role: 'editor',\r\n    username: 'jlysterl',\r\n    country: 'Ukraine',\r\n    contact: '(593) 624-0222',\r\n    email: '<EMAIL>',\r\n    currentPlan: 'company',\r\n    status: 'inactive',\r\n    avatar: '/images/avatars/8.png',\r\n    billing: 'Auto Debit'\r\n  },\r\n  {\r\n    id: 23,\r\n    fullName: 'Kare Skitterel',\r\n    company: 'Ainyx PVT LTD',\r\n    role: 'maintainer',\r\n    username: 'kskitterelm',\r\n    country: 'Poland',\r\n    contact: '(254) 845-4107',\r\n    email: '<EMAIL>',\r\n    currentPlan: 'basic',\r\n    status: 'pending',\r\n    avatar: '/images/avatars/3.png',\r\n    billing: 'Manual Cash'\r\n  },\r\n  {\r\n    id: 24,\r\n    fullName: 'Cleavland Hatherleigh',\r\n    company: 'Flipopia PVT LTD',\r\n    role: 'admin',\r\n    username: 'chatherleighn',\r\n    country: 'Brazil',\r\n    contact: '(700) 783-7498',\r\n    email: '<EMAIL>',\r\n    currentPlan: 'team',\r\n    status: 'pending',\r\n    avatar: '/images/avatars/2.png',\r\n    billing: 'Auto Debit'\r\n  },\r\n  {\r\n    id: 25,\r\n    fullName: 'Adeline Micco',\r\n    company: 'Topicware PVT LTD',\r\n    role: 'admin',\r\n    username: 'amiccoo',\r\n    country: 'France',\r\n    contact: '(227) 598-1841',\r\n    email: '<EMAIL>',\r\n    currentPlan: 'enterprise',\r\n    status: 'pending',\r\n    avatar: '',\r\n    avatarColor: 'error',\r\n    billing: 'Manual Cash'\r\n  },\r\n  {\r\n    id: 26,\r\n    fullName: 'Hugh Hasson',\r\n    company: 'Skinix PVT LTD',\r\n    role: 'admin',\r\n    username: 'hhassonp',\r\n    country: 'China',\r\n    contact: '(582) 516-1324',\r\n    email: '<EMAIL>',\r\n    currentPlan: 'basic',\r\n    status: 'inactive',\r\n    avatar: '/images/avatars/4.png',\r\n    billing: 'Manual Paypal'\r\n  },\r\n  {\r\n    id: 27,\r\n    fullName: 'Germain Jacombs',\r\n    company: 'Youopia PVT LTD',\r\n    role: 'editor',\r\n    username: 'gjacombsq',\r\n    country: 'Zambia',\r\n    contact: '(137) 467-5393',\r\n    email: '<EMAIL>',\r\n    currentPlan: 'enterprise',\r\n    status: 'active',\r\n    avatar: '/images/avatars/5.png',\r\n    billing: 'Auto Debit'\r\n  },\r\n  {\r\n    id: 28,\r\n    fullName: 'Bree Kilday',\r\n    company: 'Jetpulse PVT LTD',\r\n    role: 'maintainer',\r\n    username: 'bkildayr',\r\n    country: 'Portugal',\r\n    contact: '(412) 476-0854',\r\n    email: '<EMAIL>',\r\n    currentPlan: 'team',\r\n    status: 'active',\r\n    avatar: '',\r\n    avatarColor: 'warning',\r\n    billing: 'Manual Cash'\r\n  },\r\n  {\r\n    id: 29,\r\n    fullName: 'Candice Pinyon',\r\n    company: 'Kare PVT LTD',\r\n    role: 'maintainer',\r\n    username: 'cpinyons',\r\n    country: 'Sweden',\r\n    contact: '(170) 683-1520',\r\n    email: '<EMAIL>',\r\n    currentPlan: 'team',\r\n    status: 'active',\r\n    avatar: '/images/avatars/7.png',\r\n    billing: 'Auto Debit'\r\n  },\r\n  {\r\n    id: 30,\r\n    fullName: 'Isabel Mallindine',\r\n    company: 'Voomm PVT LTD',\r\n    role: 'subscriber',\r\n    username: 'imallindinet',\r\n    country: 'Slovenia',\r\n    contact: '(332) 803-1983',\r\n    email: '<EMAIL>',\r\n    currentPlan: 'team',\r\n    status: 'pending',\r\n    avatar: '',\r\n    avatarColor: 'info',\r\n    billing: 'Manual Cash'\r\n  },\r\n  {\r\n    id: 31,\r\n    fullName: 'Gwendolyn Meineken',\r\n    company: 'Oyondu PVT LTD',\r\n    role: 'admin',\r\n    username: 'gmeinekenu',\r\n    country: 'Moldova',\r\n    contact: '(551) 379-7460',\r\n    email: '<EMAIL>',\r\n    currentPlan: 'basic',\r\n    status: 'pending',\r\n    avatar: '/images/avatars/1.png',\r\n    billing: 'Auto Debit'\r\n  },\r\n  {\r\n    id: 32,\r\n    fullName: 'Rafaellle Snowball',\r\n    company: 'Fivespan PVT LTD',\r\n    role: 'editor',\r\n    username: 'rsnowballv',\r\n    country: 'Philippines',\r\n    contact: '(974) 829-0911',\r\n    email: '<EMAIL>',\r\n    currentPlan: 'basic',\r\n    status: 'pending',\r\n    avatar: '/images/avatars/5.png',\r\n    billing: 'Manual Cash'\r\n  },\r\n  {\r\n    id: 33,\r\n    fullName: 'Rochette Emer',\r\n    company: 'Thoughtworks PVT LTD',\r\n    role: 'admin',\r\n    username: 'remerw',\r\n    country: 'North Korea',\r\n    contact: '(841) 889-3339',\r\n    email: '<EMAIL>',\r\n    currentPlan: 'basic',\r\n    status: 'active',\r\n    avatar: '/images/avatars/8.png',\r\n    billing: 'Manual Cash'\r\n  },\r\n  {\r\n    id: 34,\r\n    fullName: 'Ophelie Fibbens',\r\n    company: 'Jaxbean PVT LTD',\r\n    role: 'subscriber',\r\n    username: 'ofibbensx',\r\n    country: 'Indonesia',\r\n    contact: '(764) 885-7351',\r\n    email: '<EMAIL>',\r\n    currentPlan: 'company',\r\n    status: 'active',\r\n    avatar: '/images/avatars/4.png',\r\n    billing: 'Manual Cash'\r\n  },\r\n  {\r\n    id: 35,\r\n    fullName: 'Stephen MacGilfoyle',\r\n    company: 'Browseblab PVT LTD',\r\n    role: 'maintainer',\r\n    username: 'smacgilfoyley',\r\n    country: 'Japan',\r\n    contact: '(350) 589-8520',\r\n    email: '<EMAIL>',\r\n    currentPlan: 'company',\r\n    status: 'pending',\r\n    avatar: '',\r\n    avatarColor: 'error',\r\n    billing: 'Auto Debit'\r\n  },\r\n  {\r\n    id: 36,\r\n    fullName: 'Bradan Rosebotham',\r\n    company: 'Agivu PVT LTD',\r\n    role: 'subscriber',\r\n    username: 'brosebothamz',\r\n    country: 'Belarus',\r\n    contact: '(882) 933-2180',\r\n    email: '<EMAIL>',\r\n    currentPlan: 'team',\r\n    status: 'inactive',\r\n    avatar: '',\r\n    avatarColor: 'success',\r\n    billing: 'Auto Debit'\r\n  },\r\n  {\r\n    id: 37,\r\n    fullName: 'Skip Hebblethwaite',\r\n    company: 'Katz PVT LTD',\r\n    role: 'admin',\r\n    username: 'shebblethwaite10',\r\n    country: 'Canada',\r\n    contact: '(610) 343-1024',\r\n    email: '<EMAIL>',\r\n    currentPlan: 'company',\r\n    status: 'inactive',\r\n    avatar: '/images/avatars/1.png',\r\n    billing: 'Manual Paypal'\r\n  },\r\n  {\r\n    id: 38,\r\n    fullName: 'Moritz Piccard',\r\n    company: 'Twitternation PVT LTD',\r\n    role: 'maintainer',\r\n    username: 'mpiccard11',\r\n    country: 'Croatia',\r\n    contact: '(365) 277-2986',\r\n    email: '<EMAIL>',\r\n    currentPlan: 'enterprise',\r\n    status: 'inactive',\r\n    avatar: '/images/avatars/1.png',\r\n    billing: 'Manual Paypal'\r\n  },\r\n  {\r\n    id: 39,\r\n    fullName: 'Tyne Widmore',\r\n    company: 'Yombu PVT LTD',\r\n    role: 'subscriber',\r\n    username: 'twidmore12',\r\n    country: 'Finland',\r\n    contact: '(531) 731-0928',\r\n    email: '<EMAIL>',\r\n    currentPlan: 'team',\r\n    status: 'pending',\r\n    avatar: '',\r\n    avatarColor: 'primary',\r\n    billing: 'Manual Paypal'\r\n  },\r\n  {\r\n    id: 40,\r\n    fullName: 'Florenza Desporte',\r\n    company: 'Kamba PVT LTD',\r\n    role: 'author',\r\n    username: 'fdesporte13',\r\n    country: 'Ukraine',\r\n    contact: '(312) 104-2638',\r\n    email: '<EMAIL>',\r\n    currentPlan: 'company',\r\n    status: 'active',\r\n    avatar: '/images/avatars/6.png',\r\n    billing: 'Auto Debit'\r\n  },\r\n  {\r\n    id: 41,\r\n    fullName: 'Edwina Baldetti',\r\n    company: 'Dazzlesphere PVT LTD',\r\n    role: 'maintainer',\r\n    username: 'ebaldetti14',\r\n    country: 'Haiti',\r\n    contact: '(315) 329-3578',\r\n    email: '<EMAIL>',\r\n    currentPlan: 'team',\r\n    status: 'pending',\r\n    avatar: '',\r\n    avatarColor: 'info',\r\n    billing: 'Auto Debit'\r\n  },\r\n  {\r\n    id: 42,\r\n    fullName: 'Benedetto Rossiter',\r\n    company: 'Mybuzz PVT LTD',\r\n    role: 'editor',\r\n    username: 'brossiter15',\r\n    country: 'Indonesia',\r\n    contact: '(323) 175-6741',\r\n    email: '<EMAIL>',\r\n    currentPlan: 'team',\r\n    status: 'inactive',\r\n    avatar: '',\r\n    avatarColor: 'warning',\r\n    billing: 'Auto Debit'\r\n  },\r\n  {\r\n    id: 43,\r\n    fullName: 'Micaela McNirlan',\r\n    company: 'Tambee PVT LTD',\r\n    role: 'admin',\r\n    username: 'mmcnirlan16',\r\n    country: 'Indonesia',\r\n    contact: '(242) 952-0916',\r\n    email: '<EMAIL>',\r\n    currentPlan: 'basic',\r\n    status: 'inactive',\r\n    avatar: '',\r\n    avatarColor: 'error',\r\n    billing: 'Manual Cash'\r\n  },\r\n  {\r\n    id: 44,\r\n    fullName: 'Vladamir Koschek',\r\n    company: 'Centimia PVT LTD',\r\n    role: 'author',\r\n    username: 'vkoschek17',\r\n    country: 'Guatemala',\r\n    contact: '(531) 758-8335',\r\n    email: '<EMAIL>',\r\n    currentPlan: 'team',\r\n    status: 'active',\r\n    avatar: '',\r\n    avatarColor: 'success',\r\n    billing: 'Manual Cash'\r\n  },\r\n  {\r\n    id: 45,\r\n    fullName: 'Corrie Perot',\r\n    company: 'Flipopia PVT LTD',\r\n    role: 'subscriber',\r\n    username: 'cperot18',\r\n    country: 'China',\r\n    contact: '(659) 385-6808',\r\n    email: '<EMAIL>',\r\n    currentPlan: 'team',\r\n    status: 'pending',\r\n    avatar: '/images/avatars/3.png',\r\n    billing: 'Manual Cash'\r\n  },\r\n  {\r\n    id: 46,\r\n    fullName: 'Saunder Offner',\r\n    company: 'Skalith PVT LTD',\r\n    role: 'maintainer',\r\n    username: 'soffner19',\r\n    country: 'Poland',\r\n    contact: '(200) 586-2264',\r\n    email: '<EMAIL>',\r\n    currentPlan: 'enterprise',\r\n    status: 'pending',\r\n    avatar: '',\r\n    avatarColor: 'primary',\r\n    billing: 'Manual Paypal'\r\n  },\r\n  {\r\n    id: 47,\r\n    fullName: 'Karena Courtliff',\r\n    company: 'Feedfire PVT LTD',\r\n    role: 'admin',\r\n    username: 'kcourtliff1a',\r\n    country: 'China',\r\n    contact: '(478) 199-0020',\r\n    email: '<EMAIL>',\r\n    currentPlan: 'basic',\r\n    status: 'active',\r\n    avatar: '/images/avatars/1.png',\r\n    billing: 'Auto Debit'\r\n  },\r\n  {\r\n    id: 48,\r\n    fullName: 'Onfre Wind',\r\n    company: 'Thoughtmix PVT LTD',\r\n    role: 'admin',\r\n    username: 'owind1b',\r\n    country: 'Ukraine',\r\n    contact: '(344) 262-7270',\r\n    email: '<EMAIL>',\r\n    currentPlan: 'basic',\r\n    status: 'pending',\r\n    avatar: '',\r\n    avatarColor: 'error',\r\n    billing: 'Auto Debit'\r\n  },\r\n  {\r\n    id: 49,\r\n    fullName: 'Paulie Durber',\r\n    company: 'Babbleblab PVT LTD',\r\n    role: 'subscriber',\r\n    username: 'pdurber1c',\r\n    country: 'Sweden',\r\n    contact: '(694) 676-1275',\r\n    email: '<EMAIL>',\r\n    currentPlan: 'team',\r\n    status: 'inactive',\r\n    avatar: '',\r\n    avatarColor: 'warning',\r\n    billing: 'Manual Cash'\r\n  },\r\n  {\r\n    id: 50,\r\n    fullName: 'Beverlie Krabbe',\r\n    company: 'Kaymbo PVT LTD',\r\n    role: 'editor',\r\n    username: 'bkrabbe1d',\r\n    country: 'China',\r\n    contact: '(397) 294-5153',\r\n    email: '<EMAIL>',\r\n    currentPlan: 'company',\r\n    status: 'active',\r\n    avatar: '/images/avatars/2.png',\r\n    billing: 'Auto Debit'\r\n  }\r\n]\r\n"], "names": [], "mappings": ";;;AAAO,MAAM,KAAK;IAChB;QACE,IAAI;QACJ,UAAU;QACV,SAAS;QACT,MAAM;QACN,UAAU;QACV,SAAS;QACT,SAAS;QACT,OAAO;QACP,aAAa;QACb,QAAQ;QACR,QAAQ;QACR,aAAa;QACb,SAAS;IACX;IACA;QACE,IAAI;QACJ,UAAU;QACV,SAAS;QACT,MAAM;QACN,UAAU;QACV,SAAS;QACT,SAAS;QACT,OAAO;QACP,aAAa;QACb,QAAQ;QACR,QAAQ;QACR,SAAS;IACX;IACA;QACE,IAAI;QACJ,UAAU;QACV,SAAS;QACT,MAAM;QACN,UAAU;QACV,SAAS;QACT,SAAS;QACT,OAAO;QACP,aAAa;QACb,QAAQ;QACR,QAAQ;QACR,SAAS;IACX;IACA;QACE,IAAI;QACJ,UAAU;QACV,SAAS;QACT,MAAM;QACN,UAAU;QACV,SAAS;QACT,SAAS;QACT,OAAO;QACP,aAAa;QACb,QAAQ;QACR,QAAQ;QACR,SAAS;IACX;IACA;QACE,IAAI;QACJ,UAAU;QACV,SAAS;QACT,MAAM;QACN,UAAU;QACV,SAAS;QACT,SAAS;QACT,OAAO;QACP,aAAa;QACb,QAAQ;QACR,QAAQ;QACR,SAAS;IACX;IACA;QACE,IAAI;QACJ,UAAU;QACV,SAAS;QACT,MAAM;QACN,UAAU;QACV,SAAS;QACT,SAAS;QACT,OAAO;QACP,aAAa;QACb,QAAQ;QACR,QAAQ;QACR,aAAa;QACb,SAAS;IACX;IACA;QACE,IAAI;QACJ,UAAU;QACV,SAAS;QACT,MAAM;QACN,UAAU;QACV,SAAS;QACT,SAAS;QACT,OAAO;QACP,aAAa;QACb,QAAQ;QACR,QAAQ;QACR,aAAa;QACb,SAAS;IACX;IACA;QACE,IAAI;QACJ,UAAU;QACV,SAAS;QACT,MAAM;QACN,UAAU;QACV,SAAS;QACT,SAAS;QACT,OAAO;QACP,aAAa;QACb,QAAQ;QACR,QAAQ;QACR,SAAS;IACX;IACA;QACE,IAAI;QACJ,UAAU;QACV,SAAS;QACT,MAAM;QACN,UAAU;QACV,SAAS;QACT,SAAS;QACT,OAAO;QACP,aAAa;QACb,QAAQ;QACR,QAAQ;QACR,SAAS;IACX;IACA;QACE,IAAI;QACJ,UAAU;QACV,SAAS;QACT,MAAM;QACN,UAAU;QACV,SAAS;QACT,SAAS;QACT,OAAO;QACP,aAAa;QACb,QAAQ;QACR,QAAQ;QACR,SAAS;IACX;IACA;QACE,IAAI;QACJ,UAAU;QACV,SAAS;QACT,MAAM;QACN,UAAU;QACV,SAAS;QACT,SAAS;QACT,OAAO;QACP,aAAa;QACb,QAAQ;QACR,QAAQ;QACR,SAAS;IACX;IACA;QACE,IAAI;QACJ,UAAU;QACV,SAAS;QACT,MAAM;QACN,UAAU;QACV,SAAS;QACT,SAAS;QACT,OAAO;QACP,aAAa;QACb,QAAQ;QACR,QAAQ;QACR,SAAS;IACX;IACA;QACE,IAAI;QACJ,UAAU;QACV,SAAS;QACT,MAAM;QACN,UAAU;QACV,SAAS;QACT,SAAS;QACT,OAAO;QACP,aAAa;QACb,QAAQ;QACR,QAAQ;QACR,SAAS;IACX;IACA;QACE,IAAI;QACJ,UAAU;QACV,SAAS;QACT,MAAM;QACN,UAAU;QACV,SAAS;QACT,SAAS;QACT,OAAO;QACP,aAAa;QACb,QAAQ;QACR,QAAQ;QACR,SAAS;IACX;IACA;QACE,IAAI;QACJ,UAAU;QACV,SAAS;QACT,MAAM;QACN,UAAU;QACV,SAAS;QACT,SAAS;QACT,OAAO;QACP,aAAa;QACb,QAAQ;QACR,QAAQ;QACR,SAAS;IACX;IACA;QACE,IAAI;QACJ,UAAU;QACV,SAAS;QACT,MAAM;QACN,UAAU;QACV,SAAS;QACT,SAAS;QACT,OAAO;QACP,aAAa;QACb,QAAQ;QACR,QAAQ;QACR,SAAS;IACX;IACA;QACE,IAAI;QACJ,UAAU;QACV,SAAS;QACT,MAAM;QACN,UAAU;QACV,SAAS;QACT,SAAS;QACT,OAAO;QACP,aAAa;QACb,QAAQ;QACR,QAAQ;QACR,aAAa;QACb,SAAS;IACX;IACA;QACE,IAAI;QACJ,UAAU;QACV,SAAS;QACT,MAAM;QACN,UAAU;QACV,SAAS;QACT,SAAS;QACT,OAAO;QACP,aAAa;QACb,QAAQ;QACR,QAAQ;QACR,SAAS;IACX;IACA;QACE,IAAI;QACJ,UAAU;QACV,SAAS;QACT,MAAM;QACN,UAAU;QACV,SAAS;QACT,SAAS;QACT,OAAO;QACP,aAAa;QACb,QAAQ;QACR,QAAQ;QACR,aAAa;QACb,SAAS;IACX;IACA;QACE,IAAI;QACJ,UAAU;QACV,SAAS;QACT,MAAM;QACN,UAAU;QACV,SAAS;QACT,SAAS;QACT,OAAO;QACP,aAAa;QACb,QAAQ;QACR,QAAQ;QACR,SAAS;IACX;IACA;QACE,IAAI;QACJ,UAAU;QACV,SAAS;QACT,MAAM;QACN,UAAU;QACV,SAAS;QACT,SAAS;QACT,OAAO;QACP,aAAa;QACb,QAAQ;QACR,QAAQ;QACR,SAAS;IACX;IACA;QACE,IAAI;QACJ,UAAU;QACV,SAAS;QACT,MAAM;QACN,UAAU;QACV,SAAS;QACT,SAAS;QACT,OAAO;QACP,aAAa;QACb,QAAQ;QACR,QAAQ;QACR,SAAS;IACX;IACA;QACE,IAAI;QACJ,UAAU;QACV,SAAS;QACT,MAAM;QACN,UAAU;QACV,SAAS;QACT,SAAS;QACT,OAAO;QACP,aAAa;QACb,QAAQ;QACR,QAAQ;QACR,SAAS;IACX;IACA;QACE,IAAI;QACJ,UAAU;QACV,SAAS;QACT,MAAM;QACN,UAAU;QACV,SAAS;QACT,SAAS;QACT,OAAO;QACP,aAAa;QACb,QAAQ;QACR,QAAQ;QACR,SAAS;IACX;IACA;QACE,IAAI;QACJ,UAAU;QACV,SAAS;QACT,MAAM;QACN,UAAU;QACV,SAAS;QACT,SAAS;QACT,OAAO;QACP,aAAa;QACb,QAAQ;QACR,QAAQ;QACR,aAAa;QACb,SAAS;IACX;IACA;QACE,IAAI;QACJ,UAAU;QACV,SAAS;QACT,MAAM;QACN,UAAU;QACV,SAAS;QACT,SAAS;QACT,OAAO;QACP,aAAa;QACb,QAAQ;QACR,QAAQ;QACR,SAAS;IACX;IACA;QACE,IAAI;QACJ,UAAU;QACV,SAAS;QACT,MAAM;QACN,UAAU;QACV,SAAS;QACT,SAAS;QACT,OAAO;QACP,aAAa;QACb,QAAQ;QACR,QAAQ;QACR,SAAS;IACX;IACA;QACE,IAAI;QACJ,UAAU;QACV,SAAS;QACT,MAAM;QACN,UAAU;QACV,SAAS;QACT,SAAS;QACT,OAAO;QACP,aAAa;QACb,QAAQ;QACR,QAAQ;QACR,aAAa;QACb,SAAS;IACX;IACA;QACE,IAAI;QACJ,UAAU;QACV,SAAS;QACT,MAAM;QACN,UAAU;QACV,SAAS;QACT,SAAS;QACT,OAAO;QACP,aAAa;QACb,QAAQ;QACR,QAAQ;QACR,SAAS;IACX;IACA;QACE,IAAI;QACJ,UAAU;QACV,SAAS;QACT,MAAM;QACN,UAAU;QACV,SAAS;QACT,SAAS;QACT,OAAO;QACP,aAAa;QACb,QAAQ;QACR,QAAQ;QACR,aAAa;QACb,SAAS;IACX;IACA;QACE,IAAI;QACJ,UAAU;QACV,SAAS;QACT,MAAM;QACN,UAAU;QACV,SAAS;QACT,SAAS;QACT,OAAO;QACP,aAAa;QACb,QAAQ;QACR,QAAQ;QACR,SAAS;IACX;IACA;QACE,IAAI;QACJ,UAAU;QACV,SAAS;QACT,MAAM;QACN,UAAU;QACV,SAAS;QACT,SAAS;QACT,OAAO;QACP,aAAa;QACb,QAAQ;QACR,QAAQ;QACR,SAAS;IACX;IACA;QACE,IAAI;QACJ,UAAU;QACV,SAAS;QACT,MAAM;QACN,UAAU;QACV,SAAS;QACT,SAAS;QACT,OAAO;QACP,aAAa;QACb,QAAQ;QACR,QAAQ;QACR,SAAS;IACX;IACA;QACE,IAAI;QACJ,UAAU;QACV,SAAS;QACT,MAAM;QACN,UAAU;QACV,SAAS;QACT,SAAS;QACT,OAAO;QACP,aAAa;QACb,QAAQ;QACR,QAAQ;QACR,SAAS;IACX;IACA;QACE,IAAI;QACJ,UAAU;QACV,SAAS;QACT,MAAM;QACN,UAAU;QACV,SAAS;QACT,SAAS;QACT,OAAO;QACP,aAAa;QACb,QAAQ;QACR,QAAQ;QACR,aAAa;QACb,SAAS;IACX;IACA;QACE,IAAI;QACJ,UAAU;QACV,SAAS;QACT,MAAM;QACN,UAAU;QACV,SAAS;QACT,SAAS;QACT,OAAO;QACP,aAAa;QACb,QAAQ;QACR,QAAQ;QACR,aAAa;QACb,SAAS;IACX;IACA;QACE,IAAI;QACJ,UAAU;QACV,SAAS;QACT,MAAM;QACN,UAAU;QACV,SAAS;QACT,SAAS;QACT,OAAO;QACP,aAAa;QACb,QAAQ;QACR,QAAQ;QACR,SAAS;IACX;IACA;QACE,IAAI;QACJ,UAAU;QACV,SAAS;QACT,MAAM;QACN,UAAU;QACV,SAAS;QACT,SAAS;QACT,OAAO;QACP,aAAa;QACb,QAAQ;QACR,QAAQ;QACR,SAAS;IACX;IACA;QACE,IAAI;QACJ,UAAU;QACV,SAAS;QACT,MAAM;QACN,UAAU;QACV,SAAS;QACT,SAAS;QACT,OAAO;QACP,aAAa;QACb,QAAQ;QACR,QAAQ;QACR,aAAa;QACb,SAAS;IACX;IACA;QACE,IAAI;QACJ,UAAU;QACV,SAAS;QACT,MAAM;QACN,UAAU;QACV,SAAS;QACT,SAAS;QACT,OAAO;QACP,aAAa;QACb,QAAQ;QACR,QAAQ;QACR,SAAS;IACX;IACA;QACE,IAAI;QACJ,UAAU;QACV,SAAS;QACT,MAAM;QACN,UAAU;QACV,SAAS;QACT,SAAS;QACT,OAAO;QACP,aAAa;QACb,QAAQ;QACR,QAAQ;QACR,aAAa;QACb,SAAS;IACX;IACA;QACE,IAAI;QACJ,UAAU;QACV,SAAS;QACT,MAAM;QACN,UAAU;QACV,SAAS;QACT,SAAS;QACT,OAAO;QACP,aAAa;QACb,QAAQ;QACR,QAAQ;QACR,aAAa;QACb,SAAS;IACX;IACA;QACE,IAAI;QACJ,UAAU;QACV,SAAS;QACT,MAAM;QACN,UAAU;QACV,SAAS;QACT,SAAS;QACT,OAAO;QACP,aAAa;QACb,QAAQ;QACR,QAAQ;QACR,aAAa;QACb,SAAS;IACX;IACA;QACE,IAAI;QACJ,UAAU;QACV,SAAS;QACT,MAAM;QACN,UAAU;QACV,SAAS;QACT,SAAS;QACT,OAAO;QACP,aAAa;QACb,QAAQ;QACR,QAAQ;QACR,aAAa;QACb,SAAS;IACX;IACA;QACE,IAAI;QACJ,UAAU;QACV,SAAS;QACT,MAAM;QACN,UAAU;QACV,SAAS;QACT,SAAS;QACT,OAAO;QACP,aAAa;QACb,QAAQ;QACR,QAAQ;QACR,SAAS;IACX;IACA;QACE,IAAI;QACJ,UAAU;QACV,SAAS;QACT,MAAM;QACN,UAAU;QACV,SAAS;QACT,SAAS;QACT,OAAO;QACP,aAAa;QACb,QAAQ;QACR,QAAQ;QACR,aAAa;QACb,SAAS;IACX;IACA;QACE,IAAI;QACJ,UAAU;QACV,SAAS;QACT,MAAM;QACN,UAAU;QACV,SAAS;QACT,SAAS;QACT,OAAO;QACP,aAAa;QACb,QAAQ;QACR,QAAQ;QACR,SAAS;IACX;IACA;QACE,IAAI;QACJ,UAAU;QACV,SAAS;QACT,MAAM;QACN,UAAU;QACV,SAAS;QACT,SAAS;QACT,OAAO;QACP,aAAa;QACb,QAAQ;QACR,QAAQ;QACR,aAAa;QACb,SAAS;IACX;IACA;QACE,IAAI;QACJ,UAAU;QACV,SAAS;QACT,MAAM;QACN,UAAU;QACV,SAAS;QACT,SAAS;QACT,OAAO;QACP,aAAa;QACb,QAAQ;QACR,QAAQ;QACR,aAAa;QACb,SAAS;IACX;IACA;QACE,IAAI;QACJ,UAAU;QACV,SAAS;QACT,MAAM;QACN,UAAU;QACV,SAAS;QACT,SAAS;QACT,OAAO;QACP,aAAa;QACb,QAAQ;QACR,QAAQ;QACR,SAAS;IACX;CACD", "debugId": null}}, {"offset": {"line": 5879, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/fake-db/apps/permissions.js"], "sourcesContent": ["export const db = [\r\n  {\r\n    id: 1,\r\n    name: 'Management',\r\n    assignedTo: 'administrator',\r\n    createdDate: '14 Apr 2021, 8:43 PM'\r\n  },\r\n  {\r\n    id: 2,\r\n    assignedTo: 'administrator',\r\n    name: 'Manage Billing & Roles',\r\n    createdDate: '16 Sep 2021, 5:20 PM'\r\n  },\r\n  {\r\n    id: 3,\r\n    name: 'Add & Remove Users',\r\n    createdDate: '14 Oct 2021, 10:20 AM',\r\n    assignedTo: ['administrator', 'manager']\r\n  },\r\n  {\r\n    id: 4,\r\n    name: 'Project Planning',\r\n    createdDate: '14 Oct 2021, 10:20 AM',\r\n    assignedTo: ['administrator', 'users', 'support']\r\n  },\r\n  {\r\n    id: 5,\r\n    name: 'Manage Email Sequences',\r\n    createdDate: '23 Aug 2021, 2:00 PM',\r\n    assignedTo: ['administrator', 'users', 'support']\r\n  },\r\n  {\r\n    id: 6,\r\n    name: 'Client Communication',\r\n    createdDate: '15 Apr 2021, 11:30 AM',\r\n    assignedTo: ['administrator', 'manager']\r\n  },\r\n  {\r\n    id: 7,\r\n    name: 'Only View',\r\n    createdDate: '04 Dec 2021, 8:15 PM',\r\n    assignedTo: ['administrator', 'restricted-user']\r\n  },\r\n  {\r\n    id: 8,\r\n    name: 'Financial Management',\r\n    createdDate: '25 Feb 2021, 10:30 AM',\r\n    assignedTo: ['administrator', 'manager']\r\n  },\r\n  {\r\n    id: 9,\r\n    name: 'Manage Others’ Tasks',\r\n    createdDate: '04 Nov 2021, 11:45 AM',\r\n    assignedTo: ['administrator', 'support']\r\n  }\r\n]\r\n"], "names": [], "mappings": ";;;AAAO,MAAM,KAAK;IAChB;QACE,IAAI;QACJ,MAAM;QACN,YAAY;QACZ,aAAa;IACf;IACA;QACE,IAAI;QACJ,YAAY;QACZ,MAAM;QACN,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,YAAY;YAAC;YAAiB;SAAU;IAC1C;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,YAAY;YAAC;YAAiB;YAAS;SAAU;IACnD;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,YAAY;YAAC;YAAiB;YAAS;SAAU;IACnD;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,YAAY;YAAC;YAAiB;SAAU;IAC1C;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,YAAY;YAAC;YAAiB;SAAkB;IAClD;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,YAAY;YAAC;YAAiB;SAAU;IAC1C;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,YAAY;YAAC;YAAiB;SAAU;IAC1C;CACD", "debugId": null}}, {"offset": {"line": 5967, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/fake-db/pages/userProfile.js"], "sourcesContent": ["export const db = {\r\n  users: {\r\n    profile: {\r\n      // Dynamic data will come from user session\r\n      // Static data removed as requested\r\n      overview: [\r\n        { property: 'Task Compiled', value: '13.5k', icon: 'tabler-check' },\r\n        { property: 'Connections', value: '897', icon: 'tabler-users' },\r\n        { property: 'Projects Compiled', value: '146', icon: 'tabler-layout-grid' }\r\n      ],\r\n      connections: [\r\n        {\r\n          isFriend: true,\r\n          connections: '45',\r\n          name: '<PERSON>',\r\n          avatar: '/images/avatars/2.png'\r\n        },\r\n        {\r\n          isFriend: false,\r\n          connections: '1.32k',\r\n          name: '<PERSON>',\r\n          avatar: '/images/avatars/3.png'\r\n        },\r\n        {\r\n          isFriend: false,\r\n          connections: '125',\r\n          name: '<PERSON>',\r\n          avatar: '/images/avatars/4.png'\r\n        },\r\n        {\r\n          isFriend: true,\r\n          connections: '456',\r\n          name: '<PERSON>',\r\n          avatar: '/images/avatars/5.png'\r\n        },\r\n        {\r\n          isFriend: true,\r\n          connections: '1.2k',\r\n          name: '<PERSON><PERSON><PERSON>',\r\n          avatar: '/images/avatars/8.png'\r\n        }\r\n      ],\r\n      teamsTech: [\r\n        {\r\n          members: 72,\r\n          ChipColor: 'error',\r\n          chipText: 'Developer',\r\n          title: 'React Developers',\r\n          avatar: '/images/logos/react-bg.png'\r\n        },\r\n        {\r\n          members: 122,\r\n          chipText: 'Support',\r\n          ChipColor: 'primary',\r\n          title: 'Support Team',\r\n          avatar: '/images/icons/support-bg.png'\r\n        },\r\n        {\r\n          members: 7,\r\n          ChipColor: 'info',\r\n          chipText: 'Designer',\r\n          title: 'UI Designer',\r\n          avatar: '/images/logos/figma-bg.png'\r\n        },\r\n        {\r\n          members: 289,\r\n          ChipColor: 'error',\r\n          chipText: 'Developer',\r\n          title: 'Vue.js Developers',\r\n          avatar: '/images/logos/vue-bg.png'\r\n        },\r\n        {\r\n          members: 24,\r\n          chipText: 'Marketing',\r\n          ChipColor: 'secondary',\r\n          title: 'Digital Marketing',\r\n          avatar: '/images/logos/twitter-bg.png'\r\n        }\r\n      ],\r\n      projectTable: [\r\n        {\r\n          id: 1,\r\n          title: 'BGC eCommerce App',\r\n          subtitle: 'React Project',\r\n          leader: 'Eileen',\r\n          avatar: '/images/logos/react-bg.png',\r\n          avatarGroup: [\r\n            '/images/avatars/1.png',\r\n            '/images/avatars/2.png',\r\n            '/images/avatars/3.png',\r\n            '/images/avatars/4.png'\r\n          ],\r\n          status: 78\r\n        },\r\n        {\r\n          id: 2,\r\n          leader: 'Owen',\r\n          title: 'Falcon Logo Design',\r\n          subtitle: 'Figma Project',\r\n          avatar: '/images/logos/figma-bg.png',\r\n          avatarGroup: ['/images/avatars/5.png', '/images/avatars/6.png'],\r\n          status: 18\r\n        },\r\n        {\r\n          id: 3,\r\n          title: 'Dashboard Design',\r\n          subtitle: 'VueJs Project',\r\n          leader: 'Keith',\r\n          avatar: '/images/logos/vue-bg.png',\r\n          avatarGroup: [\r\n            '/images/avatars/7.png',\r\n            '/images/avatars/8.png',\r\n            '/images/avatars/1.png',\r\n            '/images/avatars/2.png'\r\n          ],\r\n          status: 62\r\n        },\r\n        {\r\n          id: 4,\r\n          title: 'Foodista Mobile App',\r\n          subtitle: 'Xamarin Project',\r\n          leader: 'Merline',\r\n          avatar: '/images/icons/mobile-bg.png',\r\n          avatarGroup: [\r\n            '/images/avatars/3.png',\r\n            '/images/avatars/4.png',\r\n            '/images/avatars/5.png',\r\n            '/images/avatars/6.png'\r\n          ],\r\n          status: 8\r\n        },\r\n        {\r\n          id: 5,\r\n          leader: 'Harmonia',\r\n          title: 'Dojo React Project',\r\n          subtitle: 'Python Project',\r\n          avatar: '/images/logos/python-bg.png',\r\n          avatarGroup: ['/images/avatars/7.png', '/images/avatars/8.png', '/images/avatars/1.png'],\r\n          status: 36\r\n        },\r\n        {\r\n          id: 6,\r\n          leader: 'Allyson',\r\n          title: 'Blockchain Website',\r\n          subtitle: 'Sketch Project',\r\n          avatar: '/images/logos/sketch-bg.png',\r\n          avatarGroup: [\r\n            '/images/avatars/2.png',\r\n            '/images/avatars/3.png',\r\n            '/images/avatars/4.png',\r\n            '/images/avatars/5.png'\r\n          ],\r\n          status: 92\r\n        },\r\n        {\r\n          id: 7,\r\n          title: 'Hoffman Website',\r\n          subtitle: 'HTML Project',\r\n          leader: 'Georgie',\r\n          avatar: '/images/logos/html-bg.png',\r\n          avatarGroup: [\r\n            '/images/avatars/6.png',\r\n            '/images/avatars/7.png',\r\n            '/images/avatars/8.png',\r\n            '/images/avatars/1.png'\r\n          ],\r\n          status: 88\r\n        },\r\n        {\r\n          id: 8,\r\n          title: 'eCommerce Website',\r\n          subtitle: 'React Project',\r\n          leader: 'Eileen',\r\n          avatar: '/images/logos/react-bg.png',\r\n          avatarGroup: [\r\n            '/images/avatars/1.png',\r\n            '/images/avatars/2.png',\r\n            '/images/avatars/3.png',\r\n            '/images/avatars/4.png'\r\n          ],\r\n          status: 78\r\n        },\r\n        {\r\n          id: 9,\r\n          leader: 'Owen',\r\n          title: 'Retro Logo Design',\r\n          subtitle: 'Figma Project',\r\n          avatar: '/images/logos/figma-bg.png',\r\n          avatarGroup: ['/images/avatars/5.png', '/images/avatars/6.png'],\r\n          status: 18\r\n        },\r\n        {\r\n          id: 10,\r\n          title: 'Admin Dashboard',\r\n          subtitle: 'VueJs Project',\r\n          leader: 'Keith',\r\n          avatar: '/images/logos/vue-bg.png',\r\n          avatarGroup: [\r\n            '/images/avatars/7.png',\r\n            '/images/avatars/8.png',\r\n            '/images/avatars/1.png',\r\n            '/images/avatars/2.png'\r\n          ],\r\n          status: 62\r\n        }\r\n      ]\r\n    },\r\n    teams: [\r\n      {\r\n        extraMembers: 9,\r\n        title: 'React Developers',\r\n        avatar: '/images/logos/react-bg.png',\r\n        avatarGroup: [\r\n          { avatar: '/images/avatars/1.png', name: 'Vinnie Mostowy' },\r\n          { avatar: '/images/avatars/2.png', name: 'Allen Rieske' },\r\n          { avatar: '/images/avatars/3.png', name: 'Julee Rossignol' }\r\n        ],\r\n        description:\r\n          'We don’t make assumptions about the rest of your technology stack, so you can develop new features.',\r\n        chips: [\r\n          {\r\n            title: 'React',\r\n            color: 'primary'\r\n          },\r\n          {\r\n            title: 'MUI',\r\n            color: 'info'\r\n          }\r\n        ]\r\n      },\r\n      {\r\n        extraMembers: 4,\r\n        title: 'Vue.js Dev Team',\r\n        avatar: '/images/logos/vue-bg.png',\r\n        avatarGroup: [\r\n          { avatar: '/images/avatars/5.png', name: \"Kaith D'souza\" },\r\n          { avatar: '/images/avatars/6.png', name: 'John Doe' },\r\n          { avatar: '/images/avatars/7.png', name: 'Alan Walker' }\r\n        ],\r\n        description:\r\n          'The development of Vue and its ecosystem is guided by an international team, some of whom have chosen.',\r\n        chips: [\r\n          {\r\n            title: 'Vuejs',\r\n            color: 'success'\r\n          },\r\n          {\r\n            color: 'error',\r\n            title: 'Developer'\r\n          }\r\n        ]\r\n      },\r\n      {\r\n        title: 'Creative Designers',\r\n        avatar: '/images/logos/xd-bg.png',\r\n        avatarGroup: [\r\n          { avatar: '/images/avatars/1.png', name: 'Jimmy Ressula' },\r\n          { avatar: '/images/avatars/2.png', name: 'Kristi Lawker' },\r\n          { avatar: '/images/avatars/3.png', name: 'Danny Paul' }\r\n        ],\r\n        description:\r\n          'A design or product team is more than just the people on it. A team includes the people, the roles they play.',\r\n        chips: [\r\n          {\r\n            title: 'Sketch',\r\n            color: 'warning'\r\n          },\r\n          {\r\n            title: 'XD',\r\n            color: 'error'\r\n          }\r\n        ]\r\n      },\r\n      {\r\n        title: 'Support Team',\r\n        avatar: '/images/icons/support-bg.png',\r\n        avatarGroup: [\r\n          { avatar: '/images/avatars/5.png', name: 'Andrew Tye' },\r\n          { avatar: '/images/avatars/6.png', name: 'Rishi Swaat' },\r\n          { avatar: '/images/avatars/7.png', name: 'Rossie Kim' }\r\n        ],\r\n        description:\r\n          'Support your team. Your customer support team is fielding the good, the bad, and the ugly on daily basis.',\r\n        chips: [\r\n          {\r\n            title: 'Zendesk',\r\n            color: 'info'\r\n          }\r\n        ]\r\n      },\r\n      {\r\n        extraMembers: 7,\r\n        title: 'Digital Marketing',\r\n        avatar: '/images/icons/social-bg.png',\r\n        avatarGroup: [\r\n          { avatar: '/images/avatars/1.png', name: 'Kim Merchent' },\r\n          { avatar: '/images/avatars/2.png', name: \"Sam D'souza\" },\r\n          { avatar: '/images/avatars/3.png', name: 'Nurvi Karlos' }\r\n        ],\r\n        description:\r\n          'Digital marketing refers to advertising delivered through digital channels such as search engines, websites…',\r\n        chips: [\r\n          {\r\n            title: 'Twitter',\r\n            color: 'primary'\r\n          },\r\n          {\r\n            color: 'success',\r\n            title: 'Email'\r\n          }\r\n        ]\r\n      },\r\n      {\r\n        extraMembers: 2,\r\n        title: 'Event',\r\n        avatar: '/images/logos/event-bg.png',\r\n        avatarGroup: [\r\n          { avatar: '/images/avatars/5.png', name: 'Vinnie Mostowy' },\r\n          { avatar: '/images/avatars/6.png', name: 'Allen Rieske' },\r\n          { avatar: '/images/avatars/7.png', name: 'Julee Rossignol' }\r\n        ],\r\n        description:\r\n          'Event is defined as a particular contest which is part of a program of contests. An example of an event is the long…',\r\n        chips: [\r\n          {\r\n            title: 'Hubilo',\r\n            color: 'success'\r\n          }\r\n        ]\r\n      },\r\n      {\r\n        title: 'Figma Resources',\r\n        avatar: '/images/logos/figma-bg.png',\r\n        avatarGroup: [\r\n          { avatar: '/images/avatars/1.png', name: 'Andrew Mostowy' },\r\n          { avatar: '/images/avatars/2.png', name: 'Micky Ressula' },\r\n          { avatar: '/images/avatars/3.png', name: 'Michel Pal' }\r\n        ],\r\n        description:\r\n          'Explore, install, use, and remix thousands of plugins and files published to the Figma Community by designers.',\r\n        chips: [\r\n          {\r\n            title: 'UI/UX',\r\n            color: 'success'\r\n          },\r\n          {\r\n            title: 'Figma',\r\n            color: 'warning'\r\n          }\r\n        ]\r\n      },\r\n      {\r\n        extraMembers: 8,\r\n        title: 'Only Beginners',\r\n        avatar: '/images/logos/html-bg.png',\r\n        avatarGroup: [\r\n          { avatar: '/images/avatars/5.png', name: 'Kim Karlos' },\r\n          { avatar: '/images/avatars/6.png', name: 'Katy Turner' },\r\n          { avatar: '/images/avatars/7.png', name: 'Peter Adward' }\r\n        ],\r\n        description:\r\n          'Learn the basics of how websites work, front-end vs back-end. Learn basic HTML, CSS, and JavaScript.',\r\n        chips: [\r\n          {\r\n            title: 'CSS',\r\n            color: 'info'\r\n          },\r\n          {\r\n            title: 'HTML',\r\n            color: 'primary'\r\n          }\r\n        ]\r\n      },\r\n      {\r\n        title: 'Python Developers',\r\n        avatar: '/images/logos/python-bg.png',\r\n        avatarGroup: [\r\n          { avatar: '/images/avatars/5.png', name: 'Kim Karlos' },\r\n          { avatar: '/images/avatars/6.png', name: 'Katy Turner' },\r\n          { avatar: '/images/avatars/7.png', name: 'Peter Adward' }\r\n        ],\r\n        description:\r\n          \"Harness Python's versatility for web development, data analysis & system automation for cutting-edge solutions.\",\r\n        chips: [\r\n          {\r\n            title: 'Python',\r\n            color: 'info'\r\n          }\r\n        ]\r\n      }\r\n    ],\r\n    projects: [\r\n      {\r\n        daysLeft: 28,\r\n        comments: 15,\r\n        totalTask: 344,\r\n        hours: '380/244',\r\n        tasks: '290/344',\r\n        budget: '$18.2k',\r\n        completedTask: 328,\r\n        deadline: '28/2/22',\r\n        chipColor: 'success',\r\n        startDate: '14/2/21',\r\n        budgetSpent: '$24.8k',\r\n        members: '280 members',\r\n        title: 'Social Banners',\r\n        client: 'Christian Jimenez',\r\n        avatar: '/images/icons/social-bg.png',\r\n        description: 'We are Consulting, Software Development and Web Development Services.',\r\n        avatarGroup: [\r\n          { avatar: '/images/avatars/1.png', name: 'Vinnie Mostowy' },\r\n          { avatar: '/images/avatars/2.png', name: 'Allen Rieske' },\r\n          { avatar: '/images/avatars/3.png', name: 'Julee Rossignol' }\r\n        ]\r\n      },\r\n      {\r\n        daysLeft: 15,\r\n        comments: 236,\r\n        totalTask: 90,\r\n        tasks: '12/90',\r\n        hours: '98/135',\r\n        budget: '$1.8k',\r\n        completedTask: 38,\r\n        deadline: '21/6/22',\r\n        budgetSpent: '$2.4k',\r\n        chipColor: 'warning',\r\n        startDate: '18/8/21',\r\n        members: '1.1k members',\r\n        title: 'Admin Template',\r\n        client: 'Jeffrey Phillips',\r\n        avatar: '/images/logos/react-bg.png',\r\n        avatarGroup: [\r\n          { avatar: '/images/avatars/4.png', name: \"Kaith D'souza\" },\r\n          { avatar: '/images/avatars/5.png', name: 'John Doe' },\r\n          { avatar: '/images/avatars/6.png', name: 'Alan Walker' }\r\n        ],\r\n        description: \"Time is our most valuable asset, that's why we want to help you save it.\"\r\n      },\r\n      {\r\n        daysLeft: 45,\r\n        comments: 98,\r\n        budget: '$420',\r\n        totalTask: 140,\r\n        tasks: '22/140',\r\n        hours: '880/421',\r\n        completedTask: 95,\r\n        chipColor: 'error',\r\n        budgetSpent: '$980',\r\n        deadline: '8/10/21',\r\n        title: 'App Design',\r\n        startDate: '24/7/21',\r\n        members: '458 members',\r\n        client: 'Ricky McDonald',\r\n        avatar: '/images/logos/vue-bg.png',\r\n        description: 'Figma dashboard app design combines the user UI & UX.',\r\n        avatarGroup: [\r\n          { avatar: '/images/avatars/7.png', name: 'Jimmy Ressula' },\r\n          { avatar: '/images/avatars/8.png', name: 'Kristi Lawker' },\r\n          { avatar: '/images/avatars/1.png', name: 'Danny Paul' }\r\n        ]\r\n      },\r\n      {\r\n        comments: 120,\r\n        daysLeft: 126,\r\n        totalTask: 420,\r\n        budget: '2.43k',\r\n        tasks: '237/420',\r\n        hours: '380/820',\r\n        completedTask: 302,\r\n        deadline: '12/9/22',\r\n        budgetSpent: '$8.5k',\r\n        chipColor: 'warning',\r\n        startDate: '10/2/19',\r\n        members: '137 members',\r\n        client: 'Hulda Wright',\r\n        title: 'Create Website',\r\n        avatar: '/images/logos/html-bg.png',\r\n        description: 'Your domain name should reflect your products or services so that your...',\r\n        avatarGroup: [\r\n          { avatar: '/images/avatars/2.png', name: 'Andrew Tye' },\r\n          { avatar: '/images/avatars/3.png', name: 'Rishi Swaat' },\r\n          { avatar: '/images/avatars/4.png', name: 'Rossie Kim' }\r\n        ]\r\n      },\r\n      {\r\n        daysLeft: 5,\r\n        comments: 20,\r\n        totalTask: 285,\r\n        tasks: '29/285',\r\n        budget: '28.4k',\r\n        hours: '142/420',\r\n        chipColor: 'error',\r\n        completedTask: 100,\r\n        deadline: '25/12/21',\r\n        startDate: '12/12/20',\r\n        members: '82 members',\r\n        budgetSpent: '$52.7k',\r\n        client: 'Jerry Greene',\r\n        title: 'Figma Dashboard',\r\n        avatar: '/images/logos/figma-bg.png',\r\n        description: \"Time is our most valuable asset, that's why we want to help you save it.\",\r\n        avatarGroup: [\r\n          { avatar: '/images/avatars/5.png', name: 'Kim Merchent' },\r\n          { avatar: '/images/avatars/6.png', name: \"Sam D'souza\" },\r\n          { avatar: '/images/avatars/7.png', name: 'Nurvi Karlos' }\r\n        ]\r\n      },\r\n      {\r\n        daysLeft: 4,\r\n        comments: 98,\r\n        budget: '$655',\r\n        totalTask: 290,\r\n        tasks: '29/290',\r\n        hours: '580/445',\r\n        completedTask: 290,\r\n        budgetSpent: '$1.3k',\r\n        chipColor: 'success',\r\n        deadline: '02/11/21',\r\n        startDate: '17/8/21',\r\n        title: 'Logo Design',\r\n        members: '16 members',\r\n        client: 'Olive Strickland',\r\n        avatar: '/images/logos/xd-bg.png',\r\n        description: 'Premium logo designs created by top logo designers. Create the branding.',\r\n        avatarGroup: [\r\n          { avatar: '/images/avatars/8.png', name: 'Kim Karlos' },\r\n          { avatar: '/images/avatars/1.png', name: 'Katy Turner' },\r\n          { avatar: '/images/avatars/2.png', name: 'Peter Adward' }\r\n        ]\r\n      }\r\n    ],\r\n    connections: [\r\n      {\r\n        tasks: '834',\r\n        projects: '18',\r\n        isConnected: true,\r\n        connections: '129',\r\n        name: 'Mark Gilbert',\r\n        designation: 'UI Designer',\r\n        avatar: '/images/avatars/1.png',\r\n        chips: [\r\n          {\r\n            title: 'Figma',\r\n            color: 'secondary'\r\n          },\r\n          {\r\n            title: 'Sketch',\r\n            color: 'warning'\r\n          }\r\n        ]\r\n      },\r\n      {\r\n        tasks: '2.31k',\r\n        projects: '112',\r\n        isConnected: false,\r\n        connections: '1.28k',\r\n        name: 'Eugenia Parsons',\r\n        designation: 'Developer',\r\n        avatar: '/images/avatars/2.png',\r\n        chips: [\r\n          {\r\n            color: 'error',\r\n            title: 'Angular'\r\n          },\r\n          {\r\n            color: 'info',\r\n            title: 'React'\r\n          }\r\n        ]\r\n      },\r\n      {\r\n        tasks: '1.25k',\r\n        projects: '32',\r\n        isConnected: false,\r\n        connections: '890',\r\n        name: 'Francis Byrd',\r\n        designation: 'Developer',\r\n        avatar: '/images/avatars/3.png',\r\n        chips: [\r\n          {\r\n            title: 'HTML',\r\n            color: 'primary'\r\n          },\r\n          {\r\n            color: 'info',\r\n            title: 'React'\r\n          }\r\n        ]\r\n      },\r\n      {\r\n        tasks: '12.4k',\r\n        projects: '86',\r\n        isConnected: false,\r\n        connections: '890',\r\n        name: 'Leon Lucas',\r\n        designation: 'UI/UX Designer',\r\n        avatar: '/images/avatars/4.png',\r\n        chips: [\r\n          {\r\n            title: 'Figma',\r\n            color: 'secondary'\r\n          },\r\n          {\r\n            title: 'Sketch',\r\n            color: 'warning'\r\n          },\r\n          {\r\n            color: 'primary',\r\n            title: 'Photoshop'\r\n          }\r\n        ]\r\n      },\r\n      {\r\n        tasks: '23.8k',\r\n        projects: '244',\r\n        isConnected: true,\r\n        connections: '2.14k',\r\n        name: 'Jayden Rogers',\r\n        designation: 'Full Stack Developer',\r\n        avatar: '/images/avatars/5.png',\r\n        chips: [\r\n          {\r\n            color: 'info',\r\n            title: 'React'\r\n          },\r\n          {\r\n            title: 'HTML',\r\n            color: 'warning'\r\n          },\r\n          {\r\n            color: 'success',\r\n            title: 'Node.js'\r\n          }\r\n        ]\r\n      },\r\n      {\r\n        tasks: '1.28k',\r\n        projects: '32',\r\n        isConnected: false,\r\n        designation: 'SEO',\r\n        connections: '1.27k',\r\n        name: 'Jeanette Powell',\r\n        avatar: '/images/avatars/6.png',\r\n        chips: [\r\n          {\r\n            title: 'Analysis',\r\n            color: 'secondary'\r\n          },\r\n          {\r\n            color: 'success',\r\n            title: 'Writing'\r\n          }\r\n        ]\r\n      }\r\n    ]\r\n  },\r\n  profileHeader: {\r\n    fullName: 'John Doe',\r\n    location: 'Vatican City',\r\n    lastLogin: new Date(Date.now() - 15 * 60 * 1000).toISOString(), // 15 minutes ago\r\n    designation: 'UX Designer',\r\n    profileImg: '/images/avatars/1.png',\r\n    designationIcon: 'tabler-palette',\r\n    coverImg: '/images/pages/profile-banner.png'\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;AAAO,MAAM,KAAK;IAChB,OAAO;QACL,SAAS;YACP,2CAA2C;YAC3C,mCAAmC;YACnC,UAAU;gBACR;oBAAE,UAAU;oBAAiB,OAAO;oBAAS,MAAM;gBAAe;gBAClE;oBAAE,UAAU;oBAAe,OAAO;oBAAO,MAAM;gBAAe;gBAC9D;oBAAE,UAAU;oBAAqB,OAAO;oBAAO,MAAM;gBAAqB;aAC3E;YACD,aAAa;gBACX;oBACE,UAAU;oBACV,aAAa;oBACb,MAAM;oBACN,QAAQ;gBACV;gBACA;oBACE,UAAU;oBACV,aAAa;oBACb,MAAM;oBACN,QAAQ;gBACV;gBACA;oBACE,UAAU;oBACV,aAAa;oBACb,MAAM;oBACN,QAAQ;gBACV;gBACA;oBACE,UAAU;oBACV,aAAa;oBACb,MAAM;oBACN,QAAQ;gBACV;gBACA;oBACE,UAAU;oBACV,aAAa;oBACb,MAAM;oBACN,QAAQ;gBACV;aACD;YACD,WAAW;gBACT;oBACE,SAAS;oBACT,WAAW;oBACX,UAAU;oBACV,OAAO;oBACP,QAAQ;gBACV;gBACA;oBACE,SAAS;oBACT,UAAU;oBACV,WAAW;oBACX,OAAO;oBACP,QAAQ;gBACV;gBACA;oBACE,SAAS;oBACT,WAAW;oBACX,UAAU;oBACV,OAAO;oBACP,QAAQ;gBACV;gBACA;oBACE,SAAS;oBACT,WAAW;oBACX,UAAU;oBACV,OAAO;oBACP,QAAQ;gBACV;gBACA;oBACE,SAAS;oBACT,UAAU;oBACV,WAAW;oBACX,OAAO;oBACP,QAAQ;gBACV;aACD;YACD,cAAc;gBACZ;oBACE,IAAI;oBACJ,OAAO;oBACP,UAAU;oBACV,QAAQ;oBACR,QAAQ;oBACR,aAAa;wBACX;wBACA;wBACA;wBACA;qBACD;oBACD,QAAQ;gBACV;gBACA;oBACE,IAAI;oBACJ,QAAQ;oBACR,OAAO;oBACP,UAAU;oBACV,QAAQ;oBACR,aAAa;wBAAC;wBAAyB;qBAAwB;oBAC/D,QAAQ;gBACV;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,UAAU;oBACV,QAAQ;oBACR,QAAQ;oBACR,aAAa;wBACX;wBACA;wBACA;wBACA;qBACD;oBACD,QAAQ;gBACV;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,UAAU;oBACV,QAAQ;oBACR,QAAQ;oBACR,aAAa;wBACX;wBACA;wBACA;wBACA;qBACD;oBACD,QAAQ;gBACV;gBACA;oBACE,IAAI;oBACJ,QAAQ;oBACR,OAAO;oBACP,UAAU;oBACV,QAAQ;oBACR,aAAa;wBAAC;wBAAyB;wBAAyB;qBAAwB;oBACxF,QAAQ;gBACV;gBACA;oBACE,IAAI;oBACJ,QAAQ;oBACR,OAAO;oBACP,UAAU;oBACV,QAAQ;oBACR,aAAa;wBACX;wBACA;wBACA;wBACA;qBACD;oBACD,QAAQ;gBACV;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,UAAU;oBACV,QAAQ;oBACR,QAAQ;oBACR,aAAa;wBACX;wBACA;wBACA;wBACA;qBACD;oBACD,QAAQ;gBACV;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,UAAU;oBACV,QAAQ;oBACR,QAAQ;oBACR,aAAa;wBACX;wBACA;wBACA;wBACA;qBACD;oBACD,QAAQ;gBACV;gBACA;oBACE,IAAI;oBACJ,QAAQ;oBACR,OAAO;oBACP,UAAU;oBACV,QAAQ;oBACR,aAAa;wBAAC;wBAAyB;qBAAwB;oBAC/D,QAAQ;gBACV;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,UAAU;oBACV,QAAQ;oBACR,QAAQ;oBACR,aAAa;wBACX;wBACA;wBACA;wBACA;qBACD;oBACD,QAAQ;gBACV;aACD;QACH;QACA,OAAO;YACL;gBACE,cAAc;gBACd,OAAO;gBACP,QAAQ;gBACR,aAAa;oBACX;wBAAE,QAAQ;wBAAyB,MAAM;oBAAiB;oBAC1D;wBAAE,QAAQ;wBAAyB,MAAM;oBAAe;oBACxD;wBAAE,QAAQ;wBAAyB,MAAM;oBAAkB;iBAC5D;gBACD,aACE;gBACF,OAAO;oBACL;wBACE,OAAO;wBACP,OAAO;oBACT;oBACA;wBACE,OAAO;wBACP,OAAO;oBACT;iBACD;YACH;YACA;gBACE,cAAc;gBACd,OAAO;gBACP,QAAQ;gBACR,aAAa;oBACX;wBAAE,QAAQ;wBAAyB,MAAM;oBAAgB;oBACzD;wBAAE,QAAQ;wBAAyB,MAAM;oBAAW;oBACpD;wBAAE,QAAQ;wBAAyB,MAAM;oBAAc;iBACxD;gBACD,aACE;gBACF,OAAO;oBACL;wBACE,OAAO;wBACP,OAAO;oBACT;oBACA;wBACE,OAAO;wBACP,OAAO;oBACT;iBACD;YACH;YACA;gBACE,OAAO;gBACP,QAAQ;gBACR,aAAa;oBACX;wBAAE,QAAQ;wBAAyB,MAAM;oBAAgB;oBACzD;wBAAE,QAAQ;wBAAyB,MAAM;oBAAgB;oBACzD;wBAAE,QAAQ;wBAAyB,MAAM;oBAAa;iBACvD;gBACD,aACE;gBACF,OAAO;oBACL;wBACE,OAAO;wBACP,OAAO;oBACT;oBACA;wBACE,OAAO;wBACP,OAAO;oBACT;iBACD;YACH;YACA;gBACE,OAAO;gBACP,QAAQ;gBACR,aAAa;oBACX;wBAAE,QAAQ;wBAAyB,MAAM;oBAAa;oBACtD;wBAAE,QAAQ;wBAAyB,MAAM;oBAAc;oBACvD;wBAAE,QAAQ;wBAAyB,MAAM;oBAAa;iBACvD;gBACD,aACE;gBACF,OAAO;oBACL;wBACE,OAAO;wBACP,OAAO;oBACT;iBACD;YACH;YACA;gBACE,cAAc;gBACd,OAAO;gBACP,QAAQ;gBACR,aAAa;oBACX;wBAAE,QAAQ;wBAAyB,MAAM;oBAAe;oBACxD;wBAAE,QAAQ;wBAAyB,MAAM;oBAAc;oBACvD;wBAAE,QAAQ;wBAAyB,MAAM;oBAAe;iBACzD;gBACD,aACE;gBACF,OAAO;oBACL;wBACE,OAAO;wBACP,OAAO;oBACT;oBACA;wBACE,OAAO;wBACP,OAAO;oBACT;iBACD;YACH;YACA;gBACE,cAAc;gBACd,OAAO;gBACP,QAAQ;gBACR,aAAa;oBACX;wBAAE,QAAQ;wBAAyB,MAAM;oBAAiB;oBAC1D;wBAAE,QAAQ;wBAAyB,MAAM;oBAAe;oBACxD;wBAAE,QAAQ;wBAAyB,MAAM;oBAAkB;iBAC5D;gBACD,aACE;gBACF,OAAO;oBACL;wBACE,OAAO;wBACP,OAAO;oBACT;iBACD;YACH;YACA;gBACE,OAAO;gBACP,QAAQ;gBACR,aAAa;oBACX;wBAAE,QAAQ;wBAAyB,MAAM;oBAAiB;oBAC1D;wBAAE,QAAQ;wBAAyB,MAAM;oBAAgB;oBACzD;wBAAE,QAAQ;wBAAyB,MAAM;oBAAa;iBACvD;gBACD,aACE;gBACF,OAAO;oBACL;wBACE,OAAO;wBACP,OAAO;oBACT;oBACA;wBACE,OAAO;wBACP,OAAO;oBACT;iBACD;YACH;YACA;gBACE,cAAc;gBACd,OAAO;gBACP,QAAQ;gBACR,aAAa;oBACX;wBAAE,QAAQ;wBAAyB,MAAM;oBAAa;oBACtD;wBAAE,QAAQ;wBAAyB,MAAM;oBAAc;oBACvD;wBAAE,QAAQ;wBAAyB,MAAM;oBAAe;iBACzD;gBACD,aACE;gBACF,OAAO;oBACL;wBACE,OAAO;wBACP,OAAO;oBACT;oBACA;wBACE,OAAO;wBACP,OAAO;oBACT;iBACD;YACH;YACA;gBACE,OAAO;gBACP,QAAQ;gBACR,aAAa;oBACX;wBAAE,QAAQ;wBAAyB,MAAM;oBAAa;oBACtD;wBAAE,QAAQ;wBAAyB,MAAM;oBAAc;oBACvD;wBAAE,QAAQ;wBAAyB,MAAM;oBAAe;iBACzD;gBACD,aACE;gBACF,OAAO;oBACL;wBACE,OAAO;wBACP,OAAO;oBACT;iBACD;YACH;SACD;QACD,UAAU;YACR;gBACE,UAAU;gBACV,UAAU;gBACV,WAAW;gBACX,OAAO;gBACP,OAAO;gBACP,QAAQ;gBACR,eAAe;gBACf,UAAU;gBACV,WAAW;gBACX,WAAW;gBACX,aAAa;gBACb,SAAS;gBACT,OAAO;gBACP,QAAQ;gBACR,QAAQ;gBACR,aAAa;gBACb,aAAa;oBACX;wBAAE,QAAQ;wBAAyB,MAAM;oBAAiB;oBAC1D;wBAAE,QAAQ;wBAAyB,MAAM;oBAAe;oBACxD;wBAAE,QAAQ;wBAAyB,MAAM;oBAAkB;iBAC5D;YACH;YACA;gBACE,UAAU;gBACV,UAAU;gBACV,WAAW;gBACX,OAAO;gBACP,OAAO;gBACP,QAAQ;gBACR,eAAe;gBACf,UAAU;gBACV,aAAa;gBACb,WAAW;gBACX,WAAW;gBACX,SAAS;gBACT,OAAO;gBACP,QAAQ;gBACR,QAAQ;gBACR,aAAa;oBACX;wBAAE,QAAQ;wBAAyB,MAAM;oBAAgB;oBACzD;wBAAE,QAAQ;wBAAyB,MAAM;oBAAW;oBACpD;wBAAE,QAAQ;wBAAyB,MAAM;oBAAc;iBACxD;gBACD,aAAa;YACf;YACA;gBACE,UAAU;gBACV,UAAU;gBACV,QAAQ;gBACR,WAAW;gBACX,OAAO;gBACP,OAAO;gBACP,eAAe;gBACf,WAAW;gBACX,aAAa;gBACb,UAAU;gBACV,OAAO;gBACP,WAAW;gBACX,SAAS;gBACT,QAAQ;gBACR,QAAQ;gBACR,aAAa;gBACb,aAAa;oBACX;wBAAE,QAAQ;wBAAyB,MAAM;oBAAgB;oBACzD;wBAAE,QAAQ;wBAAyB,MAAM;oBAAgB;oBACzD;wBAAE,QAAQ;wBAAyB,MAAM;oBAAa;iBACvD;YACH;YACA;gBACE,UAAU;gBACV,UAAU;gBACV,WAAW;gBACX,QAAQ;gBACR,OAAO;gBACP,OAAO;gBACP,eAAe;gBACf,UAAU;gBACV,aAAa;gBACb,WAAW;gBACX,WAAW;gBACX,SAAS;gBACT,QAAQ;gBACR,OAAO;gBACP,QAAQ;gBACR,aAAa;gBACb,aAAa;oBACX;wBAAE,QAAQ;wBAAyB,MAAM;oBAAa;oBACtD;wBAAE,QAAQ;wBAAyB,MAAM;oBAAc;oBACvD;wBAAE,QAAQ;wBAAyB,MAAM;oBAAa;iBACvD;YACH;YACA;gBACE,UAAU;gBACV,UAAU;gBACV,WAAW;gBACX,OAAO;gBACP,QAAQ;gBACR,OAAO;gBACP,WAAW;gBACX,eAAe;gBACf,UAAU;gBACV,WAAW;gBACX,SAAS;gBACT,aAAa;gBACb,QAAQ;gBACR,OAAO;gBACP,QAAQ;gBACR,aAAa;gBACb,aAAa;oBACX;wBAAE,QAAQ;wBAAyB,MAAM;oBAAe;oBACxD;wBAAE,QAAQ;wBAAyB,MAAM;oBAAc;oBACvD;wBAAE,QAAQ;wBAAyB,MAAM;oBAAe;iBACzD;YACH;YACA;gBACE,UAAU;gBACV,UAAU;gBACV,QAAQ;gBACR,WAAW;gBACX,OAAO;gBACP,OAAO;gBACP,eAAe;gBACf,aAAa;gBACb,WAAW;gBACX,UAAU;gBACV,WAAW;gBACX,OAAO;gBACP,SAAS;gBACT,QAAQ;gBACR,QAAQ;gBACR,aAAa;gBACb,aAAa;oBACX;wBAAE,QAAQ;wBAAyB,MAAM;oBAAa;oBACtD;wBAAE,QAAQ;wBAAyB,MAAM;oBAAc;oBACvD;wBAAE,QAAQ;wBAAyB,MAAM;oBAAe;iBACzD;YACH;SACD;QACD,aAAa;YACX;gBACE,OAAO;gBACP,UAAU;gBACV,aAAa;gBACb,aAAa;gBACb,MAAM;gBACN,aAAa;gBACb,QAAQ;gBACR,OAAO;oBACL;wBACE,OAAO;wBACP,OAAO;oBACT;oBACA;wBACE,OAAO;wBACP,OAAO;oBACT;iBACD;YACH;YACA;gBACE,OAAO;gBACP,UAAU;gBACV,aAAa;gBACb,aAAa;gBACb,MAAM;gBACN,aAAa;gBACb,QAAQ;gBACR,OAAO;oBACL;wBACE,OAAO;wBACP,OAAO;oBACT;oBACA;wBACE,OAAO;wBACP,OAAO;oBACT;iBACD;YACH;YACA;gBACE,OAAO;gBACP,UAAU;gBACV,aAAa;gBACb,aAAa;gBACb,MAAM;gBACN,aAAa;gBACb,QAAQ;gBACR,OAAO;oBACL;wBACE,OAAO;wBACP,OAAO;oBACT;oBACA;wBACE,OAAO;wBACP,OAAO;oBACT;iBACD;YACH;YACA;gBACE,OAAO;gBACP,UAAU;gBACV,aAAa;gBACb,aAAa;gBACb,MAAM;gBACN,aAAa;gBACb,QAAQ;gBACR,OAAO;oBACL;wBACE,OAAO;wBACP,OAAO;oBACT;oBACA;wBACE,OAAO;wBACP,OAAO;oBACT;oBACA;wBACE,OAAO;wBACP,OAAO;oBACT;iBACD;YACH;YACA;gBACE,OAAO;gBACP,UAAU;gBACV,aAAa;gBACb,aAAa;gBACb,MAAM;gBACN,aAAa;gBACb,QAAQ;gBACR,OAAO;oBACL;wBACE,OAAO;wBACP,OAAO;oBACT;oBACA;wBACE,OAAO;wBACP,OAAO;oBACT;oBACA;wBACE,OAAO;wBACP,OAAO;oBACT;iBACD;YACH;YACA;gBACE,OAAO;gBACP,UAAU;gBACV,aAAa;gBACb,aAAa;gBACb,aAAa;gBACb,MAAM;gBACN,QAAQ;gBACR,OAAO;oBACL;wBACE,OAAO;wBACP,OAAO;oBACT;oBACA;wBACE,OAAO;wBACP,OAAO;oBACT;iBACD;YACH;SACD;IACH;IACA,eAAe;QACb,UAAU;QACV,UAAU;QACV,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,MAAM,WAAW;QAC5D,aAAa;QACb,YAAY;QACZ,iBAAiB;QACjB,UAAU;IACZ;AACF", "debugId": null}}, {"offset": {"line": 6798, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/libs/auth.js"], "sourcesContent": ["// Third-party Imports\r\nimport Cred<PERSON>Provider from 'next-auth/providers/credentials'\r\nimport GoogleProvider from 'next-auth/providers/google'\r\nimport { PrismaAdapter } from '@auth/prisma-adapter'\r\nimport { PrismaClient } from '@prisma/client'\r\n\r\n// No fake data imports - using real backend only\r\n\r\nconst prisma = new PrismaClient()\r\n\r\nexport const authOptions = {\r\n  adapter: PrismaAdapter(prisma),\r\n  providers: [\r\n    CredentialProvider({\r\n      name: 'Credentials',\r\n      type: 'credentials',\r\n      credentials: {},\r\n      async authorize(credentials) {\r\n        console.log('🚀 AUTHORIZE FUNCTION CALLED!', credentials)\r\n\r\n        if (!credentials) {\r\n          return null\r\n        }\r\n\r\n        const { userId, username, email, password, mfaToken, step } = credentials\r\n\r\n        console.log('🔐 Auth attempt:', { email, username, step, hasMfaToken: !!mfaToken, userId })\r\n\r\n        // Handle email-verified users (after OTP verification)\r\n        if (step === 'email-verified') {\r\n          console.log('📧 Email-verified user login')\r\n\r\n          try {\r\n            const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8090'\r\n            const response = await fetch(`${API_BASE_URL}/user-profile/${userId}`, {\r\n              method: 'GET',\r\n              headers: {\r\n                'Content-Type': 'application/json',\r\n              }\r\n            })\r\n\r\n            if (response.ok) {\r\n              const result = await response.json()\r\n              const apiUser = result.user\r\n\r\n              if (apiUser && apiUser.isVerified) {\r\n                return {\r\n                  id: apiUser.id || apiUser._id,\r\n                  username: apiUser.username,\r\n                  name: apiUser.username,\r\n                  email: apiUser.email,\r\n                  image: '/images/avatars/1.png',\r\n                  role: apiUser.role || 'admin',\r\n                  isVerified: true,\r\n                  requiresMFA: false, // Skip MFA for now, will be set up later\r\n                  mfaEnabled: apiUser.mfaEnabled || false,\r\n                  mfaVerified: true // Consider verified for email-verified users\r\n                }\r\n              }\r\n            }\r\n          } catch (error) {\r\n            console.error('❌ Error fetching verified user:', error)\r\n          }\r\n\r\n          return null\r\n        }\r\n\r\n        // No fake user lookup - only use real backend data\r\n        console.log('👤 Using real backend authentication only')\r\n\r\n        // Step 1: Password verification\r\n        if (step !== 'mfa') {\r\n          console.log('🔐 Regular login step')\r\n\r\n          try {\r\n            const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8090'\r\n\r\n            console.log('🌐 Making login API call to:', `${API_BASE_URL}/login`)\r\n\r\n            const response = await fetch(`${API_BASE_URL}/login`, {\r\n              method: 'POST',\r\n              headers: {\r\n                'Content-Type': 'application/json',\r\n              },\r\n              body: JSON.stringify({\r\n                username: username,\r\n                email: email,\r\n                password: password\r\n              })\r\n            })\r\n\r\n            console.log('📡 Login API response status:', response.status)\r\n\r\n            if (response.ok) {\r\n              const result = await response.json()\r\n\r\n              console.log('✅ Login API success:', result)\r\n\r\n              if (result.success && result.user) {\r\n                const apiUser = result.user\r\n\r\n                console.log('✅ Backend user data:', {\r\n                  id: apiUser.id || apiUser._id,\r\n                  username: apiUser.username,\r\n                  email: apiUser.email,\r\n                  isVerified: apiUser.isVerified,\r\n                  mfaEnabled: apiUser.mfaEnabled,\r\n                  requiresMFA: result.requiresMFA,\r\n                  requiresEmailOTP: result.requiresEmailOTP,\r\n                  requiresEmailVerification: result.requiresEmailVerification\r\n                });\r\n\r\n                // CRITICAL: Check if backend requires Email OTP verification\r\n                if (result.requiresEmailOTP) {\r\n                  console.log('📧 Backend requires Email OTP verification for login - BLOCKING session creation')\r\n                  console.log('🚫 Returning null to prevent session creation until OTP is verified')\r\n\r\n                  // SECURITY: Do NOT return a user object when OTP verification is required\r\n                  // This prevents session creation and forces the frontend to handle OTP verification\r\n                  // The frontend Login.jsx component will detect requiresEmailOTP and show OTP input\r\n                  return null\r\n                }\r\n\r\n                // Check if backend requires Email verification (for new users)\r\n                if (result.requiresEmailVerification) {\r\n                  console.log('📧 Backend requires Email verification for new user')\r\n\r\n                  return {\r\n                    id: apiUser.id || apiUser._id,\r\n                    username: apiUser.username,\r\n                    name: apiUser.username,\r\n                    email: apiUser.email,\r\n                    image: '/images/avatars/1.png',\r\n                    role: apiUser.role || 'super_admin',\r\n                    isVerified: false,\r\n                    requiresEmailVerification: true,\r\n                    mfaEnabled: apiUser.mfaEnabled,\r\n                    mfaVerified: false,\r\n                    // Special flag to indicate verification is pending\r\n                    verificationPending: true,\r\n                    verificationType: 'email'\r\n                  }\r\n                }\r\n\r\n                // Check if backend requires MFA verification\r\n                if (result.requiresMFA) {\r\n                  console.log('🔐 Backend requires MFA verification')\r\n\r\n                  return {\r\n                    id: apiUser.id || apiUser._id,\r\n                    username: apiUser.username,\r\n                    name: apiUser.username,\r\n                    email: apiUser.email,\r\n                    image: '/images/avatars/1.png',\r\n                    role: apiUser.role || 'super_admin',\r\n                    isVerified: apiUser.isVerified,\r\n                    requiresMFA: true,\r\n                    mfaEnabled: apiUser.mfaEnabled,\r\n                    mfaVerified: false\r\n                  }\r\n                }\r\n\r\n                // If no additional verification required, login successful\r\n                console.log('✅ Login successful, no additional verification required')\r\n\r\n                return {\r\n                  id: apiUser.id || apiUser._id,\r\n                  username: apiUser.username,\r\n                  name: apiUser.username,\r\n                  email: apiUser.email,\r\n                  image: '/images/avatars/1.png',\r\n                  role: apiUser.role || 'super_admin',\r\n                  isVerified: apiUser.isVerified,\r\n                  requiresMFA: false,\r\n                  mfaEnabled: apiUser.mfaEnabled,\r\n                  mfaVerified: true\r\n                }\r\n              }\r\n            } else {\r\n              const errorData = await response.json()\r\n\r\n              console.log('❌ Login API error:', errorData)\r\n            }\r\n          } catch (error) {\r\n            console.error('❌ Login API request failed:', error)\r\n          }\r\n\r\n          // No fallback - only use real backend data\r\n          console.log('❌ Backend API failed, no fallback allowed')\r\n          return null\r\n        }\r\n\r\n        // Step 2: MFA verification\r\n        if (step === 'mfa' && mfaToken) {\r\n          console.log('🔐 MFA verification step - using backend API only')\r\n\r\n          if (username && email) {\r\n            console.log('🔐 MFA verification step triggered:', {\r\n              step,\r\n              username,\r\n              email,\r\n              mfaToken: mfaToken ? `${mfaToken.substring(0, 2)}****` : 'null',\r\n              tokenLength: mfaToken ? mfaToken.length : 0\r\n            })\r\n\r\n            try {\r\n              const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8090'\r\n\r\n              console.log('🌐 Making MFA verification API call to:', `${API_BASE_URL}/login/mfa-verify`)\r\n\r\n              const requestBody = {\r\n                username: username,\r\n                email: email,\r\n                password: 'verified',\r\n                mfaToken: mfaToken,\r\n                step: 'mfa'\r\n              }\r\n\r\n              console.log('📤 Sending MFA verification request:', requestBody)\r\n\r\n              const response = await fetch(`${API_BASE_URL}/login/mfa-verify`, {\r\n                method: 'POST',\r\n                headers: {\r\n                  'Content-Type': 'application/json',\r\n                },\r\n                body: JSON.stringify(requestBody)\r\n              })\r\n\r\n              console.log('📡 MFA verification API response status:', response.status)\r\n              console.log('📡 MFA verification API response headers:', Object.fromEntries(response.headers.entries()))\r\n\r\n              if (response.ok) {\r\n                let result\r\n                try {\r\n                  const responseText = await response.text()\r\n                  console.log('📥 Raw MFA verification response:', responseText)\r\n                  result = JSON.parse(responseText)\r\n                } catch (parseError) {\r\n                  console.error('❌ Failed to parse MFA verification response:', parseError)\r\n                  return null\r\n                }\r\n\r\n                console.log('✅ MFA verification API response:', result)\r\n\r\n                if (result && result.success) {\r\n                  console.log('✅ MFA verification successful, returning user')\r\n\r\n                  // Use the real user data from backend response, not fake data\r\n                  const backendUser = result.user\r\n\r\n                  const updatedUser = {\r\n                    id: backendUser.id || backendUser._id, // Use real ObjectId from backend\r\n                    username: backendUser.username,\r\n                    name: backendUser.username,\r\n                    email: backendUser.email,\r\n                    image: '/images/avatars/1.png',\r\n                    role: backendUser.role || 'super_admin', // Temporary: default to super_admin for testing\r\n                    isVerified: backendUser.isVerified,\r\n                    requiresMFA: backendUser.mfaEnabled || false,\r\n                    mfaEnabled: backendUser.mfaEnabled || false,\r\n                    mfaVerified: true, // Only mark as verified for this session\r\n                    mfaVerifiedAt: Date.now() // Add timestamp\r\n                  }\r\n\r\n                  console.log('🔄 Returning updated user with real backend data:', updatedUser)\r\n\r\n                  return updatedUser\r\n                } else {\r\n                  console.log('❌ MFA verification failed:', result?.message || 'Unknown error')\r\n                  return null\r\n                }\r\n              } else {\r\n                console.log('❌ MFA verification API call failed with status:', response.status)\r\n                const errorText = await response.text()\r\n                console.log('❌ Error response:', errorText)\r\n                return null\r\n              }\r\n            } catch (error) {\r\n              console.error('❌ Error verifying MFA:', error)\r\n              // Don't throw here, return null to let NextAuth handle the error\r\n              return null\r\n            }\r\n          } else {\r\n            console.log('❌ MFA step called but missing user data:', {\r\n              step,\r\n              hasMfaToken: !!mfaToken,\r\n              username,\r\n              email\r\n            })\r\n            return null\r\n          }\r\n        }\r\n\r\n        console.log('❌ No valid authentication path found')\r\n        return null\r\n      }\r\n    }),\r\n    GoogleProvider({\r\n      clientId: process.env.GOOGLE_CLIENT_ID,\r\n      clientSecret: process.env.GOOGLE_CLIENT_SECRET\r\n    })\r\n  ],\r\n  session: {\r\n    strategy: 'jwt',\r\n    maxAge: 30 * 24 * 60 * 60 // 30 days\r\n  },\r\n  pages: {\r\n    signIn: '/login'\r\n  },\r\n  callbacks: {\r\n    async jwt({ token, user, trigger, session }) {\r\n      // Handle new login (when user object is provided)\r\n      if (user) {\r\n        // CRITICAL SECURITY FIX: Always reset MFA state for new logins\r\n        token.id = user.id\r\n        token.name = user.name\r\n        token.email = user.email\r\n        token.role = user.role || 'admin'\r\n        token.isVerified = user.isVerified\r\n        token.requiresMFA = user.requiresMFA\r\n        token.loginTimestamp = user.loginTimestamp || Date.now()\r\n\r\n        // Set MFA verification status based on user type\r\n        // Email-verified users are considered verified, others need MFA verification\r\n        token.mfaVerified = user.mfaVerified || false\r\n        token.mfaVerifiedAt = user.mfaVerified ? Date.now() : null\r\n\r\n        // CRITICAL: Preserve OTP and verification flags\r\n        token.otpPending = user.otpPending || false\r\n        token.otpType = user.otpType || null\r\n        token.verificationPending = user.verificationPending || false\r\n        token.verificationType = user.verificationType || null\r\n        token.requiresEmailOTP = user.requiresEmailOTP || false\r\n        token.requiresEmailVerification = user.requiresEmailVerification || false\r\n\r\n        console.log('🔐 JWT: New login detected for user:', user.email, {\r\n          otpPending: token.otpPending,\r\n          otpType: token.otpType,\r\n          verificationPending: token.verificationPending,\r\n          verificationType: token.verificationType,\r\n          requiresEmailOTP: token.requiresEmailOTP,\r\n          requiresEmailVerification: token.requiresEmailVerification\r\n        })\r\n      }\r\n\r\n      // For MFA verification updates during the same session\r\n      if (trigger === 'update' && session?.mfaVerified !== undefined) {\r\n        token.mfaVerified = session.mfaVerified\r\n        if (session.mfaVerified) {\r\n          token.mfaVerifiedAt = Date.now()\r\n        }\r\n        console.log('🔐 JWT: MFA verification updated via session update', {\r\n          mfaVerified: token.mfaVerified,\r\n          mfaVerifiedAt: token.mfaVerifiedAt\r\n        })\r\n      }\r\n\r\n      return token\r\n    },\r\n    async session({ session, token }) {\r\n      if (session.user) {\r\n        session.user.id = token.id\r\n        session.user.name = token.name\r\n        session.user.email = token.email\r\n        session.user.role = token.role\r\n        session.user.isVerified = token.isVerified\r\n        session.user.requiresMFA = token.requiresMFA\r\n        session.user.mfaVerified = token.mfaVerified\r\n        session.user.mfaVerifiedAt = token.mfaVerifiedAt\r\n        session.user.loginTimestamp = token.loginTimestamp\r\n\r\n        // CRITICAL: Include OTP and verification flags in session\r\n        session.user.otpPending = token.otpPending\r\n        session.user.otpType = token.otpType\r\n        session.user.verificationPending = token.verificationPending\r\n        session.user.verificationType = token.verificationType\r\n        session.user.requiresEmailOTP = token.requiresEmailOTP\r\n        session.user.requiresEmailVerification = token.requiresEmailVerification\r\n\r\n        console.log('📋 Session created with flags:', {\r\n          email: session.user.email,\r\n          otpPending: session.user.otpPending,\r\n          otpType: session.user.otpType,\r\n          verificationPending: session.user.verificationPending,\r\n          verificationType: session.user.verificationType\r\n        })\r\n      }\r\n\r\n      return session\r\n    }\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA,sBAAsB;;;;AACtB;AACA;AACA;AACA;;;;;AAEA,iDAAiD;AAEjD,MAAM,SAAS,IAAI,6HAAA,CAAA,eAAY;AAExB,MAAM,cAAc;IACzB,SAAS,CAAA,GAAA,oJAAA,CAAA,gBAAa,AAAD,EAAE;IACvB,WAAW;QACT,CAAA,GAAA,wJAAA,CAAA,UAAkB,AAAD,EAAE;YACjB,MAAM;YACN,MAAM;YACN,aAAa,CAAC;YACd,MAAM,WAAU,WAAW;gBACzB,QAAQ,GAAG,CAAC,iCAAiC;gBAE7C,IAAI,CAAC,aAAa;oBAChB,OAAO;gBACT;gBAEA,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG;gBAE9D,QAAQ,GAAG,CAAC,oBAAoB;oBAAE;oBAAO;oBAAU;oBAAM,aAAa,CAAC,CAAC;oBAAU;gBAAO;gBAEzF,uDAAuD;gBACvD,IAAI,SAAS,kBAAkB;oBAC7B,QAAQ,GAAG,CAAC;oBAEZ,IAAI;wBACF,MAAM,eAAe,6DAAmC;wBACxD,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,cAAc,EAAE,QAAQ,EAAE;4BACrE,QAAQ;4BACR,SAAS;gCACP,gBAAgB;4BAClB;wBACF;wBAEA,IAAI,SAAS,EAAE,EAAE;4BACf,MAAM,SAAS,MAAM,SAAS,IAAI;4BAClC,MAAM,UAAU,OAAO,IAAI;4BAE3B,IAAI,WAAW,QAAQ,UAAU,EAAE;gCACjC,OAAO;oCACL,IAAI,QAAQ,EAAE,IAAI,QAAQ,GAAG;oCAC7B,UAAU,QAAQ,QAAQ;oCAC1B,MAAM,QAAQ,QAAQ;oCACtB,OAAO,QAAQ,KAAK;oCACpB,OAAO;oCACP,MAAM,QAAQ,IAAI,IAAI;oCACtB,YAAY;oCACZ,aAAa;oCACb,YAAY,QAAQ,UAAU,IAAI;oCAClC,aAAa,KAAK,6CAA6C;gCACjE;4BACF;wBACF;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,mCAAmC;oBACnD;oBAEA,OAAO;gBACT;gBAEA,mDAAmD;gBACnD,QAAQ,GAAG,CAAC;gBAEZ,gCAAgC;gBAChC,IAAI,SAAS,OAAO;oBAClB,QAAQ,GAAG,CAAC;oBAEZ,IAAI;wBACF,MAAM,eAAe,6DAAmC;wBAExD,QAAQ,GAAG,CAAC,gCAAgC,GAAG,aAAa,MAAM,CAAC;wBAEnE,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,MAAM,CAAC,EAAE;4BACpD,QAAQ;4BACR,SAAS;gCACP,gBAAgB;4BAClB;4BACA,MAAM,KAAK,SAAS,CAAC;gCACnB,UAAU;gCACV,OAAO;gCACP,UAAU;4BACZ;wBACF;wBAEA,QAAQ,GAAG,CAAC,iCAAiC,SAAS,MAAM;wBAE5D,IAAI,SAAS,EAAE,EAAE;4BACf,MAAM,SAAS,MAAM,SAAS,IAAI;4BAElC,QAAQ,GAAG,CAAC,wBAAwB;4BAEpC,IAAI,OAAO,OAAO,IAAI,OAAO,IAAI,EAAE;gCACjC,MAAM,UAAU,OAAO,IAAI;gCAE3B,QAAQ,GAAG,CAAC,wBAAwB;oCAClC,IAAI,QAAQ,EAAE,IAAI,QAAQ,GAAG;oCAC7B,UAAU,QAAQ,QAAQ;oCAC1B,OAAO,QAAQ,KAAK;oCACpB,YAAY,QAAQ,UAAU;oCAC9B,YAAY,QAAQ,UAAU;oCAC9B,aAAa,OAAO,WAAW;oCAC/B,kBAAkB,OAAO,gBAAgB;oCACzC,2BAA2B,OAAO,yBAAyB;gCAC7D;gCAEA,6DAA6D;gCAC7D,IAAI,OAAO,gBAAgB,EAAE;oCAC3B,QAAQ,GAAG,CAAC;oCACZ,QAAQ,GAAG,CAAC;oCAEZ,0EAA0E;oCAC1E,oFAAoF;oCACpF,mFAAmF;oCACnF,OAAO;gCACT;gCAEA,+DAA+D;gCAC/D,IAAI,OAAO,yBAAyB,EAAE;oCACpC,QAAQ,GAAG,CAAC;oCAEZ,OAAO;wCACL,IAAI,QAAQ,EAAE,IAAI,QAAQ,GAAG;wCAC7B,UAAU,QAAQ,QAAQ;wCAC1B,MAAM,QAAQ,QAAQ;wCACtB,OAAO,QAAQ,KAAK;wCACpB,OAAO;wCACP,MAAM,QAAQ,IAAI,IAAI;wCACtB,YAAY;wCACZ,2BAA2B;wCAC3B,YAAY,QAAQ,UAAU;wCAC9B,aAAa;wCACb,mDAAmD;wCACnD,qBAAqB;wCACrB,kBAAkB;oCACpB;gCACF;gCAEA,6CAA6C;gCAC7C,IAAI,OAAO,WAAW,EAAE;oCACtB,QAAQ,GAAG,CAAC;oCAEZ,OAAO;wCACL,IAAI,QAAQ,EAAE,IAAI,QAAQ,GAAG;wCAC7B,UAAU,QAAQ,QAAQ;wCAC1B,MAAM,QAAQ,QAAQ;wCACtB,OAAO,QAAQ,KAAK;wCACpB,OAAO;wCACP,MAAM,QAAQ,IAAI,IAAI;wCACtB,YAAY,QAAQ,UAAU;wCAC9B,aAAa;wCACb,YAAY,QAAQ,UAAU;wCAC9B,aAAa;oCACf;gCACF;gCAEA,2DAA2D;gCAC3D,QAAQ,GAAG,CAAC;gCAEZ,OAAO;oCACL,IAAI,QAAQ,EAAE,IAAI,QAAQ,GAAG;oCAC7B,UAAU,QAAQ,QAAQ;oCAC1B,MAAM,QAAQ,QAAQ;oCACtB,OAAO,QAAQ,KAAK;oCACpB,OAAO;oCACP,MAAM,QAAQ,IAAI,IAAI;oCACtB,YAAY,QAAQ,UAAU;oCAC9B,aAAa;oCACb,YAAY,QAAQ,UAAU;oCAC9B,aAAa;gCACf;4BACF;wBACF,OAAO;4BACL,MAAM,YAAY,MAAM,SAAS,IAAI;4BAErC,QAAQ,GAAG,CAAC,sBAAsB;wBACpC;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,+BAA+B;oBAC/C;oBAEA,2CAA2C;oBAC3C,QAAQ,GAAG,CAAC;oBACZ,OAAO;gBACT;gBAEA,2BAA2B;gBAC3B,IAAI,SAAS,SAAS,UAAU;oBAC9B,QAAQ,GAAG,CAAC;oBAEZ,IAAI,YAAY,OAAO;wBACrB,QAAQ,GAAG,CAAC,uCAAuC;4BACjD;4BACA;4BACA;4BACA,UAAU,WAAW,GAAG,SAAS,SAAS,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG;4BACzD,aAAa,WAAW,SAAS,MAAM,GAAG;wBAC5C;wBAEA,IAAI;4BACF,MAAM,eAAe,6DAAmC;4BAExD,QAAQ,GAAG,CAAC,2CAA2C,GAAG,aAAa,iBAAiB,CAAC;4BAEzF,MAAM,cAAc;gCAClB,UAAU;gCACV,OAAO;gCACP,UAAU;gCACV,UAAU;gCACV,MAAM;4BACR;4BAEA,QAAQ,GAAG,CAAC,wCAAwC;4BAEpD,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,iBAAiB,CAAC,EAAE;gCAC/D,QAAQ;gCACR,SAAS;oCACP,gBAAgB;gCAClB;gCACA,MAAM,KAAK,SAAS,CAAC;4BACvB;4BAEA,QAAQ,GAAG,CAAC,4CAA4C,SAAS,MAAM;4BACvE,QAAQ,GAAG,CAAC,6CAA6C,OAAO,WAAW,CAAC,SAAS,OAAO,CAAC,OAAO;4BAEpG,IAAI,SAAS,EAAE,EAAE;gCACf,IAAI;gCACJ,IAAI;oCACF,MAAM,eAAe,MAAM,SAAS,IAAI;oCACxC,QAAQ,GAAG,CAAC,qCAAqC;oCACjD,SAAS,KAAK,KAAK,CAAC;gCACtB,EAAE,OAAO,YAAY;oCACnB,QAAQ,KAAK,CAAC,gDAAgD;oCAC9D,OAAO;gCACT;gCAEA,QAAQ,GAAG,CAAC,oCAAoC;gCAEhD,IAAI,UAAU,OAAO,OAAO,EAAE;oCAC5B,QAAQ,GAAG,CAAC;oCAEZ,8DAA8D;oCAC9D,MAAM,cAAc,OAAO,IAAI;oCAE/B,MAAM,cAAc;wCAClB,IAAI,YAAY,EAAE,IAAI,YAAY,GAAG;wCACrC,UAAU,YAAY,QAAQ;wCAC9B,MAAM,YAAY,QAAQ;wCAC1B,OAAO,YAAY,KAAK;wCACxB,OAAO;wCACP,MAAM,YAAY,IAAI,IAAI;wCAC1B,YAAY,YAAY,UAAU;wCAClC,aAAa,YAAY,UAAU,IAAI;wCACvC,YAAY,YAAY,UAAU,IAAI;wCACtC,aAAa;wCACb,eAAe,KAAK,GAAG,GAAG,gBAAgB;oCAC5C;oCAEA,QAAQ,GAAG,CAAC,qDAAqD;oCAEjE,OAAO;gCACT,OAAO;oCACL,QAAQ,GAAG,CAAC,8BAA8B,QAAQ,WAAW;oCAC7D,OAAO;gCACT;4BACF,OAAO;gCACL,QAAQ,GAAG,CAAC,mDAAmD,SAAS,MAAM;gCAC9E,MAAM,YAAY,MAAM,SAAS,IAAI;gCACrC,QAAQ,GAAG,CAAC,qBAAqB;gCACjC,OAAO;4BACT;wBACF,EAAE,OAAO,OAAO;4BACd,QAAQ,KAAK,CAAC,0BAA0B;4BACxC,iEAAiE;4BACjE,OAAO;wBACT;oBACF,OAAO;wBACL,QAAQ,GAAG,CAAC,4CAA4C;4BACtD;4BACA,aAAa,CAAC,CAAC;4BACf;4BACA;wBACF;wBACA,OAAO;oBACT;gBACF;gBAEA,QAAQ,GAAG,CAAC;gBACZ,OAAO;YACT;QACF;QACA,CAAA,GAAA,mJAAA,CAAA,UAAc,AAAD,EAAE;YACb,UAAU,QAAQ,GAAG,CAAC,gBAAgB;YACtC,cAAc,QAAQ,GAAG,CAAC,oBAAoB;QAChD;KACD;IACD,SAAS;QACP,UAAU;QACV,QAAQ,KAAK,KAAK,KAAK,GAAG,UAAU;IACtC;IACA,OAAO;QACL,QAAQ;IACV;IACA,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE;YACzC,kDAAkD;YAClD,IAAI,MAAM;gBACR,+DAA+D;gBAC/D,MAAM,EAAE,GAAG,KAAK,EAAE;gBAClB,MAAM,IAAI,GAAG,KAAK,IAAI;gBACtB,MAAM,KAAK,GAAG,KAAK,KAAK;gBACxB,MAAM,IAAI,GAAG,KAAK,IAAI,IAAI;gBAC1B,MAAM,UAAU,GAAG,KAAK,UAAU;gBAClC,MAAM,WAAW,GAAG,KAAK,WAAW;gBACpC,MAAM,cAAc,GAAG,KAAK,cAAc,IAAI,KAAK,GAAG;gBAEtD,iDAAiD;gBACjD,6EAA6E;gBAC7E,MAAM,WAAW,GAAG,KAAK,WAAW,IAAI;gBACxC,MAAM,aAAa,GAAG,KAAK,WAAW,GAAG,KAAK,GAAG,KAAK;gBAEtD,gDAAgD;gBAChD,MAAM,UAAU,GAAG,KAAK,UAAU,IAAI;gBACtC,MAAM,OAAO,GAAG,KAAK,OAAO,IAAI;gBAChC,MAAM,mBAAmB,GAAG,KAAK,mBAAmB,IAAI;gBACxD,MAAM,gBAAgB,GAAG,KAAK,gBAAgB,IAAI;gBAClD,MAAM,gBAAgB,GAAG,KAAK,gBAAgB,IAAI;gBAClD,MAAM,yBAAyB,GAAG,KAAK,yBAAyB,IAAI;gBAEpE,QAAQ,GAAG,CAAC,wCAAwC,KAAK,KAAK,EAAE;oBAC9D,YAAY,MAAM,UAAU;oBAC5B,SAAS,MAAM,OAAO;oBACtB,qBAAqB,MAAM,mBAAmB;oBAC9C,kBAAkB,MAAM,gBAAgB;oBACxC,kBAAkB,MAAM,gBAAgB;oBACxC,2BAA2B,MAAM,yBAAyB;gBAC5D;YACF;YAEA,uDAAuD;YACvD,IAAI,YAAY,YAAY,SAAS,gBAAgB,WAAW;gBAC9D,MAAM,WAAW,GAAG,QAAQ,WAAW;gBACvC,IAAI,QAAQ,WAAW,EAAE;oBACvB,MAAM,aAAa,GAAG,KAAK,GAAG;gBAChC;gBACA,QAAQ,GAAG,CAAC,uDAAuD;oBACjE,aAAa,MAAM,WAAW;oBAC9B,eAAe,MAAM,aAAa;gBACpC;YACF;YAEA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,QAAQ,IAAI,EAAE;gBAChB,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,EAAE;gBAC1B,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;gBAC9B,QAAQ,IAAI,CAAC,KAAK,GAAG,MAAM,KAAK;gBAChC,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;gBAC9B,QAAQ,IAAI,CAAC,UAAU,GAAG,MAAM,UAAU;gBAC1C,QAAQ,IAAI,CAAC,WAAW,GAAG,MAAM,WAAW;gBAC5C,QAAQ,IAAI,CAAC,WAAW,GAAG,MAAM,WAAW;gBAC5C,QAAQ,IAAI,CAAC,aAAa,GAAG,MAAM,aAAa;gBAChD,QAAQ,IAAI,CAAC,cAAc,GAAG,MAAM,cAAc;gBAElD,0DAA0D;gBAC1D,QAAQ,IAAI,CAAC,UAAU,GAAG,MAAM,UAAU;gBAC1C,QAAQ,IAAI,CAAC,OAAO,GAAG,MAAM,OAAO;gBACpC,QAAQ,IAAI,CAAC,mBAAmB,GAAG,MAAM,mBAAmB;gBAC5D,QAAQ,IAAI,CAAC,gBAAgB,GAAG,MAAM,gBAAgB;gBACtD,QAAQ,IAAI,CAAC,gBAAgB,GAAG,MAAM,gBAAgB;gBACtD,QAAQ,IAAI,CAAC,yBAAyB,GAAG,MAAM,yBAAyB;gBAExE,QAAQ,GAAG,CAAC,kCAAkC;oBAC5C,OAAO,QAAQ,IAAI,CAAC,KAAK;oBACzB,YAAY,QAAQ,IAAI,CAAC,UAAU;oBACnC,SAAS,QAAQ,IAAI,CAAC,OAAO;oBAC7B,qBAAqB,QAAQ,IAAI,CAAC,mBAAmB;oBACrD,kBAAkB,QAAQ,IAAI,CAAC,gBAAgB;gBACjD;YACF;YAEA,OAAO;QACT;IACF;AACF", "debugId": null}}, {"offset": {"line": 7154, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/app/server/actions.js"], "sourcesContent": ["/**\r\n * ! The server actions below are used to fetch the static data from the fake-db. If you're using an ORM\r\n * ! (Object-Relational Mapping) or a database, you can swap the code below with your own database queries.\r\n */\r\n'use server'\r\n\r\n// Third-party Imports\r\nimport { getServerSession } from 'next-auth'\r\n\r\n// Data Imports\r\nimport { db as eCommerceData } from '@/fake-db/apps/ecommerce'\r\nimport { db as userData } from '@/fake-db/apps/userList'\r\nimport { db as permissionData } from '@/fake-db/apps/permissions'\r\nimport { db as profileData } from '@/fake-db/pages/userProfile'\r\n\r\n// Auth Imports\r\nimport { authOptions } from '@/libs/auth'\r\n\r\nexport const getEcommerceData = async () => {\r\n  return eCommerceData\r\n}\r\n\r\nexport const getUserData = async () => {\r\n  return userData\r\n}\r\n\r\nexport const getPermissionsData = async () => {\r\n  return permissionData\r\n}\r\n\r\nexport const getProfileData = async () => {\r\n  const session = await getServerSession(authOptions)\r\n\r\n  if (!session) {\r\n    throw new Error('Unauthorized: No session found');\r\n  }\r\n\r\n  const userProfileData = {\r\n    profileHeader: {\r\n      fullName: session.user.name || 'Admin User',\r\n      designation: 'Administrator',\r\n      designationIcon: 'tabler-crown',\r\n      location: 'Regina, SK, Canada',\r\n      joiningDate: 'Joined Today',\r\n      profileImg: '/images/avatars/1.png',\r\n      coverImg: '/images/pages/profile-banner.png'\r\n    },\r\n    users: {\r\n      profile: {\r\n        about: [\r\n          { property: 'Full Name', value: session.user.name || 'Admin User', icon: 'tabler-user' },\r\n          { property: 'Status', value: session.user.isVerified ? 'Verified' : 'Pending', icon: 'tabler-check' },\r\n          { property: 'Role', value: 'Administrator', icon: 'tabler-crown' },\r\n          { property: 'Country', value: 'Canada', icon: 'tabler-flag' },\r\n          { property: 'Language', value: 'English', icon: 'tabler-language' }\r\n        ],\r\n        contacts: [\r\n          { property: 'Contact', value: '+****************', icon: 'tabler-phone-call' },\r\n          { property: 'Email', value: session.user.email || '<EMAIL>', icon: 'tabler-mail' },\r\n          { property: 'Company', value: 'CAM Transport Ltd.', icon: 'tabler-building' }\r\n        ],\r\n        teams: [\r\n          { property: 'Transport Management', value: '(Admin Access)' },\r\n          { property: 'System Administration', value: '(Full Access)' }\r\n        ],\r\n        overview: [\r\n          { property: 'Task Compiled', value: '13.5k', icon: 'tabler-check' },\r\n          { property: 'Connections', value: '897', icon: 'tabler-users' },\r\n          { property: 'Projects Compiled', value: '146', icon: 'tabler-layout-grid' }\r\n        ],\r\n        connections: [\r\n          {\r\n            isFriend: false,\r\n            connections: '45',\r\n            name: 'Cecilia Payne',\r\n            avatar: '/images/avatars/2.png'\r\n          }\r\n        ],\r\n        teamsTech: [\r\n          {\r\n            members: 72,\r\n            ChipColor: 'error',\r\n            chipText: 'Developer',\r\n            title: 'React Developers',\r\n            avatar: '/images/icons/project-icons/react-label.png'\r\n          }\r\n        ],\r\n        projectTable: [\r\n          {\r\n            id: 1,\r\n            title: 'CAM Transport System',\r\n            subtitle: 'Transport Management',\r\n            leader: 'Admin',\r\n            avatar: '/images/avatars/1.png',\r\n            avatarGroup: ['/images/avatars/1.png'],\r\n            status: 'Active',\r\n            chipColor: 'success'\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  }\r\n\r\n  return userProfileData\r\n}\r\n\r\nexport const checkUsersExistInDB = async () => {\r\n  // We are checking user data from the fake-db.\r\n  // If you are using a real database, you would query it here.\r\n  const users = userData || []\r\n\r\n  return users.length > 0\r\n}\r\n\r\nexport const fetchUrgentInquiriesFromServer = async () => {\r\n  const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8090';\r\n  try {\r\n    const response = await fetch(`${API_BASE_URL}/urgent/get-urgents`, {\r\n      method: 'GET',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n      },\r\n      cache: 'no-store'\r\n    });\r\n\r\n    if (!response.ok) {\r\n      const errorText = await response.text();\r\n      console.error('❌ Backend API returned an error:', {\r\n        status: response.status,\r\n        statusText: response.statusText,\r\n        url: response.url,\r\n        responseBody: errorText,\r\n      });\r\n      throw new Error(`Failed to fetch urgent inquiries: Backend responded with status ${response.status}`);\r\n    }\r\n\r\n    const responseText = await response.text();\r\n    try {\r\n      return JSON.parse(responseText);\r\n    } catch (e) {\r\n      console.error('❌ Failed to parse JSON response from backend. Raw response:', responseText);\r\n      throw new Error('Backend did not return valid JSON.');\r\n    }\r\n  } catch (error) {\r\n    console.error('❌ Error fetching urgent inquiries from server:', error);\r\n    throw error;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;AAGD,sBAAsB;AACtB;AAEA,eAAe;AACf;AACA;AACA;AACA;AAEA,eAAe;AACf;;;;;;;;;;AAEO,MAAM,mBAAmB;IAC9B,OAAO,sIAAA,CAAA,KAAa;AACtB;AAEO,MAAM,cAAc;IACzB,OAAO,qIAAA,CAAA,KAAQ;AACjB;AAEO,MAAM,qBAAqB;IAChC,OAAO,wIAAA,CAAA,KAAc;AACvB;AAEO,MAAM,iBAAiB;IAC5B,MAAM,UAAU,MAAM,CAAA,GAAA,qIAAA,CAAA,mBAAgB,AAAD,EAAE,mHAAA,CAAA,cAAW;IAElD,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,kBAAkB;QACtB,eAAe;YACb,UAAU,QAAQ,IAAI,CAAC,IAAI,IAAI;YAC/B,aAAa;YACb,iBAAiB;YACjB,UAAU;YACV,aAAa;YACb,YAAY;YACZ,UAAU;QACZ;QACA,OAAO;YACL,SAAS;gBACP,OAAO;oBACL;wBAAE,UAAU;wBAAa,OAAO,QAAQ,IAAI,CAAC,IAAI,IAAI;wBAAc,MAAM;oBAAc;oBACvF;wBAAE,UAAU;wBAAU,OAAO,QAAQ,IAAI,CAAC,UAAU,GAAG,aAAa;wBAAW,MAAM;oBAAe;oBACpG;wBAAE,UAAU;wBAAQ,OAAO;wBAAiB,MAAM;oBAAe;oBACjE;wBAAE,UAAU;wBAAW,OAAO;wBAAU,MAAM;oBAAc;oBAC5D;wBAAE,UAAU;wBAAY,OAAO;wBAAW,MAAM;oBAAkB;iBACnE;gBACD,UAAU;oBACR;wBAAE,UAAU;wBAAW,OAAO;wBAAqB,MAAM;oBAAoB;oBAC7E;wBAAE,UAAU;wBAAS,OAAO,QAAQ,IAAI,CAAC,KAAK,IAAI;wBAAyB,MAAM;oBAAc;oBAC/F;wBAAE,UAAU;wBAAW,OAAO;wBAAsB,MAAM;oBAAkB;iBAC7E;gBACD,OAAO;oBACL;wBAAE,UAAU;wBAAwB,OAAO;oBAAiB;oBAC5D;wBAAE,UAAU;wBAAyB,OAAO;oBAAgB;iBAC7D;gBACD,UAAU;oBACR;wBAAE,UAAU;wBAAiB,OAAO;wBAAS,MAAM;oBAAe;oBAClE;wBAAE,UAAU;wBAAe,OAAO;wBAAO,MAAM;oBAAe;oBAC9D;wBAAE,UAAU;wBAAqB,OAAO;wBAAO,MAAM;oBAAqB;iBAC3E;gBACD,aAAa;oBACX;wBACE,UAAU;wBACV,aAAa;wBACb,MAAM;wBACN,QAAQ;oBACV;iBACD;gBACD,WAAW;oBACT;wBACE,SAAS;wBACT,WAAW;wBACX,UAAU;wBACV,OAAO;wBACP,QAAQ;oBACV;iBACD;gBACD,cAAc;oBACZ;wBACE,IAAI;wBACJ,OAAO;wBACP,UAAU;wBACV,QAAQ;wBACR,QAAQ;wBACR,aAAa;4BAAC;yBAAwB;wBACtC,QAAQ;wBACR,WAAW;oBACb;iBACD;YACH;QACF;IACF;IAEA,OAAO;AACT;AAEO,MAAM,sBAAsB;IACjC,8CAA8C;IAC9C,6DAA6D;IAC7D,MAAM,QAAQ,qIAAA,CAAA,KAAQ,IAAI,EAAE;IAE5B,OAAO,MAAM,MAAM,GAAG;AACxB;AAEO,MAAM,iCAAiC;IAC5C,MAAM,eAAe,0HAA2E;IAChG,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,mBAAmB,CAAC,EAAE;YACjE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,OAAO;QACT;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,QAAQ,KAAK,CAAC,oCAAoC;gBAChD,QAAQ,SAAS,MAAM;gBACvB,YAAY,SAAS,UAAU;gBAC/B,KAAK,SAAS,GAAG;gBACjB,cAAc;YAChB;YACA,MAAM,IAAI,MAAM,CAAC,gEAAgE,EAAE,SAAS,MAAM,EAAE;QACtG;QAEA,MAAM,eAAe,MAAM,SAAS,IAAI;QACxC,IAAI;YACF,OAAO,KAAK,KAAK,CAAC;QACpB,EAAE,OAAO,GAAG;YACV,QAAQ,KAAK,CAAC,+DAA+D;YAC7E,MAAM,IAAI,MAAM;QAClB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kDAAkD;QAChE,MAAM;IACR;AACF;;;IAjIa;IAIA;IAIA;IAIA;IA4EA;IAQA;;AAhGA,+OAAA;AAIA,+OAAA;AAIA,+OAAA;AAIA,+OAAA;AA4EA,+OAAA;AAQA,+OAAA", "debugId": null}}, {"offset": {"line": 7377, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/.next-internal/server/app/%5Blang%5D/pages/user-profile/page/actions.js%20%28server%20actions%20loader%29"], "sourcesContent": ["export {getProfileData as '7f1c457e0c0705539cd3bb2fecd1846801a604019a'} from 'ACTIONS_MODULE0'\nexport {getUserData as '7f27096b32fcebfe43c05866c28f62dafa12f2af15'} from 'ACTIONS_MODULE0'\nexport {fetchUrgentInquiriesFromServer as '7f8f15f4893a0c6661561b55de5e700090a567f747'} from 'ACTIONS_MODULE0'\nexport {checkUsersExistInDB as '7fa86eb7a919b6e5f5c31f51c0435a015785819d8a'} from 'ACTIONS_MODULE0'\nexport {getEcommerceData as '7fcb01234d46807f3b15bc50ada2dffdbd2b77f83d'} from 'ACTIONS_MODULE0'\nexport {getPermissionsData as '7feff801cb850da8076429e1cb16b5d49fc5f9e303'} from 'ACTIONS_MODULE0'\n"], "names": [], "mappings": ";AAAA", "debugId": null}}, {"offset": {"line": 7444, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/%40layouts/LayoutWrapper.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/@layouts/LayoutWrapper.jsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/@layouts/LayoutWrapper.jsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAkS,GAC/T,gEACA", "debugId": null}}, {"offset": {"line": 7458, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/%40layouts/LayoutWrapper.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/@layouts/LayoutWrapper.jsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/@layouts/LayoutWrapper.jsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA8Q,GAC3S,4CACA", "debugId": null}}, {"offset": {"line": 7472, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 7482, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/%40layouts/components/vertical/LayoutContent.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/@layouts/components/vertical/LayoutContent.jsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/@layouts/components/vertical/LayoutContent.jsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAsT,GACnV,oFACA", "debugId": null}}, {"offset": {"line": 7496, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/%40layouts/components/vertical/LayoutContent.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/@layouts/components/vertical/LayoutContent.jsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/@layouts/components/vertical/LayoutContent.jsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAkS,GAC/T,gEACA", "debugId": null}}, {"offset": {"line": 7510, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 7520, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/%40layouts/utils/layoutClasses.js"], "sourcesContent": ["// Classes for vertical layout\r\nexport const verticalLayoutClasses = {\r\n  root: 'ts-vertical-layout',\r\n  contentWrapper: 'ts-vertical-layout-content-wrapper',\r\n  header: 'ts-vertical-layout-header',\r\n  headerFixed: 'ts-vertical-layout-header-fixed',\r\n  headerStatic: 'ts-vertical-layout-header-static',\r\n  headerFloating: 'ts-vertical-layout-header-floating',\r\n  headerDetached: 'ts-vertical-layout-header-detached',\r\n  headerAttached: 'ts-vertical-layout-header-attached',\r\n  headerContentCompact: 'ts-vertical-layout-header-content-compact',\r\n  headerContentWide: 'ts-vertical-layout-header-content-wide',\r\n  headerBlur: 'ts-vertical-layout-header-blur',\r\n  navbar: 'ts-vertical-layout-navbar',\r\n  navbarContent: 'ts-vertical-layout-navbar-content',\r\n  content: 'ts-vertical-layout-content',\r\n  contentCompact: 'ts-vertical-layout-content-compact',\r\n  contentWide: 'ts-vertical-layout-content-wide',\r\n  footer: 'ts-vertical-layout-footer',\r\n  footerStatic: 'ts-vertical-layout-footer-static',\r\n  footerFixed: 'ts-vertical-layout-footer-fixed',\r\n  footerDetached: 'ts-vertical-layout-footer-detached',\r\n  footerAttached: 'ts-vertical-layout-footer-attached',\r\n  footerContentWrapper: 'ts-vertical-layout-footer-content-wrapper',\r\n  footerContent: 'ts-vertical-layout-footer-content',\r\n  footerContentCompact: 'ts-vertical-layout-footer-content-compact',\r\n  footerContentWide: 'ts-vertical-layout-footer-content-wide'\r\n}\r\n\r\n// Classes for horizontal layout\r\nexport const horizontalLayoutClasses = {\r\n  root: 'ts-horizontal-layout',\r\n  contentWrapper: 'ts-horizontal-layout-content-wrapper',\r\n  header: 'ts-horizontal-layout-header',\r\n  headerFixed: 'ts-horizontal-layout-header-fixed',\r\n  headerStatic: 'ts-horizontal-layout-header-static',\r\n  headerContentCompact: 'ts-horizontal-layout-header-content-compact',\r\n  headerContentWide: 'ts-horizontal-layout-header-content-wide',\r\n  headerBlur: 'ts-horizontal-layout-header-blur',\r\n  navbar: 'ts-horizontal-layout-navbar',\r\n  navbarContent: 'ts-horizontal-layout-navbar-content',\r\n  navigation: 'ts-horizontal-layout-navigation',\r\n  navigationContentWrapper: 'ts-horizontal-layout-navigation-content-wrapper',\r\n  content: 'ts-horizontal-layout-content',\r\n  contentCompact: 'ts-horizontal-layout-content-compact',\r\n  contentWide: 'ts-horizontal-layout-content-wide',\r\n  footer: 'ts-horizontal-layout-footer',\r\n  footerStatic: 'ts-horizontal-layout-footer-static',\r\n  footerFixed: 'ts-horizontal-layout-footer-fixed',\r\n  footerContentWrapper: 'ts-horizontal-layout-footer-content-wrapper',\r\n  footerContent: 'ts-horizontal-layout-footer-content',\r\n  footerContentCompact: 'ts-horizontal-layout-footer-content-compact',\r\n  footerContentWide: 'ts-horizontal-layout-footer-content-wide'\r\n}\r\n\r\n// Classes for blank layout\r\nexport const blankLayoutClasses = {\r\n  root: 'ts-blank-layout'\r\n}\r\n\r\n// Classes for front layout\r\nexport const frontLayoutClasses = {\r\n  root: 'ts-front-layout-root',\r\n  header: 'ts-front-layout-header',\r\n  navbar: 'ts-front-layout-navbar',\r\n  navbarContent: 'ts-front-layout-navbar-content',\r\n  footer: 'ts-front-layout-footer'\r\n}\r\n\r\n// Common classes for Vertical and Horizontal layouts\r\nexport const commonLayoutClasses = {\r\n  contentHeightFixed: 'ts-layout-content-height-fixed'\r\n}\r\n"], "names": [], "mappings": "AAAA,8BAA8B;;;;;;;;AACvB,MAAM,wBAAwB;IACnC,MAAM;IACN,gBAAgB;IAChB,QAAQ;IACR,aAAa;IACb,cAAc;IACd,gBAAgB;IAChB,gBAAgB;IAChB,gBAAgB;IAChB,sBAAsB;IACtB,mBAAmB;IACnB,YAAY;IACZ,QAAQ;IACR,eAAe;IACf,SAAS;IACT,gBAAgB;IAChB,aAAa;IACb,QAAQ;IACR,cAAc;IACd,aAAa;IACb,gBAAgB;IAChB,gBAAgB;IAChB,sBAAsB;IACtB,eAAe;IACf,sBAAsB;IACtB,mBAAmB;AACrB;AAGO,MAAM,0BAA0B;IACrC,MAAM;IACN,gBAAgB;IAChB,QAAQ;IACR,aAAa;IACb,cAAc;IACd,sBAAsB;IACtB,mBAAmB;IACnB,YAAY;IACZ,QAAQ;IACR,eAAe;IACf,YAAY;IACZ,0BAA0B;IAC1B,SAAS;IACT,gBAAgB;IAChB,aAAa;IACb,QAAQ;IACR,cAAc;IACd,aAAa;IACb,sBAAsB;IACtB,eAAe;IACf,sBAAsB;IACtB,mBAAmB;AACrB;AAGO,MAAM,qBAAqB;IAChC,MAAM;AACR;AAGO,MAAM,qBAAqB;IAChC,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,eAAe;IACf,QAAQ;AACV;AAGO,MAAM,sBAAsB;IACjC,oBAAoB;AACtB", "debugId": null}}, {"offset": {"line": 7598, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/%40layouts/styles/vertical/StyledContentWrapper.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/@layouts/styles/vertical/StyledContentWrapper.jsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/@layouts/styles/vertical/StyledContentWrapper.jsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAyT,GACtV,uFACA", "debugId": null}}, {"offset": {"line": 7612, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/%40layouts/styles/vertical/StyledContentWrapper.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/@layouts/styles/vertical/StyledContentWrapper.jsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/@layouts/styles/vertical/StyledContentWrapper.jsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAqS,GAClU,mEACA", "debugId": null}}, {"offset": {"line": 7626, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 7636, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/%40layouts/VerticalLayout.jsx"], "sourcesContent": ["// Third-party Imports\r\nimport classnames from 'classnames'\r\n\r\n// Component Imports\r\nimport LayoutContent from './components/vertical/LayoutContent'\r\n\r\n// Util Imports\r\nimport { verticalLayoutClasses } from './utils/layoutClasses'\r\n\r\n// Styled Component Imports\r\nimport StyledContentWrapper from './styles/vertical/StyledContentWrapper'\r\n\r\nconst VerticalLayout = props => {\r\n  // Props\r\n  const { navbar, footer, navigation, children } = props\r\n\r\n  return (\r\n    <div className={classnames(verticalLayoutClasses.root, 'flex flex-auto')}>\r\n      {navigation || null}\r\n      <StyledContentWrapper\r\n        className={classnames(verticalLayoutClasses.contentWrapper, 'flex flex-col min-is-0 is-full')}\r\n      >\r\n        {navbar || null}\r\n        {/* Content */}\r\n        <LayoutContent>{children}</LayoutContent>\r\n        {footer || null}\r\n      </StyledContentWrapper>\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default VerticalLayout\r\n"], "names": [], "mappings": "AAAA,sBAAsB;;;;;AACtB;AAEA,oBAAoB;AACpB;AAEA,eAAe;AACf;AAEA,2BAA2B;AAC3B;;;;;;AAEA,MAAM,iBAAiB,CAAA;IACrB,QAAQ;IACR,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,GAAG;IAEjD,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mIAAA,CAAA,UAAU,AAAD,EAAE,0IAAA,CAAA,wBAAqB,CAAC,IAAI,EAAE;;YACpD,cAAc;0BACf,8OAAC,+JAAA,CAAA,UAAoB;gBACnB,WAAW,CAAA,GAAA,mIAAA,CAAA,UAAU,AAAD,EAAE,0IAAA,CAAA,wBAAqB,CAAC,cAAc,EAAE;;oBAE3D,UAAU;kCAEX,8OAAC,4JAAA,CAAA,UAAa;kCAAE;;;;;;oBACf,UAAU;;;;;;;;;;;;;AAInB;uCAEe", "debugId": null}}, {"offset": {"line": 7692, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/%40menu/contexts/horizontalNavContext.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const HorizontalNavProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call HorizontalNavProvider() from the server but HorizontalNavProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/@menu/contexts/horizontalNavContext.jsx <module evaluation>\",\n    \"HorizontalNavProvider\",\n);\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/@menu/contexts/horizontalNavContext.jsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/@menu/contexts/horizontalNavContext.jsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,wBAAwB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACvD;IAAa,MAAM,IAAI,MAAM;AAA0P,GACvR,6EACA;uCAEW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA+S,GAC5U,6EACA", "debugId": null}}, {"offset": {"line": 7710, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/%40menu/contexts/horizontalNavContext.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const HorizontalNavProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call HorizontalNavProvider() from the server but HorizontalNavProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/@menu/contexts/horizontalNavContext.jsx\",\n    \"HorizontalNavProvider\",\n);\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/@menu/contexts/horizontalNavContext.jsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/@menu/contexts/horizontalNavContext.jsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,wBAAwB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACvD;IAAa,MAAM,IAAI,MAAM;AAA0P,GACvR,yDACA;uCAEW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA2R,GACxT,yDACA", "debugId": null}}, {"offset": {"line": 7728, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 7738, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/%40layouts/components/horizontal/LayoutContent.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/@layouts/components/horizontal/LayoutContent.jsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/@layouts/components/horizontal/LayoutContent.jsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAwT,GACrV,sFACA", "debugId": null}}, {"offset": {"line": 7752, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/%40layouts/components/horizontal/LayoutContent.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/@layouts/components/horizontal/LayoutContent.jsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/@layouts/components/horizontal/LayoutContent.jsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAoS,GACjU,kEACA", "debugId": null}}, {"offset": {"line": 7766, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 7776, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/%40layouts/styles/horizontal/StyledContentWrapper.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/@layouts/styles/horizontal/StyledContentWrapper.jsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/@layouts/styles/horizontal/StyledContentWrapper.jsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA2T,GACxV,yFACA", "debugId": null}}, {"offset": {"line": 7790, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/%40layouts/styles/horizontal/StyledContentWrapper.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/@layouts/styles/horizontal/StyledContentWrapper.jsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/@layouts/styles/horizontal/StyledContentWrapper.jsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAuS,GACpU,qEACA", "debugId": null}}, {"offset": {"line": 7804, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 7814, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/%40layouts/HorizontalLayout.jsx"], "sourcesContent": ["// Third-party Imports\r\nimport classnames from 'classnames'\r\n\r\n// Context Imports\r\nimport { HorizontalNavProvider } from '@menu/contexts/horizontalNavContext'\r\n\r\n// Component Imports\r\nimport LayoutContent from './components/horizontal/LayoutContent'\r\n\r\n// Util Imports\r\nimport { horizontalLayoutClasses } from './utils/layoutClasses'\r\n\r\n// Styled Component Imports\r\nimport StyledContentWrapper from './styles/horizontal/StyledContentWrapper'\r\n\r\nconst HorizontalLayout = props => {\r\n  // Props\r\n  const { header, footer, children } = props\r\n\r\n  return (\r\n    <div className={classnames(horizontalLayoutClasses.root, 'flex flex-auto')}>\r\n      <HorizontalNavProvider>\r\n        <StyledContentWrapper className={classnames(horizontalLayoutClasses.contentWrapper, 'flex flex-col is-full')}>\r\n          {header || null}\r\n          <LayoutContent>{children}</LayoutContent>\r\n          {footer || null}\r\n        </StyledContentWrapper>\r\n      </HorizontalNavProvider>\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default HorizontalLayout\r\n"], "names": [], "mappings": "AAAA,sBAAsB;;;;;AACtB;AAEA,kBAAkB;AAClB;AAEA,oBAAoB;AACpB;AAEA,eAAe;AACf;AAEA,2BAA2B;AAC3B;;;;;;;AAEA,MAAM,mBAAmB,CAAA;IACvB,QAAQ;IACR,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG;IAErC,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mIAAA,CAAA,UAAU,AAAD,EAAE,0IAAA,CAAA,0BAAuB,CAAC,IAAI,EAAE;kBACvD,cAAA,8OAAC,kJAAA,CAAA,wBAAqB;sBACpB,cAAA,8OAAC,iKAAA,CAAA,UAAoB;gBAAC,WAAW,CAAA,GAAA,mIAAA,CAAA,UAAU,AAAD,EAAE,0IAAA,CAAA,0BAAuB,CAAC,cAAc,EAAE;;oBACjF,UAAU;kCACX,8OAAC,8JAAA,CAAA,UAAa;kCAAE;;;;;;oBACf,UAAU;;;;;;;;;;;;;;;;;AAKrB;uCAEe", "debugId": null}}, {"offset": {"line": 7876, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/views/pages/user-profile/index.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/views/pages/user-profile/index.jsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/views/pages/user-profile/index.jsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA0S,GACvU,wEACA", "debugId": null}}, {"offset": {"line": 7890, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/views/pages/user-profile/index.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/views/pages/user-profile/index.jsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/views/pages/user-profile/index.jsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAsR,GACnT,oDACA", "debugId": null}}, {"offset": {"line": 7904, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 7914, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/contexts/nextAuthProvider.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const NextAuthProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call NextAuthProvider() from the server but NextAuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/nextAuthProvider.jsx <module evaluation>\",\n    \"NextAuthProvider\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,mBAAmB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,mEACA", "debugId": null}}, {"offset": {"line": 7928, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/contexts/nextAuthProvider.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const NextAuthProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call NextAuthProvider() from the server but NextAuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/nextAuthProvider.jsx\",\n    \"NextAuthProvider\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,mBAAmB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,+CACA", "debugId": null}}, {"offset": {"line": 7942, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 7952, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/%40menu/contexts/verticalNavContext.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const VerticalNavProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call VerticalNavProvider() from the server but VerticalNavProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/@menu/contexts/verticalNavContext.jsx <module evaluation>\",\n    \"VerticalNavProvider\",\n);\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/@menu/contexts/verticalNavContext.jsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/@menu/contexts/verticalNavContext.jsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,sBAAsB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACrD;IAAa,MAAM,IAAI,MAAM;AAAsP,GACnR,2EACA;uCAEW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA6S,GAC1U,2EACA", "debugId": null}}, {"offset": {"line": 7970, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/%40menu/contexts/verticalNavContext.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const VerticalNavProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call VerticalNavProvider() from the server but VerticalNavProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/@menu/contexts/verticalNavContext.jsx\",\n    \"VerticalNavProvider\",\n);\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/@menu/contexts/verticalNavContext.jsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/@menu/contexts/verticalNavContext.jsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,sBAAsB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACrD;IAAa,MAAM,IAAI,MAAM;AAAsP,GACnR,uDACA;uCAEW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAyR,GACtT,uDACA", "debugId": null}}, {"offset": {"line": 7988, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 7998, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/%40core/contexts/settingsContext.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const SettingsContext = registerClientReference(\n    function() { throw new Error(\"Attempted to call SettingsContext() from the server but SettingsContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/@core/contexts/settingsContext.jsx <module evaluation>\",\n    \"SettingsContext\",\n);\nexport const SettingsProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call SettingsProvider() from the server but SettingsProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/@core/contexts/settingsContext.jsx <module evaluation>\",\n    \"SettingsProvider\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,kBAAkB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,wEACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,wEACA", "debugId": null}}, {"offset": {"line": 8016, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/%40core/contexts/settingsContext.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const SettingsContext = registerClientReference(\n    function() { throw new Error(\"Attempted to call SettingsContext() from the server but SettingsContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/@core/contexts/settingsContext.jsx\",\n    \"SettingsContext\",\n);\nexport const SettingsProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call SettingsProvider() from the server but SettingsProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/@core/contexts/settingsContext.jsx\",\n    \"SettingsProvider\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,kBAAkB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,oDACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,oDACA", "debugId": null}}, {"offset": {"line": 8034, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 8044, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/contexts/ProfileImageContext.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ProfileImageProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call ProfileImageProvider() from the server but ProfileImageProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/ProfileImageContext.jsx <module evaluation>\",\n    \"ProfileImageProvider\",\n);\nexport const useProfileImage = registerClientReference(\n    function() { throw new Error(\"Attempted to call useProfileImage() from the server but useProfileImage is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/ProfileImageContext.jsx <module evaluation>\",\n    \"useProfileImage\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,uBAAuB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACtD;IAAa,MAAM,IAAI,MAAM;AAAwP,GACrR,sEACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,sEACA", "debugId": null}}, {"offset": {"line": 8062, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/contexts/ProfileImageContext.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ProfileImageProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call ProfileImageProvider() from the server but ProfileImageProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/ProfileImageContext.jsx\",\n    \"ProfileImageProvider\",\n);\nexport const useProfileImage = registerClientReference(\n    function() { throw new Error(\"Attempted to call useProfileImage() from the server but useProfileImage is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/ProfileImageContext.jsx\",\n    \"useProfileImage\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,uBAAuB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACtD;IAAa,MAAM,IAAI,MAAM;AAAwP,GACrR,kDACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,kDACA", "debugId": null}}, {"offset": {"line": 8080, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 8090, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/components/theme/index.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/theme/index.jsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/theme/index.jsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAkS,GAC/T,gEACA", "debugId": null}}, {"offset": {"line": 8104, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/components/theme/index.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/theme/index.jsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/theme/index.jsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA8Q,GAC3S,4CACA", "debugId": null}}, {"offset": {"line": 8118, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 8128, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/redux-store/ReduxProvider.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/redux-store/ReduxProvider.jsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/redux-store/ReduxProvider.jsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAqS,GAClU,mEACA", "debugId": null}}, {"offset": {"line": 8142, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/redux-store/ReduxProvider.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/redux-store/ReduxProvider.jsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/redux-store/ReduxProvider.jsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAiR,GAC9S,+CACA", "debugId": null}}, {"offset": {"line": 8156, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 8166, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/libs/styles/AppReactToastify.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/libs/styles/AppReactToastify.jsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/libs/styles/AppReactToastify.jsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAwS,GACrU,sEACA", "debugId": null}}, {"offset": {"line": 8180, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/libs/styles/AppReactToastify.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/libs/styles/AppReactToastify.jsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/libs/styles/AppReactToastify.jsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAoR,GACjT,kDACA", "debugId": null}}, {"offset": {"line": 8194, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 8204, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/components/Providers.jsx"], "sourcesContent": ["// Context Imports\r\nimport { NextAuthProvider } from '@/contexts/nextAuthProvider'\r\nimport { VerticalNavProvider } from '@menu/contexts/verticalNavContext'\r\nimport { SettingsProvider } from '@core/contexts/settingsContext'\r\nimport { ProfileImageProvider } from '@/contexts/ProfileImageContext'\r\nimport ThemeProvider from '@components/theme'\r\nimport ReduxProvider from '@/redux-store/ReduxProvider'\r\n\r\n// Styled Component Imports\r\nimport AppReactToastify from '@/libs/styles/AppReactToastify'\r\n\r\n// Util Imports\r\nimport { getMode, getSettingsFromCookie, getSystemMode } from '@core/utils/serverHelpers'\r\n\r\nconst Providers = async props => {\r\n  // Props\r\n  const { children, direction } = props\r\n\r\n  // Vars\r\n  const mode = await getMode()\r\n  const settingsCookie = await getSettingsFromCookie()\r\n  const systemMode = await getSystemMode()\r\n\r\n  return (\r\n    <NextAuthProvider basePath={process.env.NEXTAUTH_BASEPATH}>\r\n      <ProfileImageProvider>\r\n        <VerticalNavProvider>\r\n          <SettingsProvider settingsCookie={settingsCookie} mode={mode}>\r\n            <ThemeProvider direction={direction} systemMode={systemMode}>\r\n              <ReduxProvider>{children}</ReduxProvider>\r\n              <AppReactToastify direction={direction} hideProgressBar />\r\n            </ThemeProvider>\r\n          </SettingsProvider>\r\n        </VerticalNavProvider>\r\n      </ProfileImageProvider>\r\n    </NextAuthProvider>\r\n  )\r\n}\r\n\r\nexport default Providers\r\n"], "names": [], "mappings": "AAAA,kBAAkB;;;;;AAClB;AACA;AACA;AACA;AACA;AACA;AAEA,2BAA2B;AAC3B;AAEA,eAAe;AACf;;;;;;;;;;AAEA,MAAM,YAAY,OAAM;IACtB,QAAQ;IACR,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG;IAEhC,OAAO;IACP,MAAM,OAAO,MAAM,CAAA,GAAA,uIAAA,CAAA,UAAO,AAAD;IACzB,MAAM,iBAAiB,MAAM,CAAA,GAAA,uIAAA,CAAA,wBAAqB,AAAD;IACjD,MAAM,aAAa,MAAM,CAAA,GAAA,uIAAA,CAAA,gBAAa,AAAD;IAErC,qBACE,8OAAC,oIAAA,CAAA,mBAAgB;QAAC,UAAU,QAAQ,GAAG,CAAC,iBAAiB;kBACvD,cAAA,8OAAC,uIAAA,CAAA,uBAAoB;sBACnB,cAAA,8OAAC,gJAAA,CAAA,sBAAmB;0BAClB,cAAA,8OAAC,6IAAA,CAAA,mBAAgB;oBAAC,gBAAgB;oBAAgB,MAAM;8BACtD,cAAA,8OAAC,oIAAA,CAAA,UAAa;wBAAC,WAAW;wBAAW,YAAY;;0CAC/C,8OAAC,uIAAA,CAAA,UAAa;0CAAE;;;;;;0CAChB,8OAAC,0IAAA,CAAA,UAAgB;gCAAC,WAAW;gCAAW,eAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOrE;uCAEe", "debugId": null}}, {"offset": {"line": 8295, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/components/layout/vertical/Navigation.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/vertical/Navigation.jsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/vertical/Navigation.jsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAiT,GAC9U,+EACA", "debugId": null}}, {"offset": {"line": 8309, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/components/layout/vertical/Navigation.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/vertical/Navigation.jsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/vertical/Navigation.jsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA6R,GAC1T,2DACA", "debugId": null}}, {"offset": {"line": 8323, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 8333, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/components/layout/horizontal/Header.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/horizontal/Header.jsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/horizontal/Header.jsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA+S,GAC5U,6EACA", "debugId": null}}, {"offset": {"line": 8347, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/components/layout/horizontal/Header.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/horizontal/Header.jsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/horizontal/Header.jsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA2R,GACxT,yDACA", "debugId": null}}, {"offset": {"line": 8361, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 8371, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/%40layouts/components/vertical/Navbar.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/@layouts/components/vertical/Navbar.jsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/@layouts/components/vertical/Navbar.jsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA+S,GAC5U,6EACA", "debugId": null}}, {"offset": {"line": 8385, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/%40layouts/components/vertical/Navbar.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/@layouts/components/vertical/Navbar.jsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/@layouts/components/vertical/Navbar.jsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA2R,GACxT,yDACA", "debugId": null}}, {"offset": {"line": 8399, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 8409, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/components/layout/vertical/NavToggle.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/vertical/NavToggle.jsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/vertical/NavToggle.jsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAgT,GAC7U,8EACA", "debugId": null}}, {"offset": {"line": 8423, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/components/layout/vertical/NavToggle.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/vertical/NavToggle.jsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/vertical/NavToggle.jsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA4R,GACzT,0DACA", "debugId": null}}, {"offset": {"line": 8437, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 8447, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/components/layout/shared/ModeDropdown.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/shared/ModeDropdown.jsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/shared/ModeDropdown.jsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAiT,GAC9U,+EACA", "debugId": null}}, {"offset": {"line": 8461, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/components/layout/shared/ModeDropdown.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/shared/ModeDropdown.jsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/shared/ModeDropdown.jsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA6R,GAC1T,2DACA", "debugId": null}}, {"offset": {"line": 8475, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 8485, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/components/layout/shared/UserDropdown.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/shared/UserDropdown.jsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/shared/UserDropdown.jsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAiT,GAC9U,+EACA", "debugId": null}}, {"offset": {"line": 8499, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/components/layout/shared/UserDropdown.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/shared/UserDropdown.jsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/shared/UserDropdown.jsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA6R,GAC1T,2DACA", "debugId": null}}, {"offset": {"line": 8513, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 8523, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/components/layout/vertical/NavbarContent.jsx"], "sourcesContent": ["// Third-party Imports\r\nimport classnames from 'classnames'\r\n\r\n// Component Imports\r\nimport NavToggle from './NavToggle'\r\nimport ModeDropdown from '@components/layout/shared/ModeDropdown'\r\nimport UserDropdown from '@components/layout/shared/UserDropdown'\r\n\r\n// Util Imports\r\nimport { verticalLayoutClasses } from '@layouts/utils/layoutClasses'\r\n\r\n// Vars\r\nconst shortcuts = [\r\n  {\r\n    url: '/apps/calendar',\r\n    icon: 'tabler-calendar',\r\n    title: 'Calendar',\r\n    subtitle: 'Appointments',\r\n  },\r\n  {\r\n    url: '/apps/invoice/list',\r\n    icon: 'tabler-file-dollar',\r\n    title: 'Invoice App',\r\n    subtitle: 'Manage Accounts',\r\n  },\r\n  {\r\n    url: '/apps/user/list',\r\n    icon: 'tabler-user',\r\n    title: 'Users',\r\n    subtitle: 'Manage Users',\r\n  },\r\n  {\r\n    url: '/',\r\n    icon: 'tabler-device-desktop-analytics',\r\n    title: 'Dashboard',\r\n    subtitle: 'User Dashboard',\r\n  },\r\n  {\r\n    url: '/pages/account-settings',\r\n    icon: 'tabler-settings',\r\n    title: 'Setting<PERSON>',\r\n    subtitle: 'Account Settings',\r\n  },\r\n]\r\n\r\nconst notifications = [\r\n  {\r\n    avatarImage: '/images/avatars/8.png',\r\n    title: 'Congratulations Flora 🎉',\r\n    subtitle: 'Won the monthly bestseller gold badge',\r\n    time: '1h ago',\r\n    read: false,\r\n  },\r\n  {\r\n    title: 'Cecilia Becker',\r\n    avatarColor: 'secondary',\r\n    subtitle: 'Accepted your connection',\r\n    time: '12h ago',\r\n    read: false,\r\n  },\r\n  {\r\n    avatarImage: '/images/avatars/3.png',\r\n    title: 'Bernard Woods',\r\n    subtitle: 'You have new message from Bernard Woods',\r\n    time: 'May 18, 8:26 AM',\r\n    read: true,\r\n  },\r\n  {\r\n    avatarIcon: 'tabler-chart-bar',\r\n    title: 'Monthly report generated',\r\n    subtitle: 'July month financial report is generated',\r\n    avatarColor: 'info',\r\n    time: 'Apr 24, 10:30 AM',\r\n    read: true,\r\n  },\r\n  {\r\n    avatarText: 'MG',\r\n    title: 'Application has been approved 🚀',\r\n    subtitle: 'Your Meta Gadgets project application has been approved.',\r\n    avatarColor: 'success',\r\n    time: 'Feb 17, 12:17 PM',\r\n    read: true,\r\n  },\r\n  {\r\n    avatarIcon: 'tabler-mail',\r\n    title: 'New message from Harry',\r\n    subtitle: 'You have new message from Harry',\r\n    avatarColor: 'error',\r\n    time: 'Jan 6, 1:48 PM',\r\n    read: true,\r\n  },\r\n]\r\n\r\nconst NavbarContent = () => {\r\n  return (\r\n    <div\r\n      className={classnames(\r\n        verticalLayoutClasses.navbarContent,\r\n        'flex items-center justify-between gap-4 is-full'\r\n      )}\r\n    >\r\n      <div className='flex items-center gap-4'>\r\n        <NavToggle />\r\n      </div>\r\n      <div className='flex items-center'>\r\n        <ModeDropdown />\r\n        <UserDropdown />\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default NavbarContent\r\n"], "names": [], "mappings": "AAAA,sBAAsB;;;;;AACtB;AAEA,oBAAoB;AACpB;AACA;AACA;AAEA,eAAe;AACf;;;;;;;AAEA,OAAO;AACP,MAAM,YAAY;IAChB;QACE,KAAK;QACL,MAAM;QACN,OAAO;QACP,UAAU;IACZ;IACA;QACE,KAAK;QACL,MAAM;QACN,OAAO;QACP,UAAU;IACZ;IACA;QACE,KAAK;QACL,MAAM;QACN,OAAO;QACP,UAAU;IACZ;IACA;QACE,KAAK;QACL,MAAM;QACN,OAAO;QACP,UAAU;IACZ;IACA;QACE,KAAK;QACL,MAAM;QACN,OAAO;QACP,UAAU;IACZ;CACD;AAED,MAAM,gBAAgB;IACpB;QACE,aAAa;QACb,OAAO;QACP,UAAU;QACV,MAAM;QACN,MAAM;IACR;IACA;QACE,OAAO;QACP,aAAa;QACb,UAAU;QACV,MAAM;QACN,MAAM;IACR;IACA;QACE,aAAa;QACb,OAAO;QACP,UAAU;QACV,MAAM;QACN,MAAM;IACR;IACA;QACE,YAAY;QACZ,OAAO;QACP,UAAU;QACV,aAAa;QACb,MAAM;QACN,MAAM;IACR;IACA;QACE,YAAY;QACZ,OAAO;QACP,UAAU;QACV,aAAa;QACb,MAAM;QACN,MAAM;IACR;IACA;QACE,YAAY;QACZ,OAAO;QACP,UAAU;QACV,aAAa;QACb,MAAM;QACN,MAAM;IACR;CACD;AAED,MAAM,gBAAgB;IACpB,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mIAAA,CAAA,UAAU,AAAD,EAClB,0IAAA,CAAA,wBAAqB,CAAC,aAAa,EACnC;;0BAGF,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,qJAAA,CAAA,UAAS;;;;;;;;;;0BAEZ,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,sJAAA,CAAA,UAAY;;;;;kCACb,8OAAC,sJAAA,CAAA,UAAY;;;;;;;;;;;;;;;;;AAIrB;uCAEe", "debugId": null}}, {"offset": {"line": 8670, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/components/layout/vertical/Navbar.jsx"], "sourcesContent": ["// Component Imports\r\nimport LayoutNavbar from '@layouts/components/vertical/Navbar'\r\nimport NavbarContent from './NavbarContent'\r\n\r\nconst Navbar = () => {\r\n  return (\r\n    <LayoutNavbar>\r\n      <NavbarContent />\r\n    </LayoutNavbar>\r\n  )\r\n}\r\n\r\nexport default Navbar\r\n"], "names": [], "mappings": "AAAA,oBAAoB;;;;;AACpB;AACA;;;;AAEA,MAAM,SAAS;IACb,qBACE,8OAAC,qJAAA,CAAA,UAAY;kBACX,cAAA,8OAAC,yJAAA,CAAA,UAAa;;;;;;;;;;AAGpB;uCAEe", "debugId": null}}, {"offset": {"line": 8700, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/%40layouts/components/vertical/Footer.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/@layouts/components/vertical/Footer.jsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/@layouts/components/vertical/Footer.jsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA+S,GAC5U,6EACA", "debugId": null}}, {"offset": {"line": 8714, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/%40layouts/components/vertical/Footer.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/@layouts/components/vertical/Footer.jsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/@layouts/components/vertical/Footer.jsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA2R,GACxT,yDACA", "debugId": null}}, {"offset": {"line": 8728, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 8738, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/components/layout/vertical/FooterContent.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/vertical/FooterContent.jsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/vertical/FooterContent.jsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAoT,GACjV,kFACA", "debugId": null}}, {"offset": {"line": 8752, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/components/layout/vertical/FooterContent.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/vertical/FooterContent.jsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/vertical/FooterContent.jsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAgS,GAC7T,8DACA", "debugId": null}}, {"offset": {"line": 8766, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 8776, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/components/layout/vertical/Footer.jsx"], "sourcesContent": ["// Component Imports\r\nimport LayoutFooter from '@layouts/components/vertical/Footer'\r\nimport FooterContent from './FooterContent'\r\n\r\nconst Footer = () => {\r\n  return (\r\n    <LayoutFooter>\r\n      <FooterContent />\r\n    </LayoutFooter>\r\n  )\r\n}\r\n\r\nexport default Footer\r\n"], "names": [], "mappings": "AAAA,oBAAoB;;;;;AACpB;AACA;;;;AAEA,MAAM,SAAS;IACb,qBACE,8OAAC,qJAAA,CAAA,UAAY;kBACX,cAAA,8OAAC,yJAAA,CAAA,UAAa;;;;;;;;;;AAGpB;uCAEe", "debugId": null}}, {"offset": {"line": 8806, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/%40layouts/components/horizontal/Footer.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/@layouts/components/horizontal/Footer.jsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/@layouts/components/horizontal/Footer.jsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAiT,GAC9U,+EACA", "debugId": null}}, {"offset": {"line": 8820, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/%40layouts/components/horizontal/Footer.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/@layouts/components/horizontal/Footer.jsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/@layouts/components/horizontal/Footer.jsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA6R,GAC1T,2DACA", "debugId": null}}, {"offset": {"line": 8834, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 8844, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/components/layout/horizontal/FooterContent.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/horizontal/FooterContent.jsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/horizontal/FooterContent.jsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAsT,GACnV,oFACA", "debugId": null}}, {"offset": {"line": 8858, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/components/layout/horizontal/FooterContent.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/horizontal/FooterContent.jsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/horizontal/FooterContent.jsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAkS,GAC/T,gEACA", "debugId": null}}, {"offset": {"line": 8872, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 8882, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/components/layout/horizontal/Footer.jsx"], "sourcesContent": ["// Component Imports\r\nimport LayoutFooter from '@layouts/components/horizontal/Footer'\r\nimport FooterContent from './FooterContent'\r\n\r\nconst Footer = () => {\r\n  return (\r\n    <LayoutFooter>\r\n      <FooterContent />\r\n    </LayoutFooter>\r\n  )\r\n}\r\n\r\nexport default Footer\r\n"], "names": [], "mappings": "AAAA,oBAAoB;;;;;AACpB;AACA;;;;AAEA,MAAM,SAAS;IACb,qBACE,8OAAC,uJAAA,CAAA,UAAY;kBACX,cAAA,8OAAC,2JAAA,CAAA,UAAa;;;;;;;;;;AAGpB;uCAEe", "debugId": null}}, {"offset": {"line": 8912, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/%40core/components/scroll-to-top/index.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/@core/components/scroll-to-top/index.jsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/@core/components/scroll-to-top/index.jsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAgT,GAC7U,8EACA", "debugId": null}}, {"offset": {"line": 8926, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/%40core/components/scroll-to-top/index.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/@core/components/scroll-to-top/index.jsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/@core/components/scroll-to-top/index.jsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA4R,GACzT,0DACA", "debugId": null}}, {"offset": {"line": 8940, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 8950, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/components/AuthRedirect.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/AuthRedirect.jsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/AuthRedirect.jsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAmS,GAChU,iEACA", "debugId": null}}, {"offset": {"line": 8964, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/components/AuthRedirect.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/AuthRedirect.jsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/AuthRedirect.jsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA+Q,GAC5S,6CACA", "debugId": null}}, {"offset": {"line": 8978, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 8988, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/utils/string.js"], "sourcesContent": ["export const ensurePrefix = (str, prefix) => (str.startsWith(prefix) ? str : `${prefix}${str}`)\r\nexport const withoutSuffix = (str, suffix) => (str.endsWith(suffix) ? str.slice(0, -suffix.length) : str)\r\nexport const withoutPrefix = (str, prefix) => (str.startsWith(prefix) ? str.slice(prefix.length) : str)\r\n"], "names": [], "mappings": ";;;;;AAAO,MAAM,eAAe,CAAC,KAAK,SAAY,IAAI,UAAU,CAAC,UAAU,MAAM,GAAG,SAAS,KAAK;AACvF,MAAM,gBAAgB,CAAC,KAAK,SAAY,IAAI,QAAQ,CAAC,UAAU,IAAI,KAAK,CAAC,GAAG,CAAC,OAAO,MAAM,IAAI;AAC9F,MAAM,gBAAgB,CAAC,KAAK,SAAY,IAAI,UAAU,CAAC,UAAU,IAAI,KAAK,CAAC,OAAO,MAAM,IAAI", "debugId": null}}, {"offset": {"line": 9002, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/utils/i18n.js"], "sourcesContent": ["// Config Imports\r\nimport { i18n } from '@configs/i18n'\r\n\r\n// Util Imports\r\nimport { ensurePrefix } from '@/utils/string'\r\n\r\n// Check if the url is missing the locale\r\nexport const isUrlMissingLocale = url => {\r\n  return i18n.locales.every(locale => !(url.startsWith(`/${locale}/`) || url === `/${locale}`))\r\n}\r\n\r\n// Get the localized url\r\nexport const getLocalizedUrl = (url, languageCode) => {\r\n  if (!url || !languageCode) throw new Error(\"URL or Language Code can't be empty\")\r\n\r\n  return isUrlMissingLocale(url) ? `/${languageCode}${ensurePrefix(url, '/')}` : url\r\n}\r\n"], "names": [], "mappings": "AAAA,iBAAiB;;;;;AACjB;AAEA,eAAe;AACf;;;AAGO,MAAM,qBAAqB,CAAA;IAChC,OAAO,sHAAA,CAAA,OAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA,SAAU,CAAC,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,KAAK,QAAQ,CAAC,CAAC,EAAE,QAAQ;AAC7F;AAGO,MAAM,kBAAkB,CAAC,KAAK;IACnC,IAAI,CAAC,OAAO,CAAC,cAAc,MAAM,IAAI,MAAM;IAE3C,OAAO,mBAAmB,OAAO,CAAC,CAAC,EAAE,eAAe,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE,KAAK,MAAM,GAAG;AACjF", "debugId": null}}, {"offset": {"line": 9025, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/hocs/AuthGuard.jsx"], "sourcesContent": ["// Third-party Imports\r\nimport { getServerSession } from 'next-auth'\r\nimport { redirect } from 'next/navigation'\r\n\r\n// Component Imports\r\nimport AuthRedirect from '@/components/AuthRedirect'\r\n\r\n// Util Imports\r\nimport { getLocalizedUrl } from '@/utils/i18n'\r\n\r\nexport default async function AuthGuard({ children, locale }) {\r\n  const session = await getServerSession()\r\n\r\n  // If no session, redirect to login\r\n  if (!session) {\r\n    return <AuthRedirect lang={locale} />\r\n  }\r\n\r\n  // User is logged in, show the protected content (regardless of verification status)\r\n  return <>{children}</>\r\n}\r\n\r\n\r\n"], "names": [], "mappings": "AAAA,sBAAsB;;;;;AACtB;AACA;AAEA,oBAAoB;AACpB;AAEA,eAAe;AACf;;;;;;AAEe,eAAe,UAAU,EAAE,QAAQ,EAAE,MAAM,EAAE;IAC1D,MAAM,UAAU,MAAM,CAAA,GAAA,qIAAA,CAAA,mBAAgB,AAAD;IAErC,mCAAmC;IACnC,IAAI,CAAC,SAAS;QACZ,qBAAO,8OAAC,kIAAA,CAAA,UAAY;YAAC,MAAM;;;;;;IAC7B;IAEA,oFAAoF;IACpF,qBAAO;kBAAG;;AACZ", "debugId": null}}, {"offset": {"line": 9064, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/components/ClientAuthGuard.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ClientAuthGuard.jsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ClientAuthGuard.jsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAsS,GACnU,oEACA", "debugId": null}}, {"offset": {"line": 9078, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/components/ClientAuthGuard.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ClientAuthGuard.jsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ClientAuthGuard.jsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAkR,GAC/S,gDACA", "debugId": null}}, {"offset": {"line": 9092, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 9102, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/utils/getDictionary.js"], "sourcesContent": ["// Third-party Imports\r\nimport 'server-only'\r\n\r\nconst dictionaries = {\r\n  en: () => import('@/data/dictionaries/en.json').then(module => module.default),\r\n  fr: () => import('@/data/dictionaries/fr.json').then(module => module.default),\r\n  ar: () => import('@/data/dictionaries/ar.json').then(module => module.default)\r\n}\r\n\r\nexport const getDictionary = async locale => dictionaries[locale]()\r\n"], "names": [], "mappings": "AAAA,sBAAsB;;;;AACtB;;AAEA,MAAM,eAAe;IACnB,IAAI,IAAM,iHAAsC,IAAI,CAAC,CAAA,SAAU,OAAO,OAAO;IAC7E,IAAI,IAAM,iHAAsC,IAAI,CAAC,CAAA,SAAU,OAAO,OAAO;IAC7E,IAAI,IAAM,iHAAsC,IAAI,CAAC,CAAA,SAAU,OAAO,OAAO;AAC/E;AAEO,MAAM,gBAAgB,OAAM,SAAU,YAAY,CAAC,OAAO", "debugId": null}}, {"offset": {"line": 9120, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/app/%5Blang%5D/pages/user-profile/page.jsx"], "sourcesContent": ["// Next Imports\r\nimport dynamic from 'next/dynamic'\r\n\r\n// MUI Imports\r\nimport Button from '@mui/material/Button'\r\n\r\n// Layout Imports\r\nimport LayoutWrapper from '@layouts/LayoutWrapper'\r\nimport VerticalLayout from '@layouts/VerticalLayout'\r\nimport HorizontalLayout from '@layouts/HorizontalLayout'\r\n\r\n// Component Imports\r\nimport UserProfile from '@views/pages/user-profile'\r\nimport Providers from '@components/Providers'\r\nimport Navigation from '@components/layout/vertical/Navigation'\r\nimport Header from '@components/layout/horizontal/Header'\r\nimport Navbar from '@components/layout/vertical/Navbar'\r\nimport VerticalFooter from '@components/layout/vertical/Footer'\r\nimport HorizontalFooter from '@components/layout/horizontal/Footer'\r\nimport ScrollToTop from '@core/components/scroll-to-top'\r\nimport AuthGuard from '@/hocs/AuthGuard'\r\nimport ClientAuthGuard from '@components/ClientAuthGuard'\r\n\r\n// Data Imports\r\nimport { getProfileData } from '@/app/server/actions'\r\n\r\n// Config Imports\r\nimport { i18n } from '@configs/i18n'\r\n\r\n// Util Imports\r\nimport { getDictionary } from '@/utils/getDictionary'\r\nimport { getMode, getSystemMode } from '@core/utils/serverHelpers'\r\n\r\nconst ProfileTab = dynamic(() => import('@views/pages/user-profile/profile'))\r\n\r\n// Vars\r\nconst tabContentList = data => ({\r\n    profile: <ProfileTab data={data?.users.profile} />\r\n})\r\n\r\nconst ProfilePage = async ({ params }) => {\r\n    // Vars\r\n    const data = await getProfileData()\r\n    const lang = params.lang\r\n    const direction = i18n.langDirection[lang]\r\n    const dictionary = await getDictionary(lang)\r\n    const mode = await getMode()\r\n    const systemMode = await getSystemMode()\r\n\r\n    return (\r\n        <Providers direction={direction}>\r\n            <AuthGuard locale={lang}>\r\n                <ClientAuthGuard>\r\n                    <LayoutWrapper\r\n                        systemMode={systemMode}\r\n                        verticalLayout={\r\n                            <VerticalLayout\r\n                                navigation={<Navigation dictionary={dictionary} mode={mode} />}\r\n                                navbar={<Navbar />}\r\n                                footer={<VerticalFooter />}\r\n                            >\r\n                                <UserProfile data={data} tabContentList={tabContentList(data)} />\r\n                            </VerticalLayout>\r\n                        }\r\n                        horizontalLayout={\r\n                            <HorizontalLayout header={<Header dictionary={dictionary} />} footer={<HorizontalFooter />}>\r\n                                <UserProfile data={data} tabContentList={tabContentList(data)} />\r\n                            </HorizontalLayout>\r\n                        }\r\n                    />\r\n                    <ScrollToTop className='mui-fixed'>\r\n                        <Button\r\n                            variant='contained'\r\n                            className='is-10 bs-10 rounded-full p-0 min-is-0 flex items-center justify-center'\r\n                        >\r\n                            <i className='tabler-arrow-up' />\r\n                        </Button>\r\n                    </ScrollToTop>\r\n                </ClientAuthGuard>\r\n            </AuthGuard>\r\n        </Providers>\r\n    )\r\n}\r\n\r\nexport default ProfilePage\r\n"], "names": [], "mappings": "AAAA,eAAe;;;;;AACf;AAEA,cAAc;AACd;AAEA,iBAAiB;AACjB;AACA;AACA;AAEA,oBAAoB;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA,eAAe;AACf;AAEA,iBAAiB;AACjB;AAEA,eAAe;AACf;AACA;;;;;;;;;;;;;;;;;;;;;;AAEA,MAAM,aAAa,CAAA,GAAA,+JAAA,CAAA,UAAO,AAAD,EAAE;;;;;;;AAE3B,OAAO;AACP,MAAM,iBAAiB,CAAA,OAAQ,CAAC;QAC5B,uBAAS,8OAAC;YAAW,MAAM,MAAM,MAAM;;;;;;IAC3C,CAAC;AAED,MAAM,cAAc,OAAO,EAAE,MAAM,EAAE;IACjC,OAAO;IACP,MAAM,OAAO,MAAM,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD;IAChC,MAAM,OAAO,OAAO,IAAI;IACxB,MAAM,YAAY,sHAAA,CAAA,OAAI,CAAC,aAAa,CAAC,KAAK;IAC1C,MAAM,aAAa,MAAM,CAAA,GAAA,6HAAA,CAAA,gBAAa,AAAD,EAAE;IACvC,MAAM,OAAO,MAAM,CAAA,GAAA,uIAAA,CAAA,UAAO,AAAD;IACzB,MAAM,aAAa,MAAM,CAAA,GAAA,uIAAA,CAAA,gBAAa,AAAD;IAErC,qBACI,8OAAC,+HAAA,CAAA,UAAS;QAAC,WAAW;kBAClB,cAAA,8OAAC,yHAAA,CAAA,UAAS;YAAC,QAAQ;sBACf,cAAA,8OAAC,qIAAA,CAAA,UAAe;;kCACZ,8OAAC,kIAAA,CAAA,UAAa;wBACV,YAAY;wBACZ,8BACI,8OAAC,mIAAA,CAAA,UAAc;4BACX,0BAAY,8OAAC,sJAAA,CAAA,UAAU;gCAAC,YAAY;gCAAY,MAAM;;;;;;4BACtD,sBAAQ,8OAAC,kJAAA,CAAA,UAAM;;;;;4BACf,sBAAQ,8OAAC,kJAAA,CAAA,UAAc;;;;;sCAEvB,cAAA,8OAAC,kJAAA,CAAA,UAAW;gCAAC,MAAM;gCAAM,gBAAgB,eAAe;;;;;;;;;;;wBAGhE,gCACI,8OAAC,qIAAA,CAAA,UAAgB;4BAAC,sBAAQ,8OAAC,oJAAA,CAAA,UAAM;gCAAC,YAAY;;;;;;4BAAgB,sBAAQ,8OAAC,oJAAA,CAAA,UAAgB;;;;;sCACnF,cAAA,8OAAC,kJAAA,CAAA,UAAW;gCAAC,MAAM;gCAAM,gBAAgB,eAAe;;;;;;;;;;;;;;;;kCAIpE,8OAAC,4JAAA,CAAA,UAAW;wBAAC,WAAU;kCACnB,cAAA,8OAAC,qJAAA,CAAA,UAAM;4BACH,SAAQ;4BACR,WAAU;sCAEV,cAAA,8OAAC;gCAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOzC;uCAEe", "debugId": null}}]}