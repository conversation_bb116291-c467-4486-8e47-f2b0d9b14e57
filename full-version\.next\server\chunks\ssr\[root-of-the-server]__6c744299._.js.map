{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/%40core/components/mui/TextField.jsx"], "sourcesContent": ["'use client'\r\n\r\n// React Imports\r\nimport { forwardRef } from 'react'\r\n\r\n// MUI Imports\r\nimport { styled } from '@mui/material/styles'\r\nimport TextField from '@mui/material/TextField'\r\n\r\nconst TextFieldStyled = styled(TextField)(({ theme }) => ({\r\n  '& .MuiInputLabel-root': {\r\n    transform: 'none',\r\n    width: 'fit-content',\r\n    maxWidth: '100%',\r\n    lineHeight: 1.153,\r\n    position: 'relative',\r\n    fontSize: theme.typography.body2.fontSize,\r\n    marginBottom: theme.spacing(1),\r\n    color: 'var(--mui-palette-text-primary)',\r\n    '&:not(.Mui-error).MuiFormLabel-colorPrimary.Mui-focused': {\r\n      color: 'var(--mui-palette-primary-main) !important'\r\n    },\r\n    '&.Mui-disabled': {\r\n      color: 'var(--mui-palette-text-disabled)'\r\n    },\r\n    '&.Mui-error': {\r\n      color: 'var(--mui-palette-error-main)'\r\n    }\r\n  },\r\n  '& .MuiInputBase-root': {\r\n    backgroundColor: 'transparent !important',\r\n    border: `1px solid var(--mui-palette-customColors-inputBorder)`,\r\n    '&:not(.Mui-focused):not(.Mui-disabled):not(.Mui-error):hover': {\r\n      borderColor: 'var(--mui-palette-action-active)'\r\n    },\r\n    '&:before, &:after': {\r\n      display: 'none'\r\n    },\r\n    '&.MuiInputBase-sizeSmall': {\r\n      borderRadius: 'var(--mui-shape-borderRadius)'\r\n    },\r\n    '&.Mui-error': {\r\n      borderColor: 'var(--mui-palette-error-main)'\r\n    },\r\n    '&.Mui-focused': {\r\n      borderWidth: 2,\r\n      '& .MuiInputBase-input:not(.MuiInputBase-readOnly):not([readonly])::placeholder': {\r\n        transform: 'translateX(4px)'\r\n      },\r\n      '& :not(textarea).MuiFilledInput-input': {\r\n        padding: '6.25px 13px'\r\n      },\r\n      '&:not(.Mui-error).MuiInputBase-colorPrimary': {\r\n        borderColor: 'var(--mui-palette-primary-main)',\r\n        boxShadow: 'var(--mui-customShadows-primary-sm)'\r\n      },\r\n      '&.MuiInputBase-colorSecondary': {\r\n        borderColor: 'var(--mui-palette-secondary-main)'\r\n      },\r\n      '&.MuiInputBase-colorInfo': {\r\n        borderColor: 'var(--mui-palette-info-main)'\r\n      },\r\n      '&.MuiInputBase-colorSuccess': {\r\n        borderColor: 'var(--mui-palette-success-main)'\r\n      },\r\n      '&.MuiInputBase-colorWarning': {\r\n        borderColor: 'var(--mui-palette-warning-main)'\r\n      },\r\n      '&.MuiInputBase-colorError': {\r\n        borderColor: 'var(--mui-palette-error-main)'\r\n      },\r\n      '&.Mui-error': {\r\n        borderColor: 'var(--mui-palette-error-main)'\r\n      }\r\n    },\r\n    '&.Mui-disabled': {\r\n      backgroundColor: 'var(--mui-palette-action-hover) !important'\r\n    }\r\n  },\r\n\r\n  // Adornments\r\n  '& .MuiInputAdornment-root': {\r\n    marginBlockStart: '0px !important',\r\n    '&.MuiInputAdornment-positionStart + .MuiInputBase-input:not(textarea)': {\r\n      paddingInlineStart: '0px !important'\r\n    }\r\n  },\r\n  '& .MuiInputBase-inputAdornedEnd.MuiInputBase-input': {\r\n    paddingInlineEnd: '0px !important'\r\n  },\r\n  '& .MuiInputBase-sizeSmall.MuiInputBase-adornedStart.Mui-focused': {\r\n    paddingInlineStart: '13px',\r\n    '& .MuiInputBase-input': {\r\n      paddingInlineStart: '0px !important'\r\n    }\r\n  },\r\n  '& .MuiInputBase-sizeSmall.MuiInputBase-adornedStart:not(.MuiAutocomplete-inputRoot)': {\r\n    paddingInlineStart: '14px'\r\n  },\r\n  '& .MuiInputBase-sizeSmall.MuiInputBase-adornedEnd:not(.MuiAutocomplete-inputRoot)': {\r\n    paddingInlineEnd: '14px'\r\n  },\r\n  '& .MuiInputBase-sizeSmall.MuiInputBase-adornedEnd.Mui-focused:not(.MuiAutocomplete-inputRoot)': {\r\n    paddingInlineEnd: '13px',\r\n    '& .MuiInputBase-input': {\r\n      paddingInlineEnd: '0px !important'\r\n    }\r\n  },\r\n  '& :not(.MuiInputBase-sizeSmall).MuiInputBase-adornedStart.Mui-focused': {\r\n    paddingInlineStart: '15px',\r\n    '& .MuiInputBase-input': {\r\n      paddingInlineStart: '0px !important'\r\n    }\r\n  },\r\n  '& :not(.MuiInputBase-sizeSmall).MuiInputBase-adornedStart': {\r\n    paddingInlineStart: '16px'\r\n  },\r\n  '& :not(.MuiInputBase-sizeSmall).MuiInputBase-adornedEnd.Mui-focused': {\r\n    paddingInlineEnd: '15px',\r\n    '& .MuiInputBase-input': {\r\n      paddingInlineEnd: '0px !important'\r\n    }\r\n  },\r\n  '& :not(.MuiInputBase-sizeSmall).MuiInputBase-adornedEnd': {\r\n    paddingInlineEnd: '16px'\r\n  },\r\n  '& .MuiInputAdornment-sizeMedium': {\r\n    'i, svg': {\r\n      fontSize: '1.25rem'\r\n    }\r\n  },\r\n  '& .MuiInputBase-input': {\r\n    '&:not(textarea).MuiInputBase-inputSizeSmall': {\r\n      padding: '7.25px 14px'\r\n    },\r\n    '&:not(.MuiInputBase-readOnly):not([readonly])::placeholder': {\r\n      transition: theme.transitions.create(['opacity', 'transform'], {\r\n        duration: theme.transitions.duration.shorter\r\n      })\r\n    }\r\n  },\r\n  '& :not(.MuiInputBase-sizeSmall).MuiInputBase-root': {\r\n    borderRadius: '8px',\r\n    fontSize: '17px',\r\n    lineHeight: '1.41',\r\n    '& .MuiInputBase-input': {\r\n      padding: '10.8px 16px'\r\n    },\r\n    '&.Mui-focused': {\r\n      '& .MuiInputBase-input': {\r\n        padding: '9.8px 15px'\r\n      }\r\n    }\r\n  },\r\n  '& .MuiFormHelperText-root': {\r\n    lineHeight: 1.154,\r\n    margin: theme.spacing(1, 0, 0),\r\n    fontSize: theme.typography.body2.fontSize,\r\n    '&.Mui-error': {\r\n      color: 'var(--mui-palette-error-main)'\r\n    },\r\n    '&.Mui-disabled': {\r\n      color: 'var(--mui-palette-text-disabled)'\r\n    }\r\n  },\r\n\r\n  // For Select\r\n  '& .MuiSelect-select.MuiInputBase-inputSizeSmall, & .MuiNativeSelect-select.MuiInputBase-inputSizeSmall': {\r\n    '& ~ i, & ~ svg': {\r\n      inlineSize: '1.125rem',\r\n      blockSize: '1.125rem'\r\n    }\r\n  },\r\n  '& .MuiSelect-select': {\r\n    // lineHeight: 1.5,\r\n    minHeight: 'unset !important',\r\n    lineHeight: '1.4375em',\r\n    '&.MuiInputBase-input': {\r\n      paddingInlineEnd: '32px !important'\r\n    }\r\n  },\r\n  '& .Mui-focused .MuiSelect-select': {\r\n    '& ~ i, & ~ svg': {\r\n      right: '0.9375rem'\r\n    }\r\n  },\r\n  '& .MuiSelect-select:focus, & .MuiNativeSelect-select:focus': {\r\n    backgroundColor: 'transparent'\r\n  },\r\n\r\n  // For Autocomplete\r\n  '& :not(.MuiInputBase-sizeSmall).MuiAutocomplete-inputRoot': {\r\n    paddingBlock: '5.55px',\r\n    '& .MuiAutocomplete-input': {\r\n      paddingInline: '8px !important',\r\n      paddingBlock: '5.25px !important'\r\n    },\r\n    '&.Mui-focused .MuiAutocomplete-input': {\r\n      paddingInlineStart: '7px !important'\r\n    },\r\n    '&.Mui-focused': {\r\n      paddingBlock: '4.55px !important'\r\n    },\r\n    '& .MuiAutocomplete-endAdornment': {\r\n      top: 'calc(50% - 12px)'\r\n    }\r\n  },\r\n  '& .MuiAutocomplete-inputRoot.MuiInputBase-sizeSmall': {\r\n    paddingBlock: '4.75px !important',\r\n    paddingInlineStart: '10px',\r\n    '&.Mui-focused': {\r\n      paddingBlock: '3.75px !important',\r\n      paddingInlineStart: '9px',\r\n      '.MuiAutocomplete-input': {\r\n        paddingBlock: '2.5px',\r\n        paddingInline: '3px !important'\r\n      }\r\n    },\r\n    '& .MuiAutocomplete-input': {\r\n      paddingInline: '3px !important'\r\n    }\r\n  },\r\n  '& .MuiAutocomplete-inputRoot': {\r\n    display: 'flex',\r\n    gap: '0.25rem',\r\n    '& .MuiAutocomplete-tag': {\r\n      margin: 0\r\n    }\r\n  },\r\n  '& .MuiAutocomplete-inputRoot.Mui-focused .MuiAutocomplete-endAdornment': {\r\n    right: '.9375rem'\r\n  },\r\n\r\n  // For Textarea\r\n  '& .MuiInputBase-multiline': {\r\n    '&.MuiInputBase-sizeSmall': {\r\n      padding: '6px 14px',\r\n      '&.Mui-focused': {\r\n        padding: '5px 13px'\r\n      }\r\n    },\r\n    '& textarea.MuiInputBase-inputSizeSmall:placeholder-shown': {\r\n      overflowX: 'hidden'\r\n    }\r\n  }\r\n}))\r\n\r\nconst CustomTextField = forwardRef((props, ref) => {\r\n  const { size = 'small', slotProps, ...rest } = props\r\n\r\n  return (\r\n    <TextFieldStyled\r\n      size={size}\r\n      inputRef={ref}\r\n      {...rest}\r\n      variant='filled'\r\n      slotProps={{\r\n        ...slotProps,\r\n        inputLabel: { ...slotProps?.inputLabel, shrink: true }\r\n      }}\r\n    />\r\n  )\r\n})\r\n\r\nexport default CustomTextField\r\n"], "names": [], "mappings": ";;;;AAEA,gBAAgB;AAChB;AAEA,cAAc;AACd;AACA;AAPA;;;;;AASA,MAAM,kBAAkB,CAAA,GAAA,0MAAA,CAAA,SAAM,AAAD,EAAE,2JAAA,CAAA,UAAS,EAAE,CAAC,EAAE,KAAK,EAAE,GAAK,CAAC;QACxD,yBAAyB;YACvB,WAAW;YACX,OAAO;YACP,UAAU;YACV,YAAY;YACZ,UAAU;YACV,UAAU,MAAM,UAAU,CAAC,KAAK,CAAC,QAAQ;YACzC,cAAc,MAAM,OAAO,CAAC;YAC5B,OAAO;YACP,2DAA2D;gBACzD,OAAO;YACT;YACA,kBAAkB;gBAChB,OAAO;YACT;YACA,eAAe;gBACb,OAAO;YACT;QACF;QACA,wBAAwB;YACtB,iBAAiB;YACjB,QAAQ,CAAC,qDAAqD,CAAC;YAC/D,gEAAgE;gBAC9D,aAAa;YACf;YACA,qBAAqB;gBACnB,SAAS;YACX;YACA,4BAA4B;gBAC1B,cAAc;YAChB;YACA,eAAe;gBACb,aAAa;YACf;YACA,iBAAiB;gBACf,aAAa;gBACb,kFAAkF;oBAChF,WAAW;gBACb;gBACA,yCAAyC;oBACvC,SAAS;gBACX;gBACA,+CAA+C;oBAC7C,aAAa;oBACb,WAAW;gBACb;gBACA,iCAAiC;oBAC/B,aAAa;gBACf;gBACA,4BAA4B;oBAC1B,aAAa;gBACf;gBACA,+BAA+B;oBAC7B,aAAa;gBACf;gBACA,+BAA+B;oBAC7B,aAAa;gBACf;gBACA,6BAA6B;oBAC3B,aAAa;gBACf;gBACA,eAAe;oBACb,aAAa;gBACf;YACF;YACA,kBAAkB;gBAChB,iBAAiB;YACnB;QACF;QAEA,aAAa;QACb,6BAA6B;YAC3B,kBAAkB;YAClB,yEAAyE;gBACvE,oBAAoB;YACtB;QACF;QACA,sDAAsD;YACpD,kBAAkB;QACpB;QACA,mEAAmE;YACjE,oBAAoB;YACpB,yBAAyB;gBACvB,oBAAoB;YACtB;QACF;QACA,uFAAuF;YACrF,oBAAoB;QACtB;QACA,qFAAqF;YACnF,kBAAkB;QACpB;QACA,iGAAiG;YAC/F,kBAAkB;YAClB,yBAAyB;gBACvB,kBAAkB;YACpB;QACF;QACA,yEAAyE;YACvE,oBAAoB;YACpB,yBAAyB;gBACvB,oBAAoB;YACtB;QACF;QACA,6DAA6D;YAC3D,oBAAoB;QACtB;QACA,uEAAuE;YACrE,kBAAkB;YAClB,yBAAyB;gBACvB,kBAAkB;YACpB;QACF;QACA,2DAA2D;YACzD,kBAAkB;QACpB;QACA,mCAAmC;YACjC,UAAU;gBACR,UAAU;YACZ;QACF;QACA,yBAAyB;YACvB,+CAA+C;gBAC7C,SAAS;YACX;YACA,8DAA8D;gBAC5D,YAAY,MAAM,WAAW,CAAC,MAAM,CAAC;oBAAC;oBAAW;iBAAY,EAAE;oBAC7D,UAAU,MAAM,WAAW,CAAC,QAAQ,CAAC,OAAO;gBAC9C;YACF;QACF;QACA,qDAAqD;YACnD,cAAc;YACd,UAAU;YACV,YAAY;YACZ,yBAAyB;gBACvB,SAAS;YACX;YACA,iBAAiB;gBACf,yBAAyB;oBACvB,SAAS;gBACX;YACF;QACF;QACA,6BAA6B;YAC3B,YAAY;YACZ,QAAQ,MAAM,OAAO,CAAC,GAAG,GAAG;YAC5B,UAAU,MAAM,UAAU,CAAC,KAAK,CAAC,QAAQ;YACzC,eAAe;gBACb,OAAO;YACT;YACA,kBAAkB;gBAChB,OAAO;YACT;QACF;QAEA,aAAa;QACb,0GAA0G;YACxG,kBAAkB;gBAChB,YAAY;gBACZ,WAAW;YACb;QACF;QACA,uBAAuB;YACrB,mBAAmB;YACnB,WAAW;YACX,YAAY;YACZ,wBAAwB;gBACtB,kBAAkB;YACpB;QACF;QACA,oCAAoC;YAClC,kBAAkB;gBAChB,OAAO;YACT;QACF;QACA,8DAA8D;YAC5D,iBAAiB;QACnB;QAEA,mBAAmB;QACnB,6DAA6D;YAC3D,cAAc;YACd,4BAA4B;gBAC1B,eAAe;gBACf,cAAc;YAChB;YACA,wCAAwC;gBACtC,oBAAoB;YACtB;YACA,iBAAiB;gBACf,cAAc;YAChB;YACA,mCAAmC;gBACjC,KAAK;YACP;QACF;QACA,uDAAuD;YACrD,cAAc;YACd,oBAAoB;YACpB,iBAAiB;gBACf,cAAc;gBACd,oBAAoB;gBACpB,0BAA0B;oBACxB,cAAc;oBACd,eAAe;gBACjB;YACF;YACA,4BAA4B;gBAC1B,eAAe;YACjB;QACF;QACA,gCAAgC;YAC9B,SAAS;YACT,KAAK;YACL,0BAA0B;gBACxB,QAAQ;YACV;QACF;QACA,0EAA0E;YACxE,OAAO;QACT;QAEA,eAAe;QACf,6BAA6B;YAC3B,4BAA4B;gBAC1B,SAAS;gBACT,iBAAiB;oBACf,SAAS;gBACX;YACF;YACA,4DAA4D;gBAC1D,WAAW;YACb;QACF;IACF,CAAC;AAED,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,CAAC,OAAO;IACzC,MAAM,EAAE,OAAO,OAAO,EAAE,SAAS,EAAE,GAAG,MAAM,GAAG;IAE/C,qBACE,8OAAC;QACC,MAAM;QACN,UAAU;QACT,GAAG,IAAI;QACR,SAAQ;QACR,WAAW;YACT,GAAG,SAAS;YACZ,YAAY;gBAAE,GAAG,WAAW,UAAU;gBAAE,QAAQ;YAAK;QACvD;;;;;;AAGN;uCAEe", "debugId": null}}, {"offset": {"line": 284, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/views/apps/user/list/TableFilters.jsx"], "sourcesContent": ["// React Imports\r\nimport { useState, useEffect } from 'react'\r\n\r\n// MUI Imports\r\nimport CardContent from '@mui/material/CardContent'\r\nimport Grid from '@mui/material/Grid2'\r\nimport MenuItem from '@mui/material/MenuItem'\r\n\r\n// Component Imports\r\nimport CustomTextField from '@core/components/mui/TextField'\r\n\r\nconst TableFilters = ({ setData, tableData }) => {\r\n  // States\r\n  const [inquiryType, setInquiryType] = useState('')\r\n\r\n  useEffect(() => {\r\n    const filteredData = tableData?.filter(user => {\r\n      if (inquiryType && (user.inquiryType || user.type || 'General Inquiry') !== inquiryType) return false\r\n\r\n      return true\r\n    })\r\n\r\n    setData(filteredData || [])\r\n  }, [inquiryType, tableData, setData])\r\n\r\n  return (\r\n    <CardContent>\r\n      <Grid container spacing={6}>\r\n        <Grid size={{ xs: 12, sm: 6 }}>\r\n          <CustomTextField\r\n            select\r\n            fullWidth\r\n            id='select-inquiry-type'\r\n            value={inquiryType}\r\n            onChange={e => setInquiryType(e.target.value)}\r\n            slotProps={{\r\n              select: { displayEmpty: true }\r\n            }}\r\n            label=\"Inquiry Type\"\r\n          >\r\n            <MenuItem value=''>All Inquiry Types</MenuItem>\r\n            <MenuItem value='General Inquiry'>General Inquiry</MenuItem>\r\n            <MenuItem value='Support Request'>Support Request</MenuItem>\r\n            <MenuItem value='Sales Inquiry'>Sales Inquiry</MenuItem>\r\n            <MenuItem value='Technical Support'>Technical Support</MenuItem>\r\n            <MenuItem value='Billing Question'>Billing Question</MenuItem>\r\n            <MenuItem value='Partnership'>Partnership</MenuItem>\r\n            <MenuItem value='Feedback'>Feedback</MenuItem>\r\n            <MenuItem value='Complaint'>Complaint</MenuItem>\r\n          </CustomTextField>\r\n        </Grid>\r\n      </Grid>\r\n    </CardContent>\r\n  )\r\n}\r\n\r\nexport default TableFilters\r\n"], "names": [], "mappings": "AAAA,gBAAgB;;;;;AAChB;AAEA,cAAc;AACd;AACA;AACA;AAEA,oBAAoB;AACpB;;;;;;;AAEA,MAAM,eAAe,CAAC,EAAE,OAAO,EAAE,SAAS,EAAE;IAC1C,SAAS;IACT,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe,WAAW,OAAO,CAAA;YACrC,IAAI,eAAe,CAAC,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI,iBAAiB,MAAM,aAAa,OAAO;YAEhG,OAAO;QACT;QAEA,QAAQ,gBAAgB,EAAE;IAC5B,GAAG;QAAC;QAAa;QAAW;KAAQ;IAEpC,qBACE,8OAAC,+JAAA,CAAA,UAAW;kBACV,cAAA,8OAAC,mJAAA,CAAA,UAAI;YAAC,SAAS;YAAC,SAAS;sBACvB,cAAA,8OAAC,mJAAA,CAAA,UAAI;gBAAC,MAAM;oBAAE,IAAI;oBAAI,IAAI;gBAAE;0BAC1B,cAAA,8OAAC,gJAAA,CAAA,UAAe;oBACd,MAAM;oBACN,SAAS;oBACT,IAAG;oBACH,OAAO;oBACP,UAAU,CAAA,IAAK,eAAe,EAAE,MAAM,CAAC,KAAK;oBAC5C,WAAW;wBACT,QAAQ;4BAAE,cAAc;wBAAK;oBAC/B;oBACA,OAAM;;sCAEN,8OAAC,yJAAA,CAAA,UAAQ;4BAAC,OAAM;sCAAG;;;;;;sCACnB,8OAAC,yJAAA,CAAA,UAAQ;4BAAC,OAAM;sCAAkB;;;;;;sCAClC,8OAAC,yJAAA,CAAA,UAAQ;4BAAC,OAAM;sCAAkB;;;;;;sCAClC,8OAAC,yJAAA,CAAA,UAAQ;4BAAC,OAAM;sCAAgB;;;;;;sCAChC,8OAAC,yJAAA,CAAA,UAAQ;4BAAC,OAAM;sCAAoB;;;;;;sCACpC,8OAAC,yJAAA,CAAA,UAAQ;4BAAC,OAAM;sCAAmB;;;;;;sCACnC,8OAAC,yJAAA,CAAA,UAAQ;4BAAC,OAAM;sCAAc;;;;;;sCAC9B,8OAAC,yJAAA,CAAA,UAAQ;4BAAC,OAAM;sCAAW;;;;;;sCAC3B,8OAAC,yJAAA,CAAA,UAAQ;4BAAC,OAAM;sCAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMxC;uCAEe", "debugId": null}}, {"offset": {"line": 447, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/views/apps/user/list/UserDetailsModal.jsx"], "sourcesContent": ["'use client'\r\n\r\n// React Imports\r\nimport { useState } from 'react'\r\n\r\n// MUI Imports\r\nimport Dialog from '@mui/material/Dialog'\r\nimport DialogTitle from '@mui/material/DialogTitle'\r\nimport DialogContent from '@mui/material/DialogContent'\r\nimport Button from '@mui/material/Button'\r\nimport Typography from '@mui/material/Typography'\r\nimport Grid from '@mui/material/Grid2'\r\nimport Card from '@mui/material/Card'\r\nimport CardContent from '@mui/material/CardContent'\r\nimport Avatar from '@mui/material/Avatar'\r\nimport Divider from '@mui/material/Divider'\r\nimport TextField from '@mui/material/TextField'\r\nimport IconButton from '@mui/material/IconButton'\r\n\r\n// PDF Export\r\nimport jsPDF from 'jspdf'\r\nimport 'jspdf-autotable'\r\n\r\nconst UserDetailsModal = ({ open, onClose, userData }) => {\r\n  const [userNotes, setUserNotes] = useState({})\r\n\r\n  // Get the current user's note\r\n  const currentNote = userData?.id ? userNotes[userData.id] || '' : ''\r\n\r\n  // Handle note change for current user\r\n  const handleNoteChange = (value) => {\r\n    if (userData?.id) {\r\n      setUserNotes(prev => ({\r\n        ...prev,\r\n        [userData.id]: value\r\n      }))\r\n    }\r\n  }\r\n\r\n  // Handle save note\r\n  const handleSaveNote = () => {\r\n    if (userData?.id && currentNote.trim()) {\r\n      // Here you could also send the note to a backend API\r\n      console.log(`Note saved for user ${userData.id}:`, currentNote)\r\n      // You could show a success message here\r\n      alert('Note saved successfully!')\r\n    }\r\n  }\r\n\r\n  const handleDownloadPDF = () => {\r\n    try {\r\n      const doc = new jsPDF()\r\n\r\n      // Add header\r\n      doc.setFontSize(24)\r\n      doc.setTextColor(40, 40, 40)\r\n      doc.text('CAM Transport - Contact Details', 20, 25)\r\n\r\n      // Add date\r\n      const today = new Date()\r\n      const formattedDate = today.toLocaleDateString()\r\n      doc.setFontSize(12)\r\n      doc.setTextColor(100, 100, 100)\r\n      doc.text(`Generated on: ${formattedDate}`, 20, 35)\r\n\r\n      // Add user avatar placeholder\r\n      doc.setFillColor(41, 128, 185)\r\n      doc.circle(30, 55, 8, 'F')\r\n      doc.setTextColor(255, 255, 255)\r\n      doc.setFontSize(16)\r\n      doc.text(userData?.fullName?.charAt(0)?.toUpperCase() || 'U', 27, 59)\r\n\r\n      // Add user name\r\n      doc.setFontSize(20)\r\n      doc.setTextColor(40, 40, 40)\r\n      doc.text(userData?.fullName || 'Unknown User', 50, 55)\r\n\r\n      // Add email\r\n      doc.setFontSize(14)\r\n      doc.setTextColor(100, 100, 100)\r\n      doc.text(userData?.email || userData?.username || 'No email provided', 50, 65)\r\n\r\n      // Add separator line\r\n      doc.setDrawColor(200, 200, 200)\r\n      doc.line(20, 75, 190, 75)\r\n\r\n      // Contact Details Section\r\n      doc.setFontSize(16)\r\n      doc.setTextColor(40, 40, 40)\r\n      doc.text('Contact Information', 20, 90)\r\n\r\n      let yPosition = 105\r\n      const lineHeight = 15\r\n\r\n      // Contact details\r\n      const details = [\r\n        { label: 'Contact Number:', value: userData?.phone || userData?.contact || '+****************' },\r\n        { label: 'Company:', value: userData?.company || userData?.currentPlan || 'CAM Transport' },\r\n        { label: 'Inquiry Type:', value: userData?.inquiryType || userData?.type || 'General Inquiry' },\r\n        { label: 'Status:', value: userData?.status || 'Pending' }\r\n      ]\r\n\r\n      details.forEach(detail => {\r\n        doc.setFontSize(12)\r\n        doc.setTextColor(100, 100, 100)\r\n        doc.text(detail.label, 20, yPosition)\r\n        doc.setTextColor(40, 40, 40)\r\n        doc.text(detail.value, 70, yPosition)\r\n        yPosition += lineHeight\r\n      })\r\n\r\n      // User Message Section\r\n      if (userData?.userMessage) {\r\n        yPosition += 10\r\n        doc.setFontSize(16)\r\n        doc.setTextColor(40, 40, 40)\r\n        doc.text('User Message', 20, yPosition)\r\n\r\n        yPosition += 15\r\n        doc.setFontSize(12)\r\n        doc.setTextColor(40, 40, 40)\r\n\r\n        // Split long message into multiple lines\r\n        const message = userData.userMessage || 'Hello, I would like to inquire about your services. Please contact me at your earliest convenience.'\r\n        const splitMessage = doc.splitTextToSize(message, 150)\r\n        doc.text(splitMessage, 20, yPosition)\r\n        yPosition += splitMessage.length * 6\r\n      }\r\n\r\n      // Add notes if available\r\n      if (currentNote && currentNote.trim()) {\r\n        yPosition += 15\r\n        doc.setFontSize(16)\r\n        doc.setTextColor(40, 40, 40)\r\n        doc.text('Admin Notes', 20, yPosition)\r\n\r\n        yPosition += 15\r\n        doc.setFontSize(12)\r\n        doc.setTextColor(40, 40, 40)\r\n        const splitNotes = doc.splitTextToSize(currentNote, 150)\r\n        doc.text(splitNotes, 20, yPosition)\r\n      }\r\n\r\n      // Add footer\r\n      doc.setFontSize(10)\r\n      doc.setTextColor(150, 150, 150)\r\n      doc.text('CAM Transport - Contact Management System', 20, 280)\r\n\r\n      // Save the PDF\r\n      const fileName = `${userData?.fullName?.replace(/\\s+/g, '_') || 'contact'}_details_${new Date().toISOString().split('T')[0]}.pdf`\r\n      doc.save(fileName)\r\n\r\n    } catch (error) {\r\n      console.error('PDF Generation Error:', error)\r\n      alert('Error generating PDF. Please try again.')\r\n    }\r\n  }\r\n\r\n  if (!userData) return null\r\n\r\n  return (\r\n    <Dialog open={open} onClose={onClose} maxWidth=\"md\" fullWidth>\r\n      <DialogTitle className=\"flex items-center justify-between\">\r\n        <Typography variant=\"h5\">User Details</Typography>\r\n        <div className=\"flex items-center gap-2\">\r\n          <Button\r\n            variant=\"outlined\"\r\n            color=\"primary\"\r\n            size=\"large\"\r\n            startIcon={<i className=\"tabler-file-type-pdf\" />}\r\n            onClick={handleDownloadPDF}\r\n            className=\"font-bold\"\r\n            sx={{ fontSize: '1rem', padding: '12px 24px' }}\r\n          >\r\n            Download PDF\r\n          </Button>\r\n          <IconButton onClick={onClose}>\r\n            <i className=\"tabler-x\" />\r\n          </IconButton>\r\n        </div>\r\n      </DialogTitle>\r\n\r\n      <DialogContent>\r\n        <Grid container spacing={6}>\r\n          {/* User Profile Section */}\r\n          <Grid size={{ xs: 12 }}>\r\n            <Card>\r\n              <CardContent>\r\n                <div className=\"flex items-center gap-4 mb-6\">\r\n                  <Avatar\r\n                    sx={{ width: 100, height: 100, fontSize: '3rem', fontWeight: 'bold' }}\r\n                  >\r\n                    {userData.fullName?.charAt(0)?.toUpperCase()}\r\n                  </Avatar>\r\n                  <div>\r\n                    <Typography variant=\"h3\" className=\"mb-3 font-bold\" style={{ fontSize: '2rem' }}>\r\n                      {userData.fullName}\r\n                    </Typography>\r\n                    <Typography variant=\"h5\" color=\"text.secondary\" className=\"font-medium\" style={{ fontSize: '1.2rem' }}>\r\n                      {userData.email || userData.username}\r\n                    </Typography>\r\n                  </div>\r\n                </div>\r\n\r\n                <Divider className=\"mb-6\" />\r\n\r\n                {/* User Details Grid */}\r\n                <Grid container spacing={4}>\r\n                  <Grid size={{ xs: 12, sm: 6 }}>\r\n                    <Typography variant=\"h6\" color=\"text.secondary\" className=\"mb-3 font-medium\" style={{ fontSize: '1.2rem' }}>\r\n                      Contact\r\n                    </Typography>\r\n                    <Typography variant=\"h5\" className=\"font-bold\" style={{ fontSize: '1.2rem' }}>\r\n                      {userData.phone || userData.contact || '+****************'}\r\n                    </Typography>\r\n                  </Grid>\r\n\r\n                  <Grid size={{ xs: 12, sm: 6 }}>\r\n                    <Typography variant=\"h6\" color=\"text.secondary\" className=\"mb-3 font-medium\" style={{ fontSize: '1.2rem' }}>\r\n                      Company\r\n                    </Typography>\r\n                    <Typography variant=\"h5\" className=\"font-bold\" style={{ fontSize: '1.2rem' }}>\r\n                      {userData.company || userData.currentPlan}\r\n                    </Typography>\r\n                  </Grid>\r\n\r\n                  <Grid size={{ xs: 12, sm: 6 }}>\r\n                    <Typography variant=\"h6\" color=\"text.secondary\" className=\"mb-3 font-medium\" style={{ fontSize: '1.2rem' }}>\r\n                      Type\r\n                    </Typography>\r\n                    <Typography variant=\"h5\" className=\"font-bold\" style={{ fontSize: '1.2rem' }}>\r\n                      {userData.inquiryType || userData.type || 'General Inquiry'}\r\n                    </Typography>\r\n                  </Grid>\r\n\r\n                  <Grid size={{ xs: 12, sm: 6 }}>\r\n                    <Typography variant=\"h6\" color=\"text.secondary\" className=\"mb-3 font-medium\" style={{ fontSize: '1.2rem' }}>\r\n                      Inquiry Date & Time\r\n                    </Typography>\r\n                    <Typography variant=\"h5\" className=\"font-bold\" style={{ fontSize: '1.2rem' }}>\r\n                      {userData?.inquiryDate || userData?.createdAt\r\n                        ? new Date(userData.inquiryDate || userData.createdAt).toLocaleString('en-US', {\r\n                            year: 'numeric',\r\n                            month: 'long',\r\n                            day: 'numeric',\r\n                            hour: '2-digit',\r\n                            minute: '2-digit',\r\n                            hour12: true\r\n                          })\r\n                        : 'Not available'\r\n                      }\r\n                    </Typography>\r\n                  </Grid>\r\n\r\n                  <Grid size={{ xs: 12, sm: 6 }}>\r\n                    <Typography variant=\"h6\" color=\"text.secondary\" className=\"mb-3 font-medium\" style={{ fontSize: '1.2rem' }}>\r\n                      User Message\r\n                    </Typography>\r\n                    <Typography variant=\"h5\" className=\"font-bold\" style={{ fontSize: '1.2rem', lineHeight: '1.2' }}>\r\n                      {userData.userMessage || 'Hello, I would like to inquire about your services. Please contact me at your earliest convenience.'}\r\n                    </Typography>\r\n                  </Grid>\r\n                </Grid>\r\n              </CardContent>\r\n            </Card>\r\n          </Grid>\r\n\r\n          {/* Add Note Section */}\r\n          <Grid size={{ xs: 12 }}>\r\n            <Card>\r\n              <CardContent>\r\n                <Typography variant=\"h4\" className=\"mb-4 font-bold\" style={{ fontSize: '1.2rem' }}>\r\n                  Add a Note\r\n                </Typography>\r\n                <TextField\r\n                  fullWidth\r\n                  multiline\r\n                  rows={4}\r\n                  placeholder=\"Add your note here...\"\r\n                  value={currentNote}\r\n                  onChange={(e) => handleNoteChange(e.target.value)}\r\n                  variant=\"outlined\"\r\n                  sx={{\r\n                    '& .MuiInputBase-input': {\r\n                      fontSize: '1.25rem',\r\n                      lineHeight: '1.5'\r\n                    },\r\n                    '& .MuiInputLabel-root': {\r\n                      fontSize: '1.25rem'\r\n                    }\r\n                  }}\r\n                />\r\n                <div className=\"flex justify-end mt-4\">\r\n                  <Button\r\n                    variant=\"contained\"\r\n                    color=\"primary\"\r\n                    size=\"large\"\r\n                    className=\"font-bold\"\r\n                    sx={{ fontSize: '1rem', padding: '12px 24px' }}\r\n                    onClick={handleSaveNote}\r\n                  >\r\n                    Save\r\n                  </Button>\r\n                </div>\r\n              </CardContent>\r\n            </Card>\r\n          </Grid>\r\n        </Grid>\r\n      </DialogContent>\r\n    </Dialog>\r\n  )\r\n}\r\n\r\nexport default UserDetailsModal\r\n"], "names": [], "mappings": ";;;;AAEA,gBAAgB;AAChB;AAEA,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA,aAAa;AACb;AACA;AArBA;;;;;;;;;;;;;;;;;AAuBA,MAAM,mBAAmB,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE;IACnD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IAE5C,8BAA8B;IAC9B,MAAM,cAAc,UAAU,KAAK,SAAS,CAAC,SAAS,EAAE,CAAC,IAAI,KAAK;IAElE,sCAAsC;IACtC,MAAM,mBAAmB,CAAC;QACxB,IAAI,UAAU,IAAI;YAChB,aAAa,CAAA,OAAQ,CAAC;oBACpB,GAAG,IAAI;oBACP,CAAC,SAAS,EAAE,CAAC,EAAE;gBACjB,CAAC;QACH;IACF;IAEA,mBAAmB;IACnB,MAAM,iBAAiB;QACrB,IAAI,UAAU,MAAM,YAAY,IAAI,IAAI;YACtC,qDAAqD;YACrD,QAAQ,GAAG,CAAC,CAAC,oBAAoB,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC,EAAE;YACnD,wCAAwC;YACxC,MAAM;QACR;IACF;IAEA,MAAM,oBAAoB;QACxB,IAAI;YACF,MAAM,MAAM,IAAI,mJAAA,CAAA,UAAK;YAErB,aAAa;YACb,IAAI,WAAW,CAAC;YAChB,IAAI,YAAY,CAAC,IAAI,IAAI;YACzB,IAAI,IAAI,CAAC,mCAAmC,IAAI;YAEhD,WAAW;YACX,MAAM,QAAQ,IAAI;YAClB,MAAM,gBAAgB,MAAM,kBAAkB;YAC9C,IAAI,WAAW,CAAC;YAChB,IAAI,YAAY,CAAC,KAAK,KAAK;YAC3B,IAAI,IAAI,CAAC,CAAC,cAAc,EAAE,eAAe,EAAE,IAAI;YAE/C,8BAA8B;YAC9B,IAAI,YAAY,CAAC,IAAI,KAAK;YAC1B,IAAI,MAAM,CAAC,IAAI,IAAI,GAAG;YACtB,IAAI,YAAY,CAAC,KAAK,KAAK;YAC3B,IAAI,WAAW,CAAC;YAChB,IAAI,IAAI,CAAC,UAAU,UAAU,OAAO,IAAI,iBAAiB,KAAK,IAAI;YAElE,gBAAgB;YAChB,IAAI,WAAW,CAAC;YAChB,IAAI,YAAY,CAAC,IAAI,IAAI;YACzB,IAAI,IAAI,CAAC,UAAU,YAAY,gBAAgB,IAAI;YAEnD,YAAY;YACZ,IAAI,WAAW,CAAC;YAChB,IAAI,YAAY,CAAC,KAAK,KAAK;YAC3B,IAAI,IAAI,CAAC,UAAU,SAAS,UAAU,YAAY,qBAAqB,IAAI;YAE3E,qBAAqB;YACrB,IAAI,YAAY,CAAC,KAAK,KAAK;YAC3B,IAAI,IAAI,CAAC,IAAI,IAAI,KAAK;YAEtB,0BAA0B;YAC1B,IAAI,WAAW,CAAC;YAChB,IAAI,YAAY,CAAC,IAAI,IAAI;YACzB,IAAI,IAAI,CAAC,uBAAuB,IAAI;YAEpC,IAAI,YAAY;YAChB,MAAM,aAAa;YAEnB,kBAAkB;YAClB,MAAM,UAAU;gBACd;oBAAE,OAAO;oBAAmB,OAAO,UAAU,SAAS,UAAU,WAAW;gBAAoB;gBAC/F;oBAAE,OAAO;oBAAY,OAAO,UAAU,WAAW,UAAU,eAAe;gBAAgB;gBAC1F;oBAAE,OAAO;oBAAiB,OAAO,UAAU,eAAe,UAAU,QAAQ;gBAAkB;gBAC9F;oBAAE,OAAO;oBAAW,OAAO,UAAU,UAAU;gBAAU;aAC1D;YAED,QAAQ,OAAO,CAAC,CAAA;gBACd,IAAI,WAAW,CAAC;gBAChB,IAAI,YAAY,CAAC,KAAK,KAAK;gBAC3B,IAAI,IAAI,CAAC,OAAO,KAAK,EAAE,IAAI;gBAC3B,IAAI,YAAY,CAAC,IAAI,IAAI;gBACzB,IAAI,IAAI,CAAC,OAAO,KAAK,EAAE,IAAI;gBAC3B,aAAa;YACf;YAEA,uBAAuB;YACvB,IAAI,UAAU,aAAa;gBACzB,aAAa;gBACb,IAAI,WAAW,CAAC;gBAChB,IAAI,YAAY,CAAC,IAAI,IAAI;gBACzB,IAAI,IAAI,CAAC,gBAAgB,IAAI;gBAE7B,aAAa;gBACb,IAAI,WAAW,CAAC;gBAChB,IAAI,YAAY,CAAC,IAAI,IAAI;gBAEzB,yCAAyC;gBACzC,MAAM,UAAU,SAAS,WAAW,IAAI;gBACxC,MAAM,eAAe,IAAI,eAAe,CAAC,SAAS;gBAClD,IAAI,IAAI,CAAC,cAAc,IAAI;gBAC3B,aAAa,aAAa,MAAM,GAAG;YACrC;YAEA,yBAAyB;YACzB,IAAI,eAAe,YAAY,IAAI,IAAI;gBACrC,aAAa;gBACb,IAAI,WAAW,CAAC;gBAChB,IAAI,YAAY,CAAC,IAAI,IAAI;gBACzB,IAAI,IAAI,CAAC,eAAe,IAAI;gBAE5B,aAAa;gBACb,IAAI,WAAW,CAAC;gBAChB,IAAI,YAAY,CAAC,IAAI,IAAI;gBACzB,MAAM,aAAa,IAAI,eAAe,CAAC,aAAa;gBACpD,IAAI,IAAI,CAAC,YAAY,IAAI;YAC3B;YAEA,aAAa;YACb,IAAI,WAAW,CAAC;YAChB,IAAI,YAAY,CAAC,KAAK,KAAK;YAC3B,IAAI,IAAI,CAAC,6CAA6C,IAAI;YAE1D,eAAe;YACf,MAAM,WAAW,GAAG,UAAU,UAAU,QAAQ,QAAQ,QAAQ,UAAU,SAAS,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC;YACjI,IAAI,IAAI,CAAC;QAEX,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM;QACR;IACF;IAEA,IAAI,CAAC,UAAU,OAAO;IAEtB,qBACE,8OAAC,qJAAA,CAAA,UAAM;QAAC,MAAM;QAAM,SAAS;QAAS,UAAS;QAAK,SAAS;;0BAC3D,8OAAC,+JAAA,CAAA,UAAW;gBAAC,WAAU;;kCACrB,8OAAC,6JAAA,CAAA,UAAU;wBAAC,SAAQ;kCAAK;;;;;;kCACzB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,qJAAA,CAAA,UAAM;gCACL,SAAQ;gCACR,OAAM;gCACN,MAAK;gCACL,yBAAW,8OAAC;oCAAE,WAAU;;;;;;gCACxB,SAAS;gCACT,WAAU;gCACV,IAAI;oCAAE,UAAU;oCAAQ,SAAS;gCAAY;0CAC9C;;;;;;0CAGD,8OAAC,6JAAA,CAAA,UAAU;gCAAC,SAAS;0CACnB,cAAA,8OAAC;oCAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;0BAKnB,8OAAC,mKAAA,CAAA,UAAa;0BACZ,cAAA,8OAAC,mJAAA,CAAA,UAAI;oBAAC,SAAS;oBAAC,SAAS;;sCAEvB,8OAAC,mJAAA,CAAA,UAAI;4BAAC,MAAM;gCAAE,IAAI;4BAAG;sCACnB,cAAA,8OAAC,iJAAA,CAAA,UAAI;0CACH,cAAA,8OAAC,+JAAA,CAAA,UAAW;;sDACV,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,qJAAA,CAAA,UAAM;oDACL,IAAI;wDAAE,OAAO;wDAAK,QAAQ;wDAAK,UAAU;wDAAQ,YAAY;oDAAO;8DAEnE,SAAS,QAAQ,EAAE,OAAO,IAAI;;;;;;8DAEjC,8OAAC;;sEACC,8OAAC,6JAAA,CAAA,UAAU;4DAAC,SAAQ;4DAAK,WAAU;4DAAiB,OAAO;gEAAE,UAAU;4DAAO;sEAC3E,SAAS,QAAQ;;;;;;sEAEpB,8OAAC,6JAAA,CAAA,UAAU;4DAAC,SAAQ;4DAAK,OAAM;4DAAiB,WAAU;4DAAc,OAAO;gEAAE,UAAU;4DAAS;sEACjG,SAAS,KAAK,IAAI,SAAS,QAAQ;;;;;;;;;;;;;;;;;;sDAK1C,8OAAC,uJAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;sDAGnB,8OAAC,mJAAA,CAAA,UAAI;4CAAC,SAAS;4CAAC,SAAS;;8DACvB,8OAAC,mJAAA,CAAA,UAAI;oDAAC,MAAM;wDAAE,IAAI;wDAAI,IAAI;oDAAE;;sEAC1B,8OAAC,6JAAA,CAAA,UAAU;4DAAC,SAAQ;4DAAK,OAAM;4DAAiB,WAAU;4DAAmB,OAAO;gEAAE,UAAU;4DAAS;sEAAG;;;;;;sEAG5G,8OAAC,6JAAA,CAAA,UAAU;4DAAC,SAAQ;4DAAK,WAAU;4DAAY,OAAO;gEAAE,UAAU;4DAAS;sEACxE,SAAS,KAAK,IAAI,SAAS,OAAO,IAAI;;;;;;;;;;;;8DAI3C,8OAAC,mJAAA,CAAA,UAAI;oDAAC,MAAM;wDAAE,IAAI;wDAAI,IAAI;oDAAE;;sEAC1B,8OAAC,6JAAA,CAAA,UAAU;4DAAC,SAAQ;4DAAK,OAAM;4DAAiB,WAAU;4DAAmB,OAAO;gEAAE,UAAU;4DAAS;sEAAG;;;;;;sEAG5G,8OAAC,6JAAA,CAAA,UAAU;4DAAC,SAAQ;4DAAK,WAAU;4DAAY,OAAO;gEAAE,UAAU;4DAAS;sEACxE,SAAS,OAAO,IAAI,SAAS,WAAW;;;;;;;;;;;;8DAI7C,8OAAC,mJAAA,CAAA,UAAI;oDAAC,MAAM;wDAAE,IAAI;wDAAI,IAAI;oDAAE;;sEAC1B,8OAAC,6JAAA,CAAA,UAAU;4DAAC,SAAQ;4DAAK,OAAM;4DAAiB,WAAU;4DAAmB,OAAO;gEAAE,UAAU;4DAAS;sEAAG;;;;;;sEAG5G,8OAAC,6JAAA,CAAA,UAAU;4DAAC,SAAQ;4DAAK,WAAU;4DAAY,OAAO;gEAAE,UAAU;4DAAS;sEACxE,SAAS,WAAW,IAAI,SAAS,IAAI,IAAI;;;;;;;;;;;;8DAI9C,8OAAC,mJAAA,CAAA,UAAI;oDAAC,MAAM;wDAAE,IAAI;wDAAI,IAAI;oDAAE;;sEAC1B,8OAAC,6JAAA,CAAA,UAAU;4DAAC,SAAQ;4DAAK,OAAM;4DAAiB,WAAU;4DAAmB,OAAO;gEAAE,UAAU;4DAAS;sEAAG;;;;;;sEAG5G,8OAAC,6JAAA,CAAA,UAAU;4DAAC,SAAQ;4DAAK,WAAU;4DAAY,OAAO;gEAAE,UAAU;4DAAS;sEACxE,UAAU,eAAe,UAAU,YAChC,IAAI,KAAK,SAAS,WAAW,IAAI,SAAS,SAAS,EAAE,cAAc,CAAC,SAAS;gEAC3E,MAAM;gEACN,OAAO;gEACP,KAAK;gEACL,MAAM;gEACN,QAAQ;gEACR,QAAQ;4DACV,KACA;;;;;;;;;;;;8DAKR,8OAAC,mJAAA,CAAA,UAAI;oDAAC,MAAM;wDAAE,IAAI;wDAAI,IAAI;oDAAE;;sEAC1B,8OAAC,6JAAA,CAAA,UAAU;4DAAC,SAAQ;4DAAK,OAAM;4DAAiB,WAAU;4DAAmB,OAAO;gEAAE,UAAU;4DAAS;sEAAG;;;;;;sEAG5G,8OAAC,6JAAA,CAAA,UAAU;4DAAC,SAAQ;4DAAK,WAAU;4DAAY,OAAO;gEAAE,UAAU;gEAAU,YAAY;4DAAM;sEAC3F,SAAS,WAAW,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCASrC,8OAAC,mJAAA,CAAA,UAAI;4BAAC,MAAM;gCAAE,IAAI;4BAAG;sCACnB,cAAA,8OAAC,iJAAA,CAAA,UAAI;0CACH,cAAA,8OAAC,+JAAA,CAAA,UAAW;;sDACV,8OAAC,6JAAA,CAAA,UAAU;4CAAC,SAAQ;4CAAK,WAAU;4CAAiB,OAAO;gDAAE,UAAU;4CAAS;sDAAG;;;;;;sDAGnF,8OAAC,2JAAA,CAAA,UAAS;4CACR,SAAS;4CACT,SAAS;4CACT,MAAM;4CACN,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;4CAChD,SAAQ;4CACR,IAAI;gDACF,yBAAyB;oDACvB,UAAU;oDACV,YAAY;gDACd;gDACA,yBAAyB;oDACvB,UAAU;gDACZ;4CACF;;;;;;sDAEF,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,qJAAA,CAAA,UAAM;gDACL,SAAQ;gDACR,OAAM;gDACN,MAAK;gDACL,WAAU;gDACV,IAAI;oDAAE,UAAU;oDAAQ,SAAS;gDAAY;gDAC7C,SAAS;0DACV;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWnB;uCAEe", "debugId": null}}, {"offset": {"line": 1078, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/components/TablePaginationComponent.jsx"], "sourcesContent": ["// MUI Imports\r\nimport Pagination from '@mui/material/Pagination'\r\nimport Typography from '@mui/material/Typography'\r\n\r\nconst TablePaginationComponent = ({ table }) => {\r\n  return (\r\n    <div className='flex justify-between items-center flex-wrap pli-6 border-bs bs-auto plb-[12.5px] gap-2'>\r\n      <Typography color='text.disabled'>\r\n        {`Showing ${\r\n          table.getFilteredRowModel().rows.length === 0\r\n            ? 0\r\n            : table.getState().pagination.pageIndex * table.getState().pagination.pageSize + 1\r\n        }\r\n        to ${Math.min((table.getState().pagination.pageIndex + 1) * table.getState().pagination.pageSize, table.getFilteredRowModel().rows.length)} of ${table.getFilteredRowModel().rows.length} entries`}\r\n      </Typography>\r\n      <Pagination\r\n        shape='rounded'\r\n        color='primary'\r\n        variant='tonal'\r\n        count={Math.ceil(table.getFilteredRowModel().rows.length / table.getState().pagination.pageSize)}\r\n        page={table.getState().pagination.pageIndex + 1}\r\n        onChange={(_, page) => {\r\n          table.setPageIndex(page - 1)\r\n        }}\r\n        showFirstButton\r\n        showLastButton\r\n      />\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default TablePaginationComponent\r\n"], "names": [], "mappings": "AAAA,cAAc;;;;;AACd;AACA;;;;AAEA,MAAM,2BAA2B,CAAC,EAAE,KAAK,EAAE;IACzC,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,6JAAA,CAAA,UAAU;gBAAC,OAAM;0BACf,CAAC,QAAQ,EACR,MAAM,mBAAmB,GAAG,IAAI,CAAC,MAAM,KAAK,IACxC,IACA,MAAM,QAAQ,GAAG,UAAU,CAAC,SAAS,GAAG,MAAM,QAAQ,GAAG,UAAU,CAAC,QAAQ,GAAG,EACpF;WACE,EAAE,KAAK,GAAG,CAAC,CAAC,MAAM,QAAQ,GAAG,UAAU,CAAC,SAAS,GAAG,CAAC,IAAI,MAAM,QAAQ,GAAG,UAAU,CAAC,QAAQ,EAAE,MAAM,mBAAmB,GAAG,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,MAAM,mBAAmB,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC;;;;;;0BAEpM,8OAAC,6JAAA,CAAA,UAAU;gBACT,OAAM;gBACN,OAAM;gBACN,SAAQ;gBACR,OAAO,KAAK,IAAI,CAAC,MAAM,mBAAmB,GAAG,IAAI,CAAC,MAAM,GAAG,MAAM,QAAQ,GAAG,UAAU,CAAC,QAAQ;gBAC/F,MAAM,MAAM,QAAQ,GAAG,UAAU,CAAC,SAAS,GAAG;gBAC9C,UAAU,CAAC,GAAG;oBACZ,MAAM,YAAY,CAAC,OAAO;gBAC5B;gBACA,eAAe;gBACf,cAAc;;;;;;;;;;;;AAItB;uCAEe", "debugId": null}}, {"offset": {"line": 1131, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/%40core/components/mui/Avatar.jsx"], "sourcesContent": ["'use client'\r\n\r\n// React Imports\r\nimport { forwardRef } from 'react'\r\n\r\n// MUI Imports\r\nimport MuiAvatar from '@mui/material/Avatar'\r\nimport { lighten, styled } from '@mui/material/styles'\r\n\r\nconst Avatar = styled(MuiAvatar)(({ skin, color, size, theme }) => {\r\n  return {\r\n    ...(color &&\r\n      skin === 'light' && {\r\n        backgroundColor: `var(--mui-palette-${color}-lightOpacity)`,\r\n        color: `var(--mui-palette-${color}-main)`\r\n      }),\r\n    ...(color &&\r\n      skin === 'light-static' && {\r\n        backgroundColor: lighten(theme.palette[color].main, 0.84),\r\n        color: `var(--mui-palette-${color}-main)`\r\n      }),\r\n    ...(color &&\r\n      skin === 'filled' && {\r\n        backgroundColor: `var(--mui-palette-${color}-main)`,\r\n        color: `var(--mui-palette-${color}-contrastText)`\r\n      }),\r\n    ...(size && {\r\n      height: size,\r\n      width: size\r\n    })\r\n  }\r\n})\r\n\r\nconst CustomAvatar = forwardRef((props, ref) => {\r\n  // Props\r\n  const { color, skin = 'filled', ...rest } = props\r\n\r\n  return <Avatar color={color} skin={skin} ref={ref} {...rest} />\r\n})\r\n\r\nexport default CustomAvatar\r\n"], "names": [], "mappings": ";;;;AAEA,gBAAgB;AAChB;AAEA,cAAc;AACd;AACA;AAAA;AAPA;;;;;AASA,MAAM,SAAS,CAAA,GAAA,0MAAA,CAAA,SAAM,AAAD,EAAE,qJAAA,CAAA,UAAS,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE;IAC5D,OAAO;QACL,GAAI,SACF,SAAS,WAAW;YAClB,iBAAiB,CAAC,kBAAkB,EAAE,MAAM,cAAc,CAAC;YAC3D,OAAO,CAAC,kBAAkB,EAAE,MAAM,MAAM,CAAC;QAC3C,CAAC;QACH,GAAI,SACF,SAAS,kBAAkB;YACzB,iBAAiB,CAAA,GAAA,8KAAA,CAAA,UAAO,AAAD,EAAE,MAAM,OAAO,CAAC,MAAM,CAAC,IAAI,EAAE;YACpD,OAAO,CAAC,kBAAkB,EAAE,MAAM,MAAM,CAAC;QAC3C,CAAC;QACH,GAAI,SACF,SAAS,YAAY;YACnB,iBAAiB,CAAC,kBAAkB,EAAE,MAAM,MAAM,CAAC;YACnD,OAAO,CAAC,kBAAkB,EAAE,MAAM,cAAc,CAAC;QACnD,CAAC;QACH,GAAI,QAAQ;YACV,QAAQ;YACR,OAAO;QACT,CAAC;IACH;AACF;AAEA,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,CAAC,OAAO;IACtC,QAAQ;IACR,MAAM,EAAE,KAAK,EAAE,OAAO,QAAQ,EAAE,GAAG,MAAM,GAAG;IAE5C,qBAAO,8OAAC;QAAO,OAAO;QAAO,MAAM;QAAM,KAAK;QAAM,GAAG,IAAI;;;;;;AAC7D;uCAEe", "debugId": null}}, {"offset": {"line": 1187, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/services/contactApi.js"], "sourcesContent": ["// API service for contact management\r\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8090'\r\n\r\n// Fetch all contacts from backend\r\nexport const fetchContacts = async () => {\r\n  try {\r\n    const response = await fetch(`${API_BASE_URL}/contact/get-contacts`, {\r\n      method: 'GET',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n      },\r\n    })\r\n\r\n    if (!response.ok) {\r\n      throw new Error(`HTTP error! status: ${response.status}`)\r\n    }\r\n\r\n    const contacts = await response.json()\r\n    \r\n    // Transform backend data to match frontend structure\r\n    return contacts.map(contact => ({\r\n      id: contact._id,\r\n      fullName: contact.name,\r\n      username: contact.name,\r\n      email: contact.email,\r\n      phone: contact.contact,\r\n      contact: contact.contact,\r\n      company: contact.company || 'CAM Transport',\r\n      currentPlan: contact.company || 'CAM Transport',\r\n      inquiryType: contact.inquiryType,\r\n      type: contact.inquiryType,\r\n      billing: contact.inquiryType,\r\n      userMessage: contact.message,\r\n      message: contact.message,\r\n      otherInquiry: contact.otherInquiry,\r\n      ip: contact.ip,\r\n      status: contact.status || 'pending', // Use backend status or default to pending\r\n      createdAt: contact.createdAt,\r\n      updatedAt: contact.updatedAt,\r\n      inquiryDate: contact.createdAt,\r\n      // Add avatar placeholder\r\n      avatar: null\r\n    }))\r\n  } catch (error) {\r\n    console.error('Error fetching contacts:', error)\r\n    throw error\r\n  }\r\n}\r\n\r\n// Update contact status\r\nexport const updateContactStatus = async (contactId, status) => {\r\n  try {\r\n    console.log('API: Updating contact status', contactId, 'to', status)\r\n    console.log('API URL:', `${API_BASE_URL}/contact/update-status/${contactId}`)\r\n\r\n    const response = await fetch(`${API_BASE_URL}/contact/update-status/${contactId}`, {\r\n      method: 'PATCH',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n      },\r\n      body: JSON.stringify({ status })\r\n    })\r\n\r\n    console.log('API Response status:', response.status)\r\n    console.log('API Response ok:', response.ok)\r\n\r\n    if (!response.ok) {\r\n      const errorText = await response.text()\r\n      console.error('API Error response:', errorText)\r\n      throw new Error(`HTTP error! status: ${response.status} - ${errorText}`)\r\n    }\r\n\r\n    const result = await response.json()\r\n    console.log('API Success result:', result)\r\n    return result\r\n  } catch (error) {\r\n    console.error('Error updating contact status:', error)\r\n    throw error\r\n  }\r\n}\r\n\r\n// Delete contact from backend\r\nexport const deleteContact = async (contactId) => {\r\n  try {\r\n    const response = await fetch(`${API_BASE_URL}/contact/delete-contact/${contactId}`, {\r\n      method: 'DELETE',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n      },\r\n    })\r\n\r\n    if (!response.ok) {\r\n      throw new Error(`HTTP error! status: ${response.status}`)\r\n    }\r\n\r\n    const result = await response.json()\r\n    return result\r\n  } catch (error) {\r\n    console.error('Error deleting contact:', error)\r\n    throw error\r\n  }\r\n}\r\n\r\n// Get contact by ID\r\nexport const getContactById = async (contactId) => {\r\n  try {\r\n    const contacts = await fetchContacts()\r\n    return contacts.find(contact => contact.id === contactId)\r\n  } catch (error) {\r\n    console.error('Error fetching contact by ID:', error)\r\n    throw error\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA,qCAAqC;;;;;;;AACrC,MAAM,eAAe,6DAAmC;AAGjD,MAAM,gBAAgB;IAC3B,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,qBAAqB,CAAC,EAAE;YACnE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QAEA,MAAM,WAAW,MAAM,SAAS,IAAI;QAEpC,qDAAqD;QACrD,OAAO,SAAS,GAAG,CAAC,CAAA,UAAW,CAAC;gBAC9B,IAAI,QAAQ,GAAG;gBACf,UAAU,QAAQ,IAAI;gBACtB,UAAU,QAAQ,IAAI;gBACtB,OAAO,QAAQ,KAAK;gBACpB,OAAO,QAAQ,OAAO;gBACtB,SAAS,QAAQ,OAAO;gBACxB,SAAS,QAAQ,OAAO,IAAI;gBAC5B,aAAa,QAAQ,OAAO,IAAI;gBAChC,aAAa,QAAQ,WAAW;gBAChC,MAAM,QAAQ,WAAW;gBACzB,SAAS,QAAQ,WAAW;gBAC5B,aAAa,QAAQ,OAAO;gBAC5B,SAAS,QAAQ,OAAO;gBACxB,cAAc,QAAQ,YAAY;gBAClC,IAAI,QAAQ,EAAE;gBACd,QAAQ,QAAQ,MAAM,IAAI;gBAC1B,WAAW,QAAQ,SAAS;gBAC5B,WAAW,QAAQ,SAAS;gBAC5B,aAAa,QAAQ,SAAS;gBAC9B,yBAAyB;gBACzB,QAAQ;YACV,CAAC;IACH,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM;IACR;AACF;AAGO,MAAM,sBAAsB,OAAO,WAAW;IACnD,IAAI;QACF,QAAQ,GAAG,CAAC,gCAAgC,WAAW,MAAM;QAC7D,QAAQ,GAAG,CAAC,YAAY,GAAG,aAAa,uBAAuB,EAAE,WAAW;QAE5E,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,uBAAuB,EAAE,WAAW,EAAE;YACjF,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBAAE;YAAO;QAChC;QAEA,QAAQ,GAAG,CAAC,wBAAwB,SAAS,MAAM;QACnD,QAAQ,GAAG,CAAC,oBAAoB,SAAS,EAAE;QAE3C,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,QAAQ,KAAK,CAAC,uBAAuB;YACrC,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,CAAC,GAAG,EAAE,WAAW;QACzE;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAClC,QAAQ,GAAG,CAAC,uBAAuB;QACnC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,MAAM;IACR;AACF;AAGO,MAAM,gBAAgB,OAAO;IAClC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,wBAAwB,EAAE,WAAW,EAAE;YAClF,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAClC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAGO,MAAM,iBAAiB,OAAO;IACnC,IAAI;QACF,MAAM,WAAW,MAAM;QACvB,OAAO,SAAS,IAAI,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;IACjD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,MAAM;IACR;AACF", "debugId": null}}, {"offset": {"line": 1296, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/@core/styles/table.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"cellWithInput\": \"table-module__Mig-TG__cellWithInput\",\n  \"table\": \"table-module__Mig-TG__table\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA"}}, {"offset": {"line": 1306, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/views/apps/user/list/UserListTable.jsx"], "sourcesContent": ["'use client'\r\n\r\n// React Imports\r\nimport { useEffect, useState, useMemo } from 'react'\r\n\r\n// Next Imports\r\n// import Link from 'next/link'\r\n// import { useParams } from 'next/navigation'\r\n\r\n// MUI Imports\r\nimport Card from '@mui/material/Card'\r\nimport CardHeader from '@mui/material/CardHeader'\r\nimport CardContent from '@mui/material/CardContent'\r\nimport Button from '@mui/material/Button'\r\nimport Typography from '@mui/material/Typography'\r\nimport Chip from '@mui/material/Chip'\r\nimport Checkbox from '@mui/material/Checkbox'\r\nimport IconButton from '@mui/material/IconButton'\r\nimport Box from '@mui/material/Box'\r\nimport useMediaQuery from '@mui/material/useMediaQuery'\r\nimport { useTheme } from '@mui/material/styles'\r\n// import { styled } from '@mui/material/styles'\r\nimport TablePagination from '@mui/material/TablePagination'\r\nimport MenuItem from '@mui/material/MenuItem'\r\nimport CircularProgress from '@mui/material/CircularProgress'\r\nimport Alert from '@mui/material/Alert'\r\nimport Select from '@mui/material/Select'\r\nimport FormControl from '@mui/material/FormControl'\r\nimport ListItemIcon from '@mui/material/ListItemIcon'\r\n\r\n// Third-party Imports\r\nimport classnames from 'classnames'\r\nimport { rankItem } from '@tanstack/match-sorter-utils'\r\nimport {\r\n  createColumnHelper,\r\n  flexRender,\r\n  getCoreRowModel,\r\n  useReactTable,\r\n  getFilteredRowModel,\r\n  getFacetedRowModel,\r\n  getFacetedUniqueValues,\r\n  getFacetedMinMaxValues,\r\n  getPaginationRowModel,\r\n  getSortedRowModel\r\n} from '@tanstack/react-table'\r\n\r\n// Component Imports\r\nimport TableFilters from './TableFilters'\r\nimport UserDetailsModal from './UserDetailsModal'\r\n\r\nimport TablePaginationComponent from '@components/TablePaginationComponent'\r\nimport CustomTextField from '@core/components/mui/TextField'\r\nimport CustomAvatar from '@core/components/mui/Avatar'\r\n\r\n// PDF Export\r\nimport jsPDF from 'jspdf'\r\n// Import autotable plugin\r\nimport 'jspdf-autotable'\r\n\r\n// API Imports\r\nimport { fetchContacts, deleteContact, updateContactStatus } from '@/services/contactApi'\r\n\r\n// Util Imports\r\n// import { getLocalizedUrl } from '@/utils/i18n'\r\n\r\n// Style Imports\r\nimport tableStyles from '@core/styles/table.module.css'\r\n\r\n// Custom styles for responsive icons\r\nconst actionButtonStyles = {\r\n  minWidth: '32px !important',\r\n  width: '32px !important',\r\n  height: '32px !important',\r\n  padding: '4px !important',\r\n  flexShrink: 0,\r\n  '& i': {\r\n    fontSize: '18px !important',\r\n    minWidth: '18px',\r\n    minHeight: '18px'\r\n  },\r\n  '&:hover': {\r\n    backgroundColor: 'action.hover'\r\n  }\r\n}\r\n\r\n// Styled Components\r\n// const Icon = styled('i')({})\r\n\r\nconst fuzzyFilter = (row, columnId, value, addMeta) => {\r\n  // Rank the item\r\n  const itemRank = rankItem(row.getValue(columnId), value)\r\n\r\n  // Store the itemRank info\r\n  addMeta({\r\n    itemRank\r\n  })\r\n\r\n  // Return if the item should be filtered in/out\r\n  return itemRank.passed\r\n}\r\n\r\nconst DebouncedInput = ({ value: initialValue, onChange, debounce = 500, ...props }) => {\r\n  // States\r\n  const [value, setValue] = useState(initialValue)\r\n\r\n  useEffect(() => {\r\n    setValue(initialValue)\r\n  }, [initialValue])\r\n  useEffect(() => {\r\n    const timeout = setTimeout(() => {\r\n      onChange(value)\r\n    }, debounce)\r\n\r\n    return () => clearTimeout(timeout)\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [value])\r\n\r\n  return <CustomTextField {...props} value={value} onChange={e => setValue(e.target.value)} />\r\n}\r\n\r\n// Vars\r\nconst statusConfig = {\r\n  pending: {\r\n    label: 'Pending',\r\n    color: 'warning',\r\n    icon: 'tabler-clock',\r\n    description: 'Waiting for review'\r\n  },\r\n  'in-view': {\r\n    label: 'In View',\r\n    color: 'info',\r\n    icon: 'tabler-eye',\r\n    description: 'Being reviewed'\r\n  },\r\n  completed: {\r\n    label: 'Completed',\r\n    color: 'success',\r\n    icon: 'tabler-check',\r\n    description: 'Review completed'\r\n  }\r\n}\r\n\r\n// Status options for dropdown\r\nconst statusOptions = [\r\n  { value: 'pending', label: 'Pending', color: 'warning', icon: 'tabler-clock' },\r\n  { value: 'in-view', label: 'In View', color: 'info', icon: 'tabler-eye' },\r\n  { value: 'completed', label: 'Completed', color: 'success', icon: 'tabler-check' }\r\n]\r\n\r\n// Status Dropdown Component\r\nconst StatusDropdown = ({ currentStatus, onStatusChange, contactId }) => {\r\n  const [isOpen, setIsOpen] = useState(false)\r\n  const config = statusConfig[currentStatus] || statusConfig.pending\r\n\r\n  const handleStatusSelect = (newStatus) => {\r\n    console.log('StatusDropdown: Selecting status', newStatus, 'for contact', contactId)\r\n    onStatusChange(contactId, newStatus)\r\n    setIsOpen(false)\r\n  }\r\n\r\n  if (!isOpen) {\r\n    // Show as button/chip when closed\r\n    return (\r\n      <Chip\r\n        icon={<i className={`${config.icon} text-xs sm:text-sm`} />}\r\n        label={config.label}\r\n        color={config.color}\r\n        variant='filled'\r\n        size='small'\r\n        className='text-xs sm:text-sm cursor-pointer'\r\n        onClick={() => setIsOpen(true)}\r\n        title=\"Click to change status\"\r\n        sx={{\r\n          height: { xs: '28px', sm: '32px' },\r\n          width: { xs: '100px', sm: '110px' },\r\n          minWidth: { xs: '100px', sm: '110px' },\r\n          maxWidth: { xs: '100px', sm: '110px' },\r\n          fontSize: { xs: '0.75rem', sm: '0.8rem' },\r\n          cursor: 'pointer',\r\n          transition: 'all 0.2s ease-in-out',\r\n          '& .MuiChip-label': {\r\n            padding: { xs: '0 6px', sm: '0 8px' },\r\n            fontSize: { xs: '0.7rem', sm: '0.75rem' },\r\n            fontWeight: 500,\r\n            whiteSpace: 'nowrap',\r\n            overflow: 'visible',\r\n            textOverflow: 'unset'\r\n          },\r\n          '& .MuiChip-icon': {\r\n            fontSize: { xs: '14px', sm: '16px' },\r\n            marginLeft: { xs: '6px', sm: '8px' },\r\n            marginRight: { xs: '0px', sm: '2px' }\r\n          },\r\n          '&:hover': {\r\n            transform: 'scale(1.02)',\r\n            boxShadow: 2\r\n          }\r\n        }}\r\n      />\r\n    )\r\n  }\r\n\r\n  // Show as dropdown when open\r\n  return (\r\n    <FormControl size=\"small\" sx={{ minWidth: 120 }}>\r\n      <Select\r\n        value={currentStatus}\r\n        onChange={(e) => handleStatusSelect(e.target.value)}\r\n        onClose={() => setIsOpen(false)}\r\n        open={isOpen}\r\n        size=\"small\"\r\n        autoFocus\r\n        sx={{\r\n          height: '32px',\r\n          fontSize: '0.75rem',\r\n          '& .MuiSelect-select': {\r\n            padding: '4px 8px',\r\n            display: 'flex',\r\n            alignItems: 'center',\r\n            gap: 1\r\n          }\r\n        }}\r\n      >\r\n        {statusOptions.map((option) => (\r\n          <MenuItem\r\n            key={option.value}\r\n            value={option.value}\r\n            onClick={() => handleStatusSelect(option.value)}\r\n          >\r\n            <ListItemIcon sx={{ minWidth: '20px !important' }}>\r\n              <i className={`${option.icon} text-sm`} />\r\n            </ListItemIcon>\r\n            <Typography variant=\"body2\" sx={{ fontSize: '0.75rem' }}>\r\n              {option.label}\r\n            </Typography>\r\n          </MenuItem>\r\n        ))}\r\n      </Select>\r\n    </FormControl>\r\n  )\r\n}\r\n\r\n\r\n\r\n// Column Definitions\r\nconst columnHelper = createColumnHelper()\r\n\r\n// Mobile Card Component\r\nconst MobileContactCard = ({ contact, onViewDetails, onDelete, onStatusChange }) => {\r\n  const status = contact.status || 'pending'\r\n\r\n  return (\r\n    <Card className='mb-4 shadow-sm'>\r\n      <CardContent className='p-4'>\r\n        <div className='flex items-start justify-between mb-3'>\r\n          <div className='flex items-center gap-3 flex-1 min-w-0'>\r\n            <CustomAvatar size={40}>\r\n              {contact.fullName?.charAt(0)?.toUpperCase()}\r\n            </CustomAvatar>\r\n            <div className='flex flex-col min-w-0 flex-1'>\r\n              <Typography variant='h6' className='font-semibold text-sm truncate'>\r\n                {contact.fullName}\r\n              </Typography>\r\n              <Typography variant='body2' color='text.secondary' className='text-xs truncate'>\r\n                {contact.email || contact.username}\r\n              </Typography>\r\n            </div>\r\n          </div>\r\n          <div className='flex items-center gap-1 ml-2'>\r\n            <IconButton\r\n              onClick={() => onViewDetails(contact)}\r\n              title=\"View Details\"\r\n              size='small'\r\n              sx={actionButtonStyles}\r\n            >\r\n              <i className='tabler-info-circle text-textSecondary' />\r\n            </IconButton>\r\n            <IconButton\r\n              onClick={() => onDelete(contact.id)}\r\n              title=\"Delete\"\r\n              size='small'\r\n              sx={actionButtonStyles}\r\n            >\r\n              <i className='tabler-trash text-textSecondary' />\r\n            </IconButton>\r\n          </div>\r\n        </div>\r\n\r\n        <div className='grid grid-cols-1 sm:grid-cols-2 gap-3 text-sm'>\r\n          <div>\r\n            <Typography variant='caption' color='text.secondary' className='font-medium'>\r\n              Contact\r\n            </Typography>\r\n            <Typography variant='body2' className='truncate'>\r\n              {contact.phone || contact.contact || '+****************'}\r\n            </Typography>\r\n          </div>\r\n          <div>\r\n            <Typography variant='caption' color='text.secondary' className='font-medium'>\r\n              Type\r\n            </Typography>\r\n            <Typography variant='body2' className='truncate'>\r\n              {contact.inquiryType || contact.type || 'General Inquiry'}\r\n            </Typography>\r\n          </div>\r\n          <div className='sm:col-span-2'>\r\n            <Typography variant='caption' color='text.secondary' className='font-medium'>\r\n              Status\r\n            </Typography>\r\n            <div className='mt-1'>\r\n              <StatusDropdown\r\n                currentStatus={status}\r\n                onStatusChange={onStatusChange}\r\n                contactId={contact.id}\r\n              />\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </CardContent>\r\n    </Card>\r\n  )\r\n}\r\n\r\nconst UserListTable = () => {\r\n  // States\r\n  const [rowSelection, setRowSelection] = useState({})\r\n  const [data, setData] = useState([])\r\n  const [filteredData, setFilteredData] = useState([])\r\n  const [globalFilter, setGlobalFilter] = useState('')\r\n  const [modalOpen, setModalOpen] = useState(false)\r\n  const [selectedUser, setSelectedUser] = useState(null)\r\n  const [loading, setLoading] = useState(true)\r\n  const [error, setError] = useState(null)\r\n\r\n  // Hooks\r\n  const theme = useTheme()\r\n  const isMobile = useMediaQuery(theme.breakpoints.down('md'))\r\n  // const { lang: locale } = useParams()\r\n\r\n  // Load contacts from backend\r\n  const loadContacts = async () => {\r\n    try {\r\n      console.log('🔄 Loading contacts from backend...')\r\n      setLoading(true)\r\n      setError(null)\r\n\r\n      const contacts = await fetchContacts()\r\n      console.log('✅ Fetched contacts:', contacts.length, 'items')\r\n      console.log('📋 Sample contact:', contacts[0])\r\n\r\n      // Ensure all contacts have a status field (default to 'pending' if missing)\r\n      const contactsWithStatus = contacts.map(contact => ({\r\n        ...contact,\r\n        status: contact.status || 'pending'\r\n      }))\r\n\r\n      setData(contactsWithStatus)\r\n      setFilteredData(contactsWithStatus)\r\n    } catch (err) {\r\n      console.error('❌ Error loading contacts:', err)\r\n      setError('Failed to load contacts. Please try again.')\r\n    } finally {\r\n      setLoading(false)\r\n    }\r\n  }\r\n\r\n  // Load contacts from backend on component mount\r\n  useEffect(() => {\r\n    // Clear old localStorage data that might interfere with backend status\r\n    localStorage.removeItem('contactStatuses')\r\n    localStorage.removeItem('jobApplicationStatuses')\r\n    localStorage.removeItem('quoteStatuses')\r\n    localStorage.removeItem('urgentInquiryStatuses')\r\n    console.log('🧹 Cleared all old localStorage status data')\r\n\r\n    loadContacts()\r\n  }, [])\r\n\r\n  const handleViewDetails = (userData) => {\r\n    setSelectedUser(userData)\r\n    setModalOpen(true)\r\n  }\r\n\r\n  const handleModalClose = () => {\r\n    setModalOpen(false)\r\n    setSelectedUser(null)\r\n  }\r\n\r\n  const handleStatusChange = async (userId, newStatus) => {\r\n    console.log('Updating status for user:', userId, 'to:', newStatus)\r\n\r\n    try {\r\n      // Call API to update status in backend\r\n      await updateContactStatus(userId, newStatus)\r\n      console.log('Contact status updated in backend successfully')\r\n\r\n      // Update local state only after successful backend update\r\n      setData(prevData =>\r\n        prevData.map(item =>\r\n          item.id === userId\r\n            ? { ...item, status: newStatus }\r\n            : item\r\n        )\r\n      )\r\n      setFilteredData(prevData =>\r\n        prevData.map(item =>\r\n          item.id === userId\r\n            ? { ...item, status: newStatus }\r\n            : item\r\n        )\r\n      )\r\n\r\n      console.log('Contact status updated in frontend successfully')\r\n    } catch (error) {\r\n      console.error('Failed to update contact status:', error)\r\n      alert('Failed to update status. Please try again.')\r\n    }\r\n  }\r\n\r\n  // Optional: Clear all saved statuses (for debugging)\r\n  const clearSavedStatuses = () => {\r\n    localStorage.removeItem('contactStatuses')\r\n    console.log('Cleared all saved contact statuses')\r\n  }\r\n\r\n  const handleDeleteContact = async (contactId) => {\r\n    try {\r\n      // Find the contact to get their name for confirmation\r\n      const contact = data.find(item => item.id === contactId)\r\n      const contactName = contact?.fullName || 'this contact'\r\n\r\n      // Show confirmation dialog\r\n      const confirmed = window.confirm(\r\n        `Are you sure you want to delete ${contactName}?\\n\\nThis action cannot be undone and will permanently remove the contact from the database.`\r\n      )\r\n\r\n      if (!confirmed) {\r\n        return\r\n      }\r\n\r\n      // Call backend API to delete contact\r\n      await deleteContact(contactId)\r\n\r\n      // Remove contact from local state\r\n      setData(prevData => prevData.filter(item => item.id !== contactId))\r\n      setFilteredData(prevData => prevData.filter(item => item.id !== contactId))\r\n\r\n      // Clear selection if deleted contact was selected\r\n      setRowSelection(prevSelection => {\r\n        const newSelection = { ...prevSelection }\r\n        delete newSelection[contactId]\r\n        return newSelection\r\n      })\r\n\r\n      // Show success message\r\n      alert(`${contactName} has been deleted successfully!`)\r\n\r\n    } catch (error) {\r\n      console.error('Error deleting contact:', error)\r\n      alert('Failed to delete contact. Please try again.')\r\n    }\r\n  }\r\n\r\n  // Get selected rows data\r\n  const getSelectedRowsData = () => {\r\n    const selectedRows = table.getFilteredSelectedRowModel().rows\r\n    return selectedRows.map(row => row.original)\r\n  }\r\n\r\n  // PDF Export function\r\n  const exportSelectedToPDF = () => {\r\n    const selectedData = getSelectedRowsData()\r\n\r\n    if (selectedData.length === 0) {\r\n      alert('Please select at least one contact to export.')\r\n      return\r\n    }\r\n\r\n    try {\r\n      const doc = new jsPDF()\r\n\r\n      // Add title\r\n      doc.setFontSize(20)\r\n      doc.setTextColor(40, 40, 40)\r\n      doc.text('CAM Transport - Contact Export', 20, 20)\r\n\r\n      // Add export info\r\n      doc.setFontSize(12)\r\n      doc.setTextColor(100, 100, 100)\r\n      doc.text(`Export Date: ${new Date().toLocaleDateString()}`, 20, 35)\r\n      doc.text(`Selected Contacts: ${selectedData.length}`, 20, 45)\r\n\r\n      // Prepare table data\r\n      const tableData = selectedData.map(contact => [\r\n        contact.fullName || 'N/A',\r\n        contact.email || contact.username || 'N/A',\r\n        contact.phone || contact.contact || 'N/A',\r\n        contact.company || contact.currentPlan || 'CAM Transport',\r\n        contact.inquiryType || contact.type || 'General Inquiry',\r\n        statusConfig[contact.status || 'pending']?.label || 'Pending'\r\n      ])\r\n\r\n      // Try different autoTable approaches\r\n      if (typeof doc.autoTable === 'function') {\r\n        // Method 1: Direct method\r\n        doc.autoTable({\r\n          head: [['Name', 'Email', 'Contact', 'Company', 'Inquiry Type', 'Status']],\r\n          body: tableData,\r\n          startY: 60,\r\n          styles: {\r\n            fontSize: 10,\r\n            cellPadding: 3\r\n          },\r\n          headStyles: {\r\n            fillColor: [41, 128, 185],\r\n            textColor: 255,\r\n            fontStyle: 'bold'\r\n          },\r\n          alternateRowStyles: {\r\n            fillColor: [245, 245, 245]\r\n          }\r\n        })\r\n      } else if (typeof autoTable === 'function') {\r\n        // Method 2: Imported function\r\n        autoTable(doc, {\r\n          head: [['Name', 'Email', 'Contact', 'Company', 'Inquiry Type', 'Status']],\r\n          body: tableData,\r\n          startY: 60,\r\n          styles: {\r\n            fontSize: 10,\r\n            cellPadding: 3\r\n          },\r\n          headStyles: {\r\n            fillColor: [41, 128, 185],\r\n            textColor: 255,\r\n            fontStyle: 'bold'\r\n          },\r\n          alternateRowStyles: {\r\n            fillColor: [245, 245, 245]\r\n          }\r\n        })\r\n      } else {\r\n        // Fallback: Manual table creation\r\n        let yPosition = 70\r\n        const lineHeight = 8\r\n\r\n        // Add headers\r\n        doc.setFontSize(10)\r\n        doc.setFont(undefined, 'bold')\r\n        doc.text('Name', 20, yPosition)\r\n        doc.text('Email', 60, yPosition)\r\n        doc.text('Contact', 110, yPosition)\r\n        doc.text('Company', 150, yPosition)\r\n        doc.text('Type', 180, yPosition)\r\n\r\n        yPosition += lineHeight + 2\r\n\r\n        // Add data rows\r\n        doc.setFont(undefined, 'normal')\r\n        tableData.forEach(row => {\r\n          doc.text(row[0].substring(0, 15), 20, yPosition)\r\n          doc.text(row[1].substring(0, 20), 60, yPosition)\r\n          doc.text(row[2].substring(0, 15), 110, yPosition)\r\n          doc.text(row[3].substring(0, 12), 150, yPosition)\r\n          doc.text(row[4].substring(0, 10), 180, yPosition)\r\n          yPosition += lineHeight\r\n\r\n          // Add new page if needed\r\n          if (yPosition > 270) {\r\n            doc.addPage()\r\n            yPosition = 20\r\n          }\r\n        })\r\n      }\r\n\r\n      // Save the PDF\r\n      doc.save(`CAM_Transport_Contacts_${new Date().toISOString().split('T')[0]}.pdf`)\r\n\r\n    } catch (error) {\r\n      console.error('PDF Export Error:', error)\r\n      alert('Error generating PDF. Please try again.')\r\n    }\r\n  }\r\n\r\n  const columns = useMemo(\r\n    () => [\r\n      {\r\n        id: 'select',\r\n        header: ({ table }) => (\r\n          <Checkbox\r\n            {...{\r\n              checked: table.getIsAllRowsSelected(),\r\n              indeterminate: table.getIsSomeRowsSelected(),\r\n              onChange: table.getToggleAllRowsSelectedHandler()\r\n            }}\r\n          />\r\n        ),\r\n        cell: ({ row }) => (\r\n          <Checkbox\r\n            {...{\r\n              checked: row.getIsSelected(),\r\n              disabled: !row.getCanSelect(),\r\n              indeterminate: row.getIsSomeSelected(),\r\n              onChange: row.getToggleSelectedHandler()\r\n            }}\r\n          />\r\n        )\r\n      },\r\n      columnHelper.accessor('fullName', {\r\n        header: 'User',\r\n        cell: ({ row }) => (\r\n          <div className='flex items-center gap-2 sm:gap-3 min-w-[150px] sm:min-w-[200px] max-w-[250px]'>\r\n            <div className='flex-shrink-0'>\r\n              {getAvatar({ avatar: row.original.avatar, fullName: row.original.fullName })}\r\n            </div>\r\n            <div className='flex flex-col overflow-hidden min-w-0'>\r\n              <Typography\r\n                color='text.primary'\r\n                className='font-medium truncate text-sm sm:text-base'\r\n                title={row.original.fullName}\r\n              >\r\n                {row.original.fullName}\r\n              </Typography>\r\n              <Typography\r\n                variant='body2'\r\n                color='text.secondary'\r\n                className='truncate text-xs sm:text-sm'\r\n                style={{ letterSpacing: '0.3px' }}\r\n                title={row.original.email || row.original.username}\r\n              >\r\n                {row.original.email || row.original.username}\r\n              </Typography>\r\n            </div>\r\n          </div>\r\n        )\r\n      }),\r\n      columnHelper.accessor('role', {\r\n        header: 'Contact',\r\n        cell: ({ row }) => (\r\n          <div className='min-w-[100px] sm:min-w-[120px] pr-3'>\r\n            <Typography\r\n              color='text.primary'\r\n              className='font-medium text-xs sm:text-sm truncate'\r\n              title={row.original.phone || row.original.contact || '+****************'}\r\n            >\r\n              {row.original.phone || row.original.contact || '+****************'}\r\n            </Typography>\r\n          </div>\r\n        ),\r\n        enableSorting: false\r\n      }),\r\n\r\n      columnHelper.accessor('billing', {\r\n        header: 'Type',\r\n        cell: ({ row }) => (\r\n          <div className='min-w-[110px] sm:min-w-[130px] pr-3'>\r\n            <Typography\r\n              color='text.primary'\r\n              className='font-medium text-xs sm:text-sm'\r\n              style={{\r\n                whiteSpace: 'normal',\r\n                wordWrap: 'break-word',\r\n                lineHeight: '1.3'\r\n              }}\r\n              title={row.original.inquiryType || row.original.type || 'General Inquiry'}\r\n            >\r\n              {row.original.inquiryType || row.original.type || 'General Inquiry'}\r\n            </Typography>\r\n          </div>\r\n        ),\r\n        enableSorting: false\r\n      }),\r\n      columnHelper.accessor('status', {\r\n        header: 'Status',\r\n        cell: ({ row }) => {\r\n          const status = row.original.status || 'pending'\r\n\r\n          return (\r\n            <div className='flex items-center justify-start gap-1 min-w-[110px] sm:min-w-[120px] pr-3'>\r\n              <StatusDropdown\r\n                currentStatus={status}\r\n                onStatusChange={handleStatusChange}\r\n                contactId={row.original.id}\r\n              />\r\n            </div>\r\n          )\r\n        },\r\n        enableSorting: false\r\n      }),\r\n\r\n      columnHelper.accessor('action', {\r\n        header: 'Action',\r\n        cell: ({ row }) => (\r\n          <div className='flex items-center justify-start gap-2 min-w-[120px] w-[120px] pr-4' style={{ flexShrink: 0 }}>\r\n            {/* View Details - Info Icon */}\r\n            <IconButton\r\n              onClick={() => handleViewDetails(row.original)}\r\n              title=\"View Details\"\r\n              size='small'\r\n              sx={actionButtonStyles}\r\n            >\r\n              <i className='tabler-info-circle text-textSecondary' />\r\n            </IconButton>\r\n\r\n            {/* Delete */}\r\n            <IconButton\r\n              onClick={() => handleDeleteContact(row.original.id)}\r\n              title=\"Delete\"\r\n              size='small'\r\n              sx={actionButtonStyles}\r\n            >\r\n              <i className='tabler-trash text-textSecondary' />\r\n            </IconButton>\r\n          </div>\r\n        ),\r\n        enableSorting: false\r\n      })\r\n    ],\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n    [data, filteredData]\r\n  )\r\n\r\n  const table = useReactTable({\r\n    data: filteredData,\r\n    columns,\r\n    filterFns: {\r\n      fuzzy: fuzzyFilter\r\n    },\r\n    state: {\r\n      rowSelection,\r\n      globalFilter\r\n    },\r\n    initialState: {\r\n      pagination: {\r\n        pageSize: 10\r\n      }\r\n    },\r\n    enableRowSelection: true, //enable row selection for all rows\r\n    // enableRowSelection: row => row.original.age > 18, // or enable row selection conditionally per row\r\n    globalFilterFn: fuzzyFilter,\r\n    onRowSelectionChange: setRowSelection,\r\n    getCoreRowModel: getCoreRowModel(),\r\n    onGlobalFilterChange: setGlobalFilter,\r\n    getFilteredRowModel: getFilteredRowModel(),\r\n    getSortedRowModel: getSortedRowModel(),\r\n    getPaginationRowModel: getPaginationRowModel(),\r\n    getFacetedRowModel: getFacetedRowModel(),\r\n    getFacetedUniqueValues: getFacetedUniqueValues(),\r\n    getFacetedMinMaxValues: getFacetedMinMaxValues()\r\n  })\r\n\r\n  const getAvatar = params => {\r\n    const { fullName } = params\r\n\r\n    return <CustomAvatar size={34}>{fullName?.charAt(0)?.toUpperCase()}</CustomAvatar>\r\n  }\r\n\r\n  // Show loading state\r\n  if (loading) {\r\n    return (\r\n      <Card>\r\n        <CardHeader title='Contact List' className='pbe-4' />\r\n        <CardContent className='flex justify-center items-center py-8'>\r\n          <div className='flex flex-col items-center gap-4'>\r\n            <CircularProgress />\r\n            <Typography variant='body2' color='text.secondary'>\r\n              Loading contacts...\r\n            </Typography>\r\n          </div>\r\n        </CardContent>\r\n      </Card>\r\n    )\r\n  }\r\n\r\n  // Show error state\r\n  if (error) {\r\n    return (\r\n      <Card>\r\n        <CardHeader title='Contact List' className='pbe-4' />\r\n        <CardContent>\r\n          <Alert severity='error' className='mb-4'>\r\n            {error}\r\n          </Alert>\r\n          <Button\r\n            variant='contained'\r\n            onClick={() => window.location.reload()}\r\n            startIcon={<i className='tabler-refresh' />}\r\n          >\r\n            Retry\r\n          </Button>\r\n        </CardContent>\r\n      </Card>\r\n    )\r\n  }\r\n\r\n  // Show empty state\r\n  if (!data || data.length === 0) {\r\n    return (\r\n      <Card>\r\n        <CardHeader title='Contact List' className='pbe-4' />\r\n        <CardContent className='flex justify-center items-center py-8'>\r\n          <div className='flex flex-col items-center gap-4 text-center'>\r\n            <i className='tabler-users text-6xl text-textSecondary' />\r\n            <Typography variant='h6' color='text.secondary'>\r\n              No contacts found\r\n            </Typography>\r\n            <Typography variant='body2' color='text.secondary'>\r\n              Contacts submitted through the website will appear here.\r\n            </Typography>\r\n          </div>\r\n        </CardContent>\r\n      </Card>\r\n    )\r\n  }\r\n\r\n  return (\r\n    <>\r\n      {isMobile ? (\r\n        // Mobile View\r\n        <Card className='w-full'>\r\n          <CardHeader\r\n            title='Contact Management'\r\n            subheader='Manage contact submissions and review status'\r\n            className='pb-2 px-4'\r\n          />\r\n          <CardContent className='px-4'>\r\n            <TableFilters setData={setFilteredData} tableData={data} />\r\n\r\n            <div className='flex flex-col gap-4 mb-4 mt-4'>\r\n              <DebouncedInput\r\n                value={globalFilter ?? ''}\r\n                onChange={value => setGlobalFilter(String(value))}\r\n                placeholder='Search contacts...'\r\n                className='w-full'\r\n                size='small'\r\n              />\r\n\r\n              {/* Selection Counter */}\r\n              {Object.keys(rowSelection).length > 0 && (\r\n                <div className='flex items-center justify-between p-3 bg-primary/10 rounded-lg border border-primary/20'>\r\n                  <div className='flex items-center gap-2'>\r\n                    <i className='tabler-check-circle text-primary' />\r\n                    <Typography variant='body2' className='font-medium text-primary'>\r\n                      {Object.keys(rowSelection).length} contact{Object.keys(rowSelection).length !== 1 ? 's' : ''} selected\r\n                    </Typography>\r\n                  </div>\r\n                  <Button\r\n                    size='small'\r\n                    variant='text'\r\n                    onClick={() => setRowSelection({})}\r\n                    className='text-primary'\r\n                  >\r\n                    Clear Selection\r\n                  </Button>\r\n                </div>\r\n              )}\r\n\r\n              <Button\r\n                color='secondary'\r\n                variant='tonal'\r\n                startIcon={<i className='tabler-file-type-pdf' />}\r\n                className='w-full'\r\n                size='small'\r\n                onClick={exportSelectedToPDF}\r\n                disabled={Object.keys(rowSelection).length === 0}\r\n              >\r\n                Export PDF ({Object.keys(rowSelection).length})\r\n              </Button>\r\n\r\n              <IconButton\r\n                color='primary'\r\n                onClick={loadContacts}\r\n                disabled={loading}\r\n                title={loading ? 'Loading...' : 'Refresh Data'}\r\n                sx={{\r\n                  border: '1px solid',\r\n                  borderColor: 'primary.main',\r\n                  width: '100%',\r\n                  height: '40px',\r\n                  borderRadius: '8px',\r\n                  '&:hover': {\r\n                    backgroundColor: 'primary.light',\r\n                    transform: 'scale(1.02)'\r\n                  },\r\n                  transition: 'all 0.2s ease-in-out'\r\n                }}\r\n              >\r\n                <i className={`tabler-refresh ${loading ? 'animate-spin' : ''}`} />\r\n              </IconButton>\r\n            </div>\r\n\r\n            <Box sx={{ '& > *:not(:last-child)': { marginBottom: 2 } }}>\r\n              {table.getFilteredRowModel().rows.length === 0 ? (\r\n                <Card className='p-8 text-center'>\r\n                  <Typography color='text.secondary'>No contacts found</Typography>\r\n                </Card>\r\n              ) : (\r\n                table\r\n                  .getRowModel()\r\n                  .rows.slice(0, table.getState().pagination.pageSize)\r\n                  .map(row => (\r\n                    <MobileContactCard\r\n                      key={row.id}\r\n                      contact={row.original}\r\n                      onViewDetails={handleViewDetails}\r\n                      onDelete={handleDeleteContact}\r\n                      onStatusChange={handleStatusChange}\r\n                    />\r\n                  ))\r\n              )}\r\n            </Box>\r\n\r\n            <Box className='mt-4'>\r\n              <TablePagination\r\n                component={() => <TablePaginationComponent table={table} />}\r\n                count={table.getFilteredRowModel().rows.length}\r\n                rowsPerPage={table.getState().pagination.pageSize}\r\n                page={table.getState().pagination.pageIndex}\r\n                onPageChange={(_, page) => {\r\n                  table.setPageIndex(page)\r\n                }}\r\n              />\r\n            </Box>\r\n          </CardContent>\r\n        </Card>\r\n      ) : (\r\n        // Desktop View\r\n        <Card className='w-full'>\r\n          <CardHeader\r\n            title={\r\n              <Typography variant='h5' className='text-lg sm:text-xl lg:text-2xl'>\r\n                Contact Management\r\n              </Typography>\r\n            }\r\n            subheader={\r\n              <Typography variant='body2' className='text-xs sm:text-sm'>\r\n                Manage contact submissions and review status\r\n              </Typography>\r\n            }\r\n            className='pbe-4 px-3 sm:px-6'\r\n          />\r\n          <TableFilters setData={setFilteredData} tableData={data} />\r\n        <div className='flex justify-between flex-col items-start lg:flex-row lg:items-center p-4 sm:p-6 border-bs gap-4'>\r\n          <div className='flex flex-col sm:flex-row items-start sm:items-center gap-4 w-full lg:w-auto'>\r\n            <CustomTextField\r\n              select\r\n              value={table.getState().pagination.pageSize}\r\n              onChange={e => table.setPageSize(Number(e.target.value))}\r\n              className='w-full sm:w-[100px]'\r\n              size='small'\r\n            >\r\n              <MenuItem value='10'>10</MenuItem>\r\n              <MenuItem value='25'>25</MenuItem>\r\n              <MenuItem value='50'>50</MenuItem>\r\n            </CustomTextField>\r\n            <Typography variant='body2' color='text.secondary' className='hidden sm:block'>\r\n              entries per page\r\n            </Typography>\r\n\r\n            {/* Selection Counter for Desktop */}\r\n            {Object.keys(rowSelection).length > 0 && (\r\n              <div className='flex items-center gap-2 px-3 py-1 bg-primary/10 rounded-full border border-primary/20'>\r\n                <i className='tabler-check-circle text-primary text-sm' />\r\n                <Typography variant='caption' className='font-medium text-primary'>\r\n                  {Object.keys(rowSelection).length} selected\r\n                </Typography>\r\n                <Button\r\n                  size='small'\r\n                  variant='text'\r\n                  onClick={() => setRowSelection({})}\r\n                  className='text-primary p-1 min-w-auto'\r\n                  sx={{ fontSize: '0.7rem', padding: '2px 6px' }}\r\n                >\r\n                  Clear\r\n                </Button>\r\n              </div>\r\n            )}\r\n          </div>\r\n          <div className='flex flex-col sm:flex-row w-full lg:w-auto items-start sm:items-center gap-4'>\r\n            <DebouncedInput\r\n              value={globalFilter ?? ''}\r\n              onChange={value => setGlobalFilter(String(value))}\r\n              placeholder='Search contacts...'\r\n              className='w-full sm:w-[250px]'\r\n              size='small'\r\n            />\r\n            <Button\r\n              color='secondary'\r\n              variant='tonal'\r\n              startIcon={<i className='tabler-file-type-pdf' />}\r\n              className='w-full sm:w-auto'\r\n              size='small'\r\n              onClick={exportSelectedToPDF}\r\n              disabled={Object.keys(rowSelection).length === 0}\r\n            >\r\n              Export PDF ({Object.keys(rowSelection).length})\r\n            </Button>\r\n\r\n            <IconButton\r\n              color='primary'\r\n              onClick={loadContacts}\r\n              disabled={loading}\r\n              title={loading ? 'Loading...' : 'Refresh Data'}\r\n              sx={{\r\n                border: '1px solid',\r\n                borderColor: 'primary.main',\r\n                '&:hover': {\r\n                  backgroundColor: 'primary.light',\r\n                  transform: 'scale(1.05)'\r\n                },\r\n                transition: 'all 0.2s ease-in-out'\r\n              }}\r\n            >\r\n              <i className={`tabler-refresh ${loading ? 'animate-spin' : ''}`} />\r\n            </IconButton>\r\n          </div>\r\n        </div>\r\n        <div className='overflow-x-auto overflow-y-hidden'>\r\n          <div className='min-w-[580px] sm:min-w-[780px] lg:min-w-[980px]'>\r\n            <table className={`${tableStyles.table} w-full table-fixed`}>\r\n            <thead>\r\n              {table.getHeaderGroups().map(headerGroup => (\r\n                <tr key={headerGroup.id}>\r\n                  {headerGroup.headers.map((header, index) => {\r\n                    // Define column widths\r\n                    let width = 'auto'\r\n                    if (header.id === 'select') width = '50px'\r\n                    else if (header.id === 'fullName') width = '30%'\r\n                    else if (header.id === 'role') width = '20%'\r\n                    else if (header.id === 'billing') width = '20%'\r\n                    else if (header.id === 'status') width = '18%'\r\n                    else if (header.id === 'action') width = '120px'\r\n\r\n                    return (\r\n                      <th\r\n                        key={header.id}\r\n                        className={`px-3 sm:px-4 py-2 sm:py-3 ${header.id === 'action' ? 'pr-6' : ''} ${header.id === 'status' ? 'pr-3' : ''} ${header.id === 'role' ? 'pr-3' : ''} ${header.id === 'billing' ? 'pr-3' : ''}`}\r\n                        style={{ width }}\r\n                      >\r\n                        {header.isPlaceholder ? null : (\r\n                          <>\r\n                            <div\r\n                              className={classnames({\r\n                                'flex items-center': header.column.getIsSorted(),\r\n                                'cursor-pointer select-none': header.column.getCanSort()\r\n                              })}\r\n                              onClick={header.column.getCanSort() ? header.column.getToggleSortingHandler() : undefined}\r\n                            >\r\n                              <Typography\r\n                                variant='body2'\r\n                                className='font-semibold text-xs sm:text-sm'\r\n                                color='text.primary'\r\n                              >\r\n                                {flexRender(header.column.columnDef.header, header.getContext())}\r\n                              </Typography>\r\n                              {header.column.getCanSort() && (\r\n                                {\r\n                                  asc: <i className='tabler-chevron-up text-lg sm:text-xl ml-1' />,\r\n                                  desc: <i className='tabler-chevron-down text-lg sm:text-xl ml-1' />\r\n                                }[header.column.getIsSorted()] ?? null\r\n                              )}\r\n                            </div>\r\n                          </>\r\n                        )}\r\n                      </th>\r\n                    )\r\n                  })}\r\n                </tr>\r\n              ))}\r\n            </thead>\r\n            {table.getFilteredRowModel().rows.length === 0 ? (\r\n              <tbody>\r\n                <tr>\r\n                  <td colSpan={table.getVisibleFlatColumns().length} className='text-center'>\r\n                    No data available\r\n                  </td>\r\n                </tr>\r\n              </tbody>\r\n            ) : (\r\n              <tbody>\r\n                {table\r\n                  .getRowModel()\r\n                  .rows.slice(0, table.getState().pagination.pageSize)\r\n                  .map(row => {\r\n                    return (\r\n                      <tr key={row.id} className={classnames({ selected: row.getIsSelected() })}>\r\n                        {row.getVisibleCells().map((cell, index) => {\r\n                          // Define column widths to match header\r\n                          let width = 'auto'\r\n                          if (cell.column.id === 'select') width = '50px'\r\n                          else if (cell.column.id === 'fullName') width = '30%'\r\n                          else if (cell.column.id === 'role') width = '20%'\r\n                          else if (cell.column.id === 'billing') width = '20%'\r\n                          else if (cell.column.id === 'status') width = '18%'\r\n                          else if (cell.column.id === 'action') width = '120px'\r\n\r\n                          return (\r\n                            <td\r\n                              key={cell.id}\r\n                              className={`px-3 sm:px-4 py-2 sm:py-3 ${cell.column.id === 'action' ? 'pr-6' : ''} ${cell.column.id === 'status' ? 'pr-3' : ''} ${cell.column.id === 'role' ? 'pr-3' : ''} ${cell.column.id === 'billing' ? 'pr-3' : ''}`}\r\n                              style={{ width }}\r\n                            >\r\n                              {flexRender(cell.column.columnDef.cell, cell.getContext())}\r\n                            </td>\r\n                          )\r\n                        })}\r\n                      </tr>\r\n                    )\r\n                  })}\r\n              </tbody>\r\n            )}\r\n            </table>\r\n          </div>\r\n        </div>\r\n        <TablePagination\r\n          component={() => <TablePaginationComponent table={table} />}\r\n          count={table.getFilteredRowModel().rows.length}\r\n          rowsPerPage={table.getState().pagination.pageSize}\r\n          page={table.getState().pagination.pageIndex}\r\n          onPageChange={(_, page) => {\r\n            table.setPageIndex(page)\r\n          }}\r\n        />\r\n        </Card>\r\n      )}\r\n\r\n      <UserDetailsModal\r\n        open={modalOpen}\r\n        onClose={handleModalClose}\r\n        userData={selectedUser}\r\n      />\r\n    </>\r\n  )\r\n}\r\n\r\nexport default UserListTable\r\n"], "names": [], "mappings": ";;;;AAEA,gBAAgB;AAChB;AAEA,eAAe;AACf,+BAA+B;AAC/B,8CAA8C;AAE9C,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gDAAgD;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AAEA,sBAAsB;AACtB;AACA;AACA;AAAA;AAaA,oBAAoB;AACpB;AACA;AAEA;AACA;AACA;AAEA,aAAa;AACb;AACA,0BAA0B;AAC1B;AAEA,cAAc;AACd;AAEA,eAAe;AACf,iDAAiD;AAEjD,gBAAgB;AAChB;AAlEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoEA,qCAAqC;AACrC,MAAM,qBAAqB;IACzB,UAAU;IACV,OAAO;IACP,QAAQ;IACR,SAAS;IACT,YAAY;IACZ,OAAO;QACL,UAAU;QACV,UAAU;QACV,WAAW;IACb;IACA,WAAW;QACT,iBAAiB;IACnB;AACF;AAEA,oBAAoB;AACpB,+BAA+B;AAE/B,MAAM,cAAc,CAAC,KAAK,UAAU,OAAO;IACzC,gBAAgB;IAChB,MAAM,WAAW,CAAA,GAAA,gLAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,QAAQ,CAAC,WAAW;IAElD,0BAA0B;IAC1B,QAAQ;QACN;IACF;IAEA,+CAA+C;IAC/C,OAAO,SAAS,MAAM;AACxB;AAEA,MAAM,iBAAiB,CAAC,EAAE,OAAO,YAAY,EAAE,QAAQ,EAAE,WAAW,GAAG,EAAE,GAAG,OAAO;IACjF,SAAS;IACT,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,SAAS;IACX,GAAG;QAAC;KAAa;IACjB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,UAAU,WAAW;YACzB,SAAS;QACX,GAAG;QAEH,OAAO,IAAM,aAAa;IAC1B,uDAAuD;IACzD,GAAG;QAAC;KAAM;IAEV,qBAAO,8OAAC,gJAAA,CAAA,UAAe;QAAE,GAAG,KAAK;QAAE,OAAO;QAAO,UAAU,CAAA,IAAK,SAAS,EAAE,MAAM,CAAC,KAAK;;;;;;AACzF;AAEA,OAAO;AACP,MAAM,eAAe;IACnB,SAAS;QACP,OAAO;QACP,OAAO;QACP,MAAM;QACN,aAAa;IACf;IACA,WAAW;QACT,OAAO;QACP,OAAO;QACP,MAAM;QACN,aAAa;IACf;IACA,WAAW;QACT,OAAO;QACP,OAAO;QACP,MAAM;QACN,aAAa;IACf;AACF;AAEA,8BAA8B;AAC9B,MAAM,gBAAgB;IACpB;QAAE,OAAO;QAAW,OAAO;QAAW,OAAO;QAAW,MAAM;IAAe;IAC7E;QAAE,OAAO;QAAW,OAAO;QAAW,OAAO;QAAQ,MAAM;IAAa;IACxE;QAAE,OAAO;QAAa,OAAO;QAAa,OAAO;QAAW,MAAM;IAAe;CAClF;AAED,4BAA4B;AAC5B,MAAM,iBAAiB,CAAC,EAAE,aAAa,EAAE,cAAc,EAAE,SAAS,EAAE;IAClE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,SAAS,YAAY,CAAC,cAAc,IAAI,aAAa,OAAO;IAElE,MAAM,qBAAqB,CAAC;QAC1B,QAAQ,GAAG,CAAC,oCAAoC,WAAW,eAAe;QAC1E,eAAe,WAAW;QAC1B,UAAU;IACZ;IAEA,IAAI,CAAC,QAAQ;QACX,kCAAkC;QAClC,qBACE,8OAAC,iJAAA,CAAA,UAAI;YACH,oBAAM,8OAAC;gBAAE,WAAW,GAAG,OAAO,IAAI,CAAC,mBAAmB,CAAC;;;;;;YACvD,OAAO,OAAO,KAAK;YACnB,OAAO,OAAO,KAAK;YACnB,SAAQ;YACR,MAAK;YACL,WAAU;YACV,SAAS,IAAM,UAAU;YACzB,OAAM;YACN,IAAI;gBACF,QAAQ;oBAAE,IAAI;oBAAQ,IAAI;gBAAO;gBACjC,OAAO;oBAAE,IAAI;oBAAS,IAAI;gBAAQ;gBAClC,UAAU;oBAAE,IAAI;oBAAS,IAAI;gBAAQ;gBACrC,UAAU;oBAAE,IAAI;oBAAS,IAAI;gBAAQ;gBACrC,UAAU;oBAAE,IAAI;oBAAW,IAAI;gBAAS;gBACxC,QAAQ;gBACR,YAAY;gBACZ,oBAAoB;oBAClB,SAAS;wBAAE,IAAI;wBAAS,IAAI;oBAAQ;oBACpC,UAAU;wBAAE,IAAI;wBAAU,IAAI;oBAAU;oBACxC,YAAY;oBACZ,YAAY;oBACZ,UAAU;oBACV,cAAc;gBAChB;gBACA,mBAAmB;oBACjB,UAAU;wBAAE,IAAI;wBAAQ,IAAI;oBAAO;oBACnC,YAAY;wBAAE,IAAI;wBAAO,IAAI;oBAAM;oBACnC,aAAa;wBAAE,IAAI;wBAAO,IAAI;oBAAM;gBACtC;gBACA,WAAW;oBACT,WAAW;oBACX,WAAW;gBACb;YACF;;;;;;IAGN;IAEA,6BAA6B;IAC7B,qBACE,8OAAC,+JAAA,CAAA,UAAW;QAAC,MAAK;QAAQ,IAAI;YAAE,UAAU;QAAI;kBAC5C,cAAA,8OAAC,qJAAA,CAAA,UAAM;YACL,OAAO;YACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;YAClD,SAAS,IAAM,UAAU;YACzB,MAAM;YACN,MAAK;YACL,SAAS;YACT,IAAI;gBACF,QAAQ;gBACR,UAAU;gBACV,uBAAuB;oBACrB,SAAS;oBACT,SAAS;oBACT,YAAY;oBACZ,KAAK;gBACP;YACF;sBAEC,cAAc,GAAG,CAAC,CAAC,uBAClB,8OAAC,yJAAA,CAAA,UAAQ;oBAEP,OAAO,OAAO,KAAK;oBACnB,SAAS,IAAM,mBAAmB,OAAO,KAAK;;sCAE9C,8OAAC,iKAAA,CAAA,UAAY;4BAAC,IAAI;gCAAE,UAAU;4BAAkB;sCAC9C,cAAA,8OAAC;gCAAE,WAAW,GAAG,OAAO,IAAI,CAAC,QAAQ,CAAC;;;;;;;;;;;sCAExC,8OAAC,6JAAA,CAAA,UAAU;4BAAC,SAAQ;4BAAQ,IAAI;gCAAE,UAAU;4BAAU;sCACnD,OAAO,KAAK;;;;;;;mBARV,OAAO,KAAK;;;;;;;;;;;;;;;AAe7B;AAIA,qBAAqB;AACrB,MAAM,eAAe,CAAA,GAAA,qKAAA,CAAA,qBAAkB,AAAD;AAEtC,wBAAwB;AACxB,MAAM,oBAAoB,CAAC,EAAE,OAAO,EAAE,aAAa,EAAE,QAAQ,EAAE,cAAc,EAAE;IAC7E,MAAM,SAAS,QAAQ,MAAM,IAAI;IAEjC,qBACE,8OAAC,iJAAA,CAAA,UAAI;QAAC,WAAU;kBACd,cAAA,8OAAC,+JAAA,CAAA,UAAW;YAAC,WAAU;;8BACrB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,6IAAA,CAAA,UAAY;oCAAC,MAAM;8CACjB,QAAQ,QAAQ,EAAE,OAAO,IAAI;;;;;;8CAEhC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,6JAAA,CAAA,UAAU;4CAAC,SAAQ;4CAAK,WAAU;sDAChC,QAAQ,QAAQ;;;;;;sDAEnB,8OAAC,6JAAA,CAAA,UAAU;4CAAC,SAAQ;4CAAQ,OAAM;4CAAiB,WAAU;sDAC1D,QAAQ,KAAK,IAAI,QAAQ,QAAQ;;;;;;;;;;;;;;;;;;sCAIxC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,6JAAA,CAAA,UAAU;oCACT,SAAS,IAAM,cAAc;oCAC7B,OAAM;oCACN,MAAK;oCACL,IAAI;8CAEJ,cAAA,8OAAC;wCAAE,WAAU;;;;;;;;;;;8CAEf,8OAAC,6JAAA,CAAA,UAAU;oCACT,SAAS,IAAM,SAAS,QAAQ,EAAE;oCAClC,OAAM;oCACN,MAAK;oCACL,IAAI;8CAEJ,cAAA,8OAAC;wCAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;8BAKnB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC,6JAAA,CAAA,UAAU;oCAAC,SAAQ;oCAAU,OAAM;oCAAiB,WAAU;8CAAc;;;;;;8CAG7E,8OAAC,6JAAA,CAAA,UAAU;oCAAC,SAAQ;oCAAQ,WAAU;8CACnC,QAAQ,KAAK,IAAI,QAAQ,OAAO,IAAI;;;;;;;;;;;;sCAGzC,8OAAC;;8CACC,8OAAC,6JAAA,CAAA,UAAU;oCAAC,SAAQ;oCAAU,OAAM;oCAAiB,WAAU;8CAAc;;;;;;8CAG7E,8OAAC,6JAAA,CAAA,UAAU;oCAAC,SAAQ;oCAAQ,WAAU;8CACnC,QAAQ,WAAW,IAAI,QAAQ,IAAI,IAAI;;;;;;;;;;;;sCAG5C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,6JAAA,CAAA,UAAU;oCAAC,SAAQ;oCAAU,OAAM;oCAAiB,WAAU;8CAAc;;;;;;8CAG7E,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,eAAe;wCACf,gBAAgB;wCAChB,WAAW,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQrC;AAEA,MAAM,gBAAgB;IACpB,SAAS;IACT,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IAClD,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACnC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACnD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,QAAQ;IACR,MAAM,QAAQ,CAAA,GAAA,8LAAA,CAAA,WAAQ,AAAD;IACrB,MAAM,WAAW,CAAA,GAAA,2JAAA,CAAA,UAAa,AAAD,EAAE,MAAM,WAAW,CAAC,IAAI,CAAC;IACtD,uCAAuC;IAEvC,6BAA6B;IAC7B,MAAM,eAAe;QACnB,IAAI;YACF,QAAQ,GAAG,CAAC;YACZ,WAAW;YACX,SAAS;YAET,MAAM,WAAW,MAAM,CAAA,GAAA,6HAAA,CAAA,gBAAa,AAAD;YACnC,QAAQ,GAAG,CAAC,uBAAuB,SAAS,MAAM,EAAE;YACpD,QAAQ,GAAG,CAAC,sBAAsB,QAAQ,CAAC,EAAE;YAE7C,4EAA4E;YAC5E,MAAM,qBAAqB,SAAS,GAAG,CAAC,CAAA,UAAW,CAAC;oBAClD,GAAG,OAAO;oBACV,QAAQ,QAAQ,MAAM,IAAI;gBAC5B,CAAC;YAED,QAAQ;YACR,gBAAgB;QAClB,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,gDAAgD;IAChD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,uEAAuE;QACvE,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QACxB,QAAQ,GAAG,CAAC;QAEZ;IACF,GAAG,EAAE;IAEL,MAAM,oBAAoB,CAAC;QACzB,gBAAgB;QAChB,aAAa;IACf;IAEA,MAAM,mBAAmB;QACvB,aAAa;QACb,gBAAgB;IAClB;IAEA,MAAM,qBAAqB,OAAO,QAAQ;QACxC,QAAQ,GAAG,CAAC,6BAA6B,QAAQ,OAAO;QAExD,IAAI;YACF,uCAAuC;YACvC,MAAM,CAAA,GAAA,6HAAA,CAAA,sBAAmB,AAAD,EAAE,QAAQ;YAClC,QAAQ,GAAG,CAAC;YAEZ,0DAA0D;YAC1D,QAAQ,CAAA,WACN,SAAS,GAAG,CAAC,CAAA,OACX,KAAK,EAAE,KAAK,SACR;wBAAE,GAAG,IAAI;wBAAE,QAAQ;oBAAU,IAC7B;YAGR,gBAAgB,CAAA,WACd,SAAS,GAAG,CAAC,CAAA,OACX,KAAK,EAAE,KAAK,SACR;wBAAE,GAAG,IAAI;wBAAE,QAAQ;oBAAU,IAC7B;YAIR,QAAQ,GAAG,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,MAAM;QACR;IACF;IAEA,qDAAqD;IACrD,MAAM,qBAAqB;QACzB,aAAa,UAAU,CAAC;QACxB,QAAQ,GAAG,CAAC;IACd;IAEA,MAAM,sBAAsB,OAAO;QACjC,IAAI;YACF,sDAAsD;YACtD,MAAM,UAAU,KAAK,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;YAC9C,MAAM,cAAc,SAAS,YAAY;YAEzC,2BAA2B;YAC3B,MAAM,YAAY,OAAO,OAAO,CAC9B,CAAC,gCAAgC,EAAE,YAAY,4FAA4F,CAAC;YAG9I,IAAI,CAAC,WAAW;gBACd;YACF;YAEA,qCAAqC;YACrC,MAAM,CAAA,GAAA,6HAAA,CAAA,gBAAa,AAAD,EAAE;YAEpB,kCAAkC;YAClC,QAAQ,CAAA,WAAY,SAAS,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;YACxD,gBAAgB,CAAA,WAAY,SAAS,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;YAEhE,kDAAkD;YAClD,gBAAgB,CAAA;gBACd,MAAM,eAAe;oBAAE,GAAG,aAAa;gBAAC;gBACxC,OAAO,YAAY,CAAC,UAAU;gBAC9B,OAAO;YACT;YAEA,uBAAuB;YACvB,MAAM,GAAG,YAAY,+BAA+B,CAAC;QAEvD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,MAAM;QACR;IACF;IAEA,yBAAyB;IACzB,MAAM,sBAAsB;QAC1B,MAAM,eAAe,MAAM,2BAA2B,GAAG,IAAI;QAC7D,OAAO,aAAa,GAAG,CAAC,CAAA,MAAO,IAAI,QAAQ;IAC7C;IAEA,sBAAsB;IACtB,MAAM,sBAAsB;QAC1B,MAAM,eAAe;QAErB,IAAI,aAAa,MAAM,KAAK,GAAG;YAC7B,MAAM;YACN;QACF;QAEA,IAAI;YACF,MAAM,MAAM,IAAI,mJAAA,CAAA,UAAK;YAErB,YAAY;YACZ,IAAI,WAAW,CAAC;YAChB,IAAI,YAAY,CAAC,IAAI,IAAI;YACzB,IAAI,IAAI,CAAC,kCAAkC,IAAI;YAE/C,kBAAkB;YAClB,IAAI,WAAW,CAAC;YAChB,IAAI,YAAY,CAAC,KAAK,KAAK;YAC3B,IAAI,IAAI,CAAC,CAAC,aAAa,EAAE,IAAI,OAAO,kBAAkB,IAAI,EAAE,IAAI;YAChE,IAAI,IAAI,CAAC,CAAC,mBAAmB,EAAE,aAAa,MAAM,EAAE,EAAE,IAAI;YAE1D,qBAAqB;YACrB,MAAM,YAAY,aAAa,GAAG,CAAC,CAAA,UAAW;oBAC5C,QAAQ,QAAQ,IAAI;oBACpB,QAAQ,KAAK,IAAI,QAAQ,QAAQ,IAAI;oBACrC,QAAQ,KAAK,IAAI,QAAQ,OAAO,IAAI;oBACpC,QAAQ,OAAO,IAAI,QAAQ,WAAW,IAAI;oBAC1C,QAAQ,WAAW,IAAI,QAAQ,IAAI,IAAI;oBACvC,YAAY,CAAC,QAAQ,MAAM,IAAI,UAAU,EAAE,SAAS;iBACrD;YAED,qCAAqC;YACrC,IAAI,OAAO,IAAI,SAAS,KAAK,YAAY;gBACvC,0BAA0B;gBAC1B,IAAI,SAAS,CAAC;oBACZ,MAAM;wBAAC;4BAAC;4BAAQ;4BAAS;4BAAW;4BAAW;4BAAgB;yBAAS;qBAAC;oBACzE,MAAM;oBACN,QAAQ;oBACR,QAAQ;wBACN,UAAU;wBACV,aAAa;oBACf;oBACA,YAAY;wBACV,WAAW;4BAAC;4BAAI;4BAAK;yBAAI;wBACzB,WAAW;wBACX,WAAW;oBACb;oBACA,oBAAoB;wBAClB,WAAW;4BAAC;4BAAK;4BAAK;yBAAI;oBAC5B;gBACF;YACF,OAAO,IAAI,OAAO,cAAc,YAAY;gBAC1C,8BAA8B;gBAC9B,UAAU,KAAK;oBACb,MAAM;wBAAC;4BAAC;4BAAQ;4BAAS;4BAAW;4BAAW;4BAAgB;yBAAS;qBAAC;oBACzE,MAAM;oBACN,QAAQ;oBACR,QAAQ;wBACN,UAAU;wBACV,aAAa;oBACf;oBACA,YAAY;wBACV,WAAW;4BAAC;4BAAI;4BAAK;yBAAI;wBACzB,WAAW;wBACX,WAAW;oBACb;oBACA,oBAAoB;wBAClB,WAAW;4BAAC;4BAAK;4BAAK;yBAAI;oBAC5B;gBACF;YACF,OAAO;gBACL,kCAAkC;gBAClC,IAAI,YAAY;gBAChB,MAAM,aAAa;gBAEnB,cAAc;gBACd,IAAI,WAAW,CAAC;gBAChB,IAAI,OAAO,CAAC,WAAW;gBACvB,IAAI,IAAI,CAAC,QAAQ,IAAI;gBACrB,IAAI,IAAI,CAAC,SAAS,IAAI;gBACtB,IAAI,IAAI,CAAC,WAAW,KAAK;gBACzB,IAAI,IAAI,CAAC,WAAW,KAAK;gBACzB,IAAI,IAAI,CAAC,QAAQ,KAAK;gBAEtB,aAAa,aAAa;gBAE1B,gBAAgB;gBAChB,IAAI,OAAO,CAAC,WAAW;gBACvB,UAAU,OAAO,CAAC,CAAA;oBAChB,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,SAAS,CAAC,GAAG,KAAK,IAAI;oBACtC,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,SAAS,CAAC,GAAG,KAAK,IAAI;oBACtC,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,SAAS,CAAC,GAAG,KAAK,KAAK;oBACvC,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,SAAS,CAAC,GAAG,KAAK,KAAK;oBACvC,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,SAAS,CAAC,GAAG,KAAK,KAAK;oBACvC,aAAa;oBAEb,yBAAyB;oBACzB,IAAI,YAAY,KAAK;wBACnB,IAAI,OAAO;wBACX,YAAY;oBACd;gBACF;YACF;YAEA,eAAe;YACf,IAAI,IAAI,CAAC,CAAC,uBAAuB,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC;QAEjF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;YACnC,MAAM;QACR;IACF;IAEA,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EACpB,IAAM;YACJ;gBACE,IAAI;gBACJ,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,yJAAA,CAAA,UAAQ;wBAEL,SAAS,MAAM,oBAAoB;wBACnC,eAAe,MAAM,qBAAqB;wBAC1C,UAAU,MAAM,+BAA+B;;;;;;gBAIrD,MAAM,CAAC,EAAE,GAAG,EAAE,iBACZ,8OAAC,yJAAA,CAAA,UAAQ;wBAEL,SAAS,IAAI,aAAa;wBAC1B,UAAU,CAAC,IAAI,YAAY;wBAC3B,eAAe,IAAI,iBAAiB;wBACpC,UAAU,IAAI,wBAAwB;;;;;;YAI9C;YACA,aAAa,QAAQ,CAAC,YAAY;gBAChC,QAAQ;gBACR,MAAM,CAAC,EAAE,GAAG,EAAE,iBACZ,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACZ,UAAU;oCAAE,QAAQ,IAAI,QAAQ,CAAC,MAAM;oCAAE,UAAU,IAAI,QAAQ,CAAC,QAAQ;gCAAC;;;;;;0CAE5E,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,6JAAA,CAAA,UAAU;wCACT,OAAM;wCACN,WAAU;wCACV,OAAO,IAAI,QAAQ,CAAC,QAAQ;kDAE3B,IAAI,QAAQ,CAAC,QAAQ;;;;;;kDAExB,8OAAC,6JAAA,CAAA,UAAU;wCACT,SAAQ;wCACR,OAAM;wCACN,WAAU;wCACV,OAAO;4CAAE,eAAe;wCAAQ;wCAChC,OAAO,IAAI,QAAQ,CAAC,KAAK,IAAI,IAAI,QAAQ,CAAC,QAAQ;kDAEjD,IAAI,QAAQ,CAAC,KAAK,IAAI,IAAI,QAAQ,CAAC,QAAQ;;;;;;;;;;;;;;;;;;YAKtD;YACA,aAAa,QAAQ,CAAC,QAAQ;gBAC5B,QAAQ;gBACR,MAAM,CAAC,EAAE,GAAG,EAAE,iBACZ,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,6JAAA,CAAA,UAAU;4BACT,OAAM;4BACN,WAAU;4BACV,OAAO,IAAI,QAAQ,CAAC,KAAK,IAAI,IAAI,QAAQ,CAAC,OAAO,IAAI;sCAEpD,IAAI,QAAQ,CAAC,KAAK,IAAI,IAAI,QAAQ,CAAC,OAAO,IAAI;;;;;;;;;;;gBAIrD,eAAe;YACjB;YAEA,aAAa,QAAQ,CAAC,WAAW;gBAC/B,QAAQ;gBACR,MAAM,CAAC,EAAE,GAAG,EAAE,iBACZ,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,6JAAA,CAAA,UAAU;4BACT,OAAM;4BACN,WAAU;4BACV,OAAO;gCACL,YAAY;gCACZ,UAAU;gCACV,YAAY;4BACd;4BACA,OAAO,IAAI,QAAQ,CAAC,WAAW,IAAI,IAAI,QAAQ,CAAC,IAAI,IAAI;sCAEvD,IAAI,QAAQ,CAAC,WAAW,IAAI,IAAI,QAAQ,CAAC,IAAI,IAAI;;;;;;;;;;;gBAIxD,eAAe;YACjB;YACA,aAAa,QAAQ,CAAC,UAAU;gBAC9B,QAAQ;gBACR,MAAM,CAAC,EAAE,GAAG,EAAE;oBACZ,MAAM,SAAS,IAAI,QAAQ,CAAC,MAAM,IAAI;oBAEtC,qBACE,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,eAAe;4BACf,gBAAgB;4BAChB,WAAW,IAAI,QAAQ,CAAC,EAAE;;;;;;;;;;;gBAIlC;gBACA,eAAe;YACjB;YAEA,aAAa,QAAQ,CAAC,UAAU;gBAC9B,QAAQ;gBACR,MAAM,CAAC,EAAE,GAAG,EAAE,iBACZ,8OAAC;wBAAI,WAAU;wBAAqE,OAAO;4BAAE,YAAY;wBAAE;;0CAEzG,8OAAC,6JAAA,CAAA,UAAU;gCACT,SAAS,IAAM,kBAAkB,IAAI,QAAQ;gCAC7C,OAAM;gCACN,MAAK;gCACL,IAAI;0CAEJ,cAAA,8OAAC;oCAAE,WAAU;;;;;;;;;;;0CAIf,8OAAC,6JAAA,CAAA,UAAU;gCACT,SAAS,IAAM,oBAAoB,IAAI,QAAQ,CAAC,EAAE;gCAClD,OAAM;gCACN,MAAK;gCACL,IAAI;0CAEJ,cAAA,8OAAC;oCAAE,WAAU;;;;;;;;;;;;;;;;;gBAInB,eAAe;YACjB;SACD,EACD,uDAAuD;IACvD;QAAC;QAAM;KAAa;IAGtB,MAAM,QAAQ,CAAA,GAAA,sLAAA,CAAA,gBAAa,AAAD,EAAE;QAC1B,MAAM;QACN;QACA,WAAW;YACT,OAAO;QACT;QACA,OAAO;YACL;YACA;QACF;QACA,cAAc;YACZ,YAAY;gBACV,UAAU;YACZ;QACF;QACA,oBAAoB;QACpB,qGAAqG;QACrG,gBAAgB;QAChB,sBAAsB;QACtB,iBAAiB,CAAA,GAAA,qKAAA,CAAA,kBAAe,AAAD;QAC/B,sBAAsB;QACtB,qBAAqB,CAAA,GAAA,qKAAA,CAAA,sBAAmB,AAAD;QACvC,mBAAmB,CAAA,GAAA,qKAAA,CAAA,oBAAiB,AAAD;QACnC,uBAAuB,CAAA,GAAA,qKAAA,CAAA,wBAAqB,AAAD;QAC3C,oBAAoB,CAAA,GAAA,qKAAA,CAAA,qBAAkB,AAAD;QACrC,wBAAwB,CAAA,GAAA,qKAAA,CAAA,yBAAsB,AAAD;QAC7C,wBAAwB,CAAA,GAAA,qKAAA,CAAA,yBAAsB,AAAD;IAC/C;IAEA,MAAM,YAAY,CAAA;QAChB,MAAM,EAAE,QAAQ,EAAE,GAAG;QAErB,qBAAO,8OAAC,6IAAA,CAAA,UAAY;YAAC,MAAM;sBAAK,UAAU,OAAO,IAAI;;;;;;IACvD;IAEA,qBAAqB;IACrB,IAAI,SAAS;QACX,qBACE,8OAAC,iJAAA,CAAA,UAAI;;8BACH,8OAAC,6JAAA,CAAA,UAAU;oBAAC,OAAM;oBAAe,WAAU;;;;;;8BAC3C,8OAAC,+JAAA,CAAA,UAAW;oBAAC,WAAU;8BACrB,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,yKAAA,CAAA,UAAgB;;;;;0CACjB,8OAAC,6JAAA,CAAA,UAAU;gCAAC,SAAQ;gCAAQ,OAAM;0CAAiB;;;;;;;;;;;;;;;;;;;;;;;IAO7D;IAEA,mBAAmB;IACnB,IAAI,OAAO;QACT,qBACE,8OAAC,iJAAA,CAAA,UAAI;;8BACH,8OAAC,6JAAA,CAAA,UAAU;oBAAC,OAAM;oBAAe,WAAU;;;;;;8BAC3C,8OAAC,+JAAA,CAAA,UAAW;;sCACV,8OAAC,mJAAA,CAAA,UAAK;4BAAC,UAAS;4BAAQ,WAAU;sCAC/B;;;;;;sCAEH,8OAAC,qJAAA,CAAA,UAAM;4BACL,SAAQ;4BACR,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;4BACrC,yBAAW,8OAAC;gCAAE,WAAU;;;;;;sCACzB;;;;;;;;;;;;;;;;;;IAMT;IAEA,mBAAmB;IACnB,IAAI,CAAC,QAAQ,KAAK,MAAM,KAAK,GAAG;QAC9B,qBACE,8OAAC,iJAAA,CAAA,UAAI;;8BACH,8OAAC,6JAAA,CAAA,UAAU;oBAAC,OAAM;oBAAe,WAAU;;;;;;8BAC3C,8OAAC,+JAAA,CAAA,UAAW;oBAAC,WAAU;8BACrB,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;;;;;;0CACb,8OAAC,6JAAA,CAAA,UAAU;gCAAC,SAAQ;gCAAK,OAAM;0CAAiB;;;;;;0CAGhD,8OAAC,6JAAA,CAAA,UAAU;gCAAC,SAAQ;gCAAQ,OAAM;0CAAiB;;;;;;;;;;;;;;;;;;;;;;;IAO7D;IAEA,qBACE;;YACG,WACC,cAAc;0BACd,8OAAC,iJAAA,CAAA,UAAI;gBAAC,WAAU;;kCACd,8OAAC,6JAAA,CAAA,UAAU;wBACT,OAAM;wBACN,WAAU;wBACV,WAAU;;;;;;kCAEZ,8OAAC,+JAAA,CAAA,UAAW;wBAAC,WAAU;;0CACrB,8OAAC,qJAAA,CAAA,UAAY;gCAAC,SAAS;gCAAiB,WAAW;;;;;;0CAEnD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,OAAO,gBAAgB;wCACvB,UAAU,CAAA,QAAS,gBAAgB,OAAO;wCAC1C,aAAY;wCACZ,WAAU;wCACV,MAAK;;;;;;oCAIN,OAAO,IAAI,CAAC,cAAc,MAAM,GAAG,mBAClC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;;;;;;kEACb,8OAAC,6JAAA,CAAA,UAAU;wDAAC,SAAQ;wDAAQ,WAAU;;4DACnC,OAAO,IAAI,CAAC,cAAc,MAAM;4DAAC;4DAAS,OAAO,IAAI,CAAC,cAAc,MAAM,KAAK,IAAI,MAAM;4DAAG;;;;;;;;;;;;;0DAGjG,8OAAC,qJAAA,CAAA,UAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,SAAS,IAAM,gBAAgB,CAAC;gDAChC,WAAU;0DACX;;;;;;;;;;;;kDAML,8OAAC,qJAAA,CAAA,UAAM;wCACL,OAAM;wCACN,SAAQ;wCACR,yBAAW,8OAAC;4CAAE,WAAU;;;;;;wCACxB,WAAU;wCACV,MAAK;wCACL,SAAS;wCACT,UAAU,OAAO,IAAI,CAAC,cAAc,MAAM,KAAK;;4CAChD;4CACc,OAAO,IAAI,CAAC,cAAc,MAAM;4CAAC;;;;;;;kDAGhD,8OAAC,6JAAA,CAAA,UAAU;wCACT,OAAM;wCACN,SAAS;wCACT,UAAU;wCACV,OAAO,UAAU,eAAe;wCAChC,IAAI;4CACF,QAAQ;4CACR,aAAa;4CACb,OAAO;4CACP,QAAQ;4CACR,cAAc;4CACd,WAAW;gDACT,iBAAiB;gDACjB,WAAW;4CACb;4CACA,YAAY;wCACd;kDAEA,cAAA,8OAAC;4CAAE,WAAW,CAAC,eAAe,EAAE,UAAU,iBAAiB,IAAI;;;;;;;;;;;;;;;;;0CAInE,8OAAC,+IAAA,CAAA,UAAG;gCAAC,IAAI;oCAAE,0BAA0B;wCAAE,cAAc;oCAAE;gCAAE;0CACtD,MAAM,mBAAmB,GAAG,IAAI,CAAC,MAAM,KAAK,kBAC3C,8OAAC,iJAAA,CAAA,UAAI;oCAAC,WAAU;8CACd,cAAA,8OAAC,6JAAA,CAAA,UAAU;wCAAC,OAAM;kDAAiB;;;;;;;;;;2CAGrC,MACG,WAAW,GACX,IAAI,CAAC,KAAK,CAAC,GAAG,MAAM,QAAQ,GAAG,UAAU,CAAC,QAAQ,EAClD,GAAG,CAAC,CAAA,oBACH,8OAAC;wCAEC,SAAS,IAAI,QAAQ;wCACrB,eAAe;wCACf,UAAU;wCACV,gBAAgB;uCAJX,IAAI,EAAE;;;;;;;;;;0CAUrB,8OAAC,+IAAA,CAAA,UAAG;gCAAC,WAAU;0CACb,cAAA,8OAAC,uKAAA,CAAA,UAAe;oCACd,WAAW,kBAAM,8OAAC,8IAAA,CAAA,UAAwB;4CAAC,OAAO;;;;;;oCAClD,OAAO,MAAM,mBAAmB,GAAG,IAAI,CAAC,MAAM;oCAC9C,aAAa,MAAM,QAAQ,GAAG,UAAU,CAAC,QAAQ;oCACjD,MAAM,MAAM,QAAQ,GAAG,UAAU,CAAC,SAAS;oCAC3C,cAAc,CAAC,GAAG;wCAChB,MAAM,YAAY,CAAC;oCACrB;;;;;;;;;;;;;;;;;;;;;;uBAMR,eAAe;0BACf,8OAAC,iJAAA,CAAA,UAAI;gBAAC,WAAU;;kCACd,8OAAC,6JAAA,CAAA,UAAU;wBACT,qBACE,8OAAC,6JAAA,CAAA,UAAU;4BAAC,SAAQ;4BAAK,WAAU;sCAAiC;;;;;;wBAItE,yBACE,8OAAC,6JAAA,CAAA,UAAU;4BAAC,SAAQ;4BAAQ,WAAU;sCAAqB;;;;;;wBAI7D,WAAU;;;;;;kCAEZ,8OAAC,qJAAA,CAAA,UAAY;wBAAC,SAAS;wBAAiB,WAAW;;;;;;kCACrD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,gJAAA,CAAA,UAAe;wCACd,MAAM;wCACN,OAAO,MAAM,QAAQ,GAAG,UAAU,CAAC,QAAQ;wCAC3C,UAAU,CAAA,IAAK,MAAM,WAAW,CAAC,OAAO,EAAE,MAAM,CAAC,KAAK;wCACtD,WAAU;wCACV,MAAK;;0DAEL,8OAAC,yJAAA,CAAA,UAAQ;gDAAC,OAAM;0DAAK;;;;;;0DACrB,8OAAC,yJAAA,CAAA,UAAQ;gDAAC,OAAM;0DAAK;;;;;;0DACrB,8OAAC,yJAAA,CAAA,UAAQ;gDAAC,OAAM;0DAAK;;;;;;;;;;;;kDAEvB,8OAAC,6JAAA,CAAA,UAAU;wCAAC,SAAQ;wCAAQ,OAAM;wCAAiB,WAAU;kDAAkB;;;;;;oCAK9E,OAAO,IAAI,CAAC,cAAc,MAAM,GAAG,mBAClC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;;;;;;0DACb,8OAAC,6JAAA,CAAA,UAAU;gDAAC,SAAQ;gDAAU,WAAU;;oDACrC,OAAO,IAAI,CAAC,cAAc,MAAM;oDAAC;;;;;;;0DAEpC,8OAAC,qJAAA,CAAA,UAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,SAAS,IAAM,gBAAgB,CAAC;gDAChC,WAAU;gDACV,IAAI;oDAAE,UAAU;oDAAU,SAAS;gDAAU;0DAC9C;;;;;;;;;;;;;;;;;;0CAMP,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,OAAO,gBAAgB;wCACvB,UAAU,CAAA,QAAS,gBAAgB,OAAO;wCAC1C,aAAY;wCACZ,WAAU;wCACV,MAAK;;;;;;kDAEP,8OAAC,qJAAA,CAAA,UAAM;wCACL,OAAM;wCACN,SAAQ;wCACR,yBAAW,8OAAC;4CAAE,WAAU;;;;;;wCACxB,WAAU;wCACV,MAAK;wCACL,SAAS;wCACT,UAAU,OAAO,IAAI,CAAC,cAAc,MAAM,KAAK;;4CAChD;4CACc,OAAO,IAAI,CAAC,cAAc,MAAM;4CAAC;;;;;;;kDAGhD,8OAAC,6JAAA,CAAA,UAAU;wCACT,OAAM;wCACN,SAAS;wCACT,UAAU;wCACV,OAAO,UAAU,eAAe;wCAChC,IAAI;4CACF,QAAQ;4CACR,aAAa;4CACb,WAAW;gDACT,iBAAiB;gDACjB,WAAW;4CACb;4CACA,YAAY;wCACd;kDAEA,cAAA,8OAAC;4CAAE,WAAW,CAAC,eAAe,EAAE,UAAU,iBAAiB,IAAI;;;;;;;;;;;;;;;;;;;;;;;kCAIrE,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAM,WAAW,GAAG,4IAAA,CAAA,UAAW,CAAC,KAAK,CAAC,mBAAmB,CAAC;;kDAC3D,8OAAC;kDACE,MAAM,eAAe,GAAG,GAAG,CAAC,CAAA,4BAC3B,8OAAC;0DACE,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ;oDAChC,uBAAuB;oDACvB,IAAI,QAAQ;oDACZ,IAAI,OAAO,EAAE,KAAK,UAAU,QAAQ;yDAC/B,IAAI,OAAO,EAAE,KAAK,YAAY,QAAQ;yDACtC,IAAI,OAAO,EAAE,KAAK,QAAQ,QAAQ;yDAClC,IAAI,OAAO,EAAE,KAAK,WAAW,QAAQ;yDACrC,IAAI,OAAO,EAAE,KAAK,UAAU,QAAQ;yDACpC,IAAI,OAAO,EAAE,KAAK,UAAU,QAAQ;oDAEzC,qBACE,8OAAC;wDAEC,WAAW,CAAC,0BAA0B,EAAE,OAAO,EAAE,KAAK,WAAW,SAAS,GAAG,CAAC,EAAE,OAAO,EAAE,KAAK,WAAW,SAAS,GAAG,CAAC,EAAE,OAAO,EAAE,KAAK,SAAS,SAAS,GAAG,CAAC,EAAE,OAAO,EAAE,KAAK,YAAY,SAAS,IAAI;wDACrM,OAAO;4DAAE;wDAAM;kEAEd,OAAO,aAAa,GAAG,qBACtB;sEACE,cAAA,8OAAC;gEACC,WAAW,CAAA,GAAA,mIAAA,CAAA,UAAU,AAAD,EAAE;oEACpB,qBAAqB,OAAO,MAAM,CAAC,WAAW;oEAC9C,8BAA8B,OAAO,MAAM,CAAC,UAAU;gEACxD;gEACA,SAAS,OAAO,MAAM,CAAC,UAAU,KAAK,OAAO,MAAM,CAAC,uBAAuB,KAAK;;kFAEhF,8OAAC,6JAAA,CAAA,UAAU;wEACT,SAAQ;wEACR,WAAU;wEACV,OAAM;kFAEL,CAAA,GAAA,sLAAA,CAAA,aAAU,AAAD,EAAE,OAAO,MAAM,CAAC,SAAS,CAAC,MAAM,EAAE,OAAO,UAAU;;;;;;oEAE9D,OAAO,MAAM,CAAC,UAAU,MAAM,CAC7B,CAAA;wEACE,mBAAK,8OAAC;4EAAE,WAAU;;;;;;wEAClB,oBAAM,8OAAC;4EAAE,WAAU;;;;;;oEACrB,CAAA,CAAC,CAAC,OAAO,MAAM,CAAC,WAAW,GAAG,IAAI,IACpC;;;;;;;;uDAzBD,OAAO,EAAE;;;;;gDA+BpB;+CA5CO,YAAY,EAAE;;;;;;;;;;oCAgD1B,MAAM,mBAAmB,GAAG,IAAI,CAAC,MAAM,KAAK,kBAC3C,8OAAC;kDACC,cAAA,8OAAC;sDACC,cAAA,8OAAC;gDAAG,SAAS,MAAM,qBAAqB,GAAG,MAAM;gDAAE,WAAU;0DAAc;;;;;;;;;;;;;;;6DAM/E,8OAAC;kDACE,MACE,WAAW,GACX,IAAI,CAAC,KAAK,CAAC,GAAG,MAAM,QAAQ,GAAG,UAAU,CAAC,QAAQ,EAClD,GAAG,CAAC,CAAA;4CACH,qBACE,8OAAC;gDAAgB,WAAW,CAAA,GAAA,mIAAA,CAAA,UAAU,AAAD,EAAE;oDAAE,UAAU,IAAI,aAAa;gDAAG;0DACpE,IAAI,eAAe,GAAG,GAAG,CAAC,CAAC,MAAM;oDAChC,uCAAuC;oDACvC,IAAI,QAAQ;oDACZ,IAAI,KAAK,MAAM,CAAC,EAAE,KAAK,UAAU,QAAQ;yDACpC,IAAI,KAAK,MAAM,CAAC,EAAE,KAAK,YAAY,QAAQ;yDAC3C,IAAI,KAAK,MAAM,CAAC,EAAE,KAAK,QAAQ,QAAQ;yDACvC,IAAI,KAAK,MAAM,CAAC,EAAE,KAAK,WAAW,QAAQ;yDAC1C,IAAI,KAAK,MAAM,CAAC,EAAE,KAAK,UAAU,QAAQ;yDACzC,IAAI,KAAK,MAAM,CAAC,EAAE,KAAK,UAAU,QAAQ;oDAE9C,qBACE,8OAAC;wDAEC,WAAW,CAAC,0BAA0B,EAAE,KAAK,MAAM,CAAC,EAAE,KAAK,WAAW,SAAS,GAAG,CAAC,EAAE,KAAK,MAAM,CAAC,EAAE,KAAK,WAAW,SAAS,GAAG,CAAC,EAAE,KAAK,MAAM,CAAC,EAAE,KAAK,SAAS,SAAS,GAAG,CAAC,EAAE,KAAK,MAAM,CAAC,EAAE,KAAK,YAAY,SAAS,IAAI;wDACzN,OAAO;4DAAE;wDAAM;kEAEd,CAAA,GAAA,sLAAA,CAAA,aAAU,AAAD,EAAE,KAAK,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,KAAK,UAAU;uDAJlD,KAAK,EAAE;;;;;gDAOlB;+CApBO,IAAI,EAAE;;;;;wCAuBnB;;;;;;;;;;;;;;;;;;;;;;kCAMV,8OAAC,uKAAA,CAAA,UAAe;wBACd,WAAW,kBAAM,8OAAC,8IAAA,CAAA,UAAwB;gCAAC,OAAO;;;;;;wBAClD,OAAO,MAAM,mBAAmB,GAAG,IAAI,CAAC,MAAM;wBAC9C,aAAa,MAAM,QAAQ,GAAG,UAAU,CAAC,QAAQ;wBACjD,MAAM,MAAM,QAAQ,GAAG,UAAU,CAAC,SAAS;wBAC3C,cAAc,CAAC,GAAG;4BAChB,MAAM,YAAY,CAAC;wBACrB;;;;;;;;;;;;0BAKJ,8OAAC,yJAAA,CAAA,UAAgB;gBACf,MAAM;gBACN,SAAS;gBACT,UAAU;;;;;;;;AAIlB;uCAEe", "debugId": null}}]}