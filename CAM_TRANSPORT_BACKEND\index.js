const express = require('express');
const cors = require('cors');
const ConnectDB = require('./config/db');
const path = require('path');
const fs = require('fs');

const swaggerUi = require('swagger-ui-express');
const swaggerSpec = require('./config/swagger');

const ContactRouter = require('./routes/contact');
const JobRouter = require('./routes/jobs');
const UrgentRouter = require('./routes/urgent_Inquiry');
const ApiLog = require('./model/ApiLog');
const api_key_auth = require('./middleware/api_key_auth');
const LoginRoute = require('./routes/Login');
const QuoteRouter = require('./routes/quote');
const UserProfileRouter = require('./routes/userProfile');
const MFARoute = require('./routes/MFA');
const { CleanupExpiredOTPs } = require('./controller/Login');
require('dotenv').config();

const app = express();

// CORS configuration
app.use(cors({
    origin: ['http://localhost:3000', 'http://127.0.0.1:3000'],
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'x-api-key']
}));

app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Serve static files (resumes) from uploads directory
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));
app.use('/uploads/resumes', express.static(path.join(__dirname, 'uploads/resumes')));

// Middleware to log API request hit and leave times
app.use(async (req, res, next) => {
    const hitTime = new Date();
    const ipAddress = req.ip;

    // Create a new log entry when the request hits
    const apiLog = new ApiLog({
        method: req.method,
        originalUrl: req.originalUrl,
        ipAddress: ipAddress,
        hitTime: hitTime
    });
    await apiLog.save();


    res.on('finish', async () => {
        const leaveTime = new Date();
        const duration = leaveTime.getTime() - hitTime.getTime();

        // Update the log entry when the response finishes
        apiLog.leaveTime = leaveTime;
        apiLog.statusCode = res.statusCode;
        apiLog.duration = duration;
        await apiLog.save();

    });

    next();
});

app.use('/api', swaggerUi.serve, swaggerUi.setup(swaggerSpec));

app.use('/contact', ContactRouter);
app.use('/jobs', JobRouter);
app.use('/urgent', UrgentRouter)
app.use('/quote', QuoteRouter)
app.use('/api-logs', api_key_auth, require('./routes/apiLog'));
app.use('/login', LoginRoute)
app.use('/user-profile', UserProfileRouter)
app.use('/mfa', MFARoute)

// Add direct route for OTP verification
const { VerifyOTP } = require('./controller/Login');
app.get('/verify-otp/:userId/:otp', VerifyOTP);

app.get('/', (req, res) => {
    res.json({
        message: 'WELCOME TO THE CAM TRANSPORT INQUIRY AREA!'
    })
})

// Global Error Handling Middleware
app.use((err, req, res, next) => {
    console.error(err.stack);

    if (err.code === 11000) { // MongoDB duplicate key error
        const field = Object.keys(err.keyPattern)[0];
        const value = err.keyValue[field];
        return res.status(409).json({
            message: `Duplicate field value: ${field} already exists with ${value}. Please use another value.`
        });
    }

    // Handle Mongoose validation errors (if not handled specifically in controllers)
    if (err.name === 'ValidationError') {
        const errors = Object.values(err.errors).map(el => el.message);
        return res.status(400).json({ message: errors.join(', ') });
    }

    res.status(err.statusCode || 500).json({
        message: err.message || 'Internal Server Error'
    });
});

const PORT = process.env.PORT || 8090
app.listen(PORT, () => {
    console.log(`Listening on port ${PORT}`);
    ConnectDB();

    // Start automatic OTP cleanup - runs every 5 minutes
    console.log('🧹 Starting automatic OTP cleanup service...');
    setInterval(async () => {
        try {
            await CleanupExpiredOTPs();
        } catch (error) {
            console.error('❌ Scheduled OTP cleanup failed:', error);
        }
    }, 5 * 60 * 1000); // 5 minutes in milliseconds

    console.log('✅ OTP cleanup service started (runs every 5 minutes)');
})
