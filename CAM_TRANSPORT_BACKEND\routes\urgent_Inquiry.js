const { Router } = require('express');
const { UrgentInquiry, GetAllInquiries, DeleteInquiry } = require('../controller/urgent_Inquiry');
const uploadUrgent = require('../service/Urgent');
const rateLimit = require('express-rate-limit');
const Urgent = require('../model/urgent_Inquiry');
const { checkUrgentEmail24hBlock, checkUrgentIp24hBlock } = require('../utils/rateLimitHelpers');

const UrgentRouter = Router();

const urgentRateLimiter = rateLimit({
    windowMs: 15 * 60 * 1000,
    max: 15,
    message: 'Too many urgent inquiries from this IP, please try again later',
});

/**
 * @swagger
 * /urgent:
 *   post:
 *     summary: Submit an urgent inquiry
 *     description: Submit an urgent inquiry with optional document attachments and conditional fields.
 *     consumes:
 *       - multipart/form-data
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             required:
 *               - full_name
 *               - email
 *               - phone_number
 *               - urgency_type
 *               - brief_description
 *             properties:
 *               full_name:
 *                 type: string
 *               email:
 *                 type: string
 *               phone_number:
 *                 type: string
 *               urgency_type:
 *                 type: string
 *                 description: The type of urgency (e.g., Delivery Issue, Shipment Delay, Other)
 *               other_urgency:
 *                 type: string
 *                 description: Provide if urgency_type is "Other"
 *               ref_number:
 *                 type: string
 *                 description: Required only for Delivery Issue or Shipment Delay
 *               brief_description:
 *                 type: string
 *               documents:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: binary
 *                 description: Upload any related documents (.pdf, .png, .jpeg, etc.)
 *     responses:
 *       200:
 *         description: Urgent inquiry submitted successfully
 *       400:
 *         description: Bad request or missing required fields
 */


UrgentRouter.post('/', urgentRateLimiter, uploadUrgent.single('documents'), UrgentInquiry)

UrgentRouter.get('/get-urgents', GetAllInquiries)

UrgentRouter.delete('/delete-urgent/:id', DeleteInquiry)

// Update urgent inquiry status
UrgentRouter.patch('/update-status/:inquiryId', async (req, res) => {
    try {
        const { inquiryId } = req.params;
        const { status } = req.body;

        // Validate status
        const validStatuses = ['pending', 'in-view', 'completed'];
        if (!validStatuses.includes(status)) {
            return res.status(400).json({
                success: false,
                message: 'Invalid status. Must be one of: ' + validStatuses.join(', ')
            });
        }

        // Update inquiry in database
        const updatedInquiry = await Urgent.findByIdAndUpdate(
            inquiryId,
            {
                status: status,
                updatedAt: new Date()
            },
            { new: true }
        );

        if (!updatedInquiry) {
            return res.status(404).json({
                success: false,
                message: 'Urgent inquiry not found'
            });
        }

        res.json({
            success: true,
            message: 'Urgent inquiry status updated successfully',
            inquiry: updatedInquiry
        });

    } catch (error) {
        console.error('Error updating urgent inquiry status:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error.message
        });
    }
})

// GET remaining global attempts for urgent inquiry form in 15 minutes
UrgentRouter.get('/user-limit', async (req, res) => {
    const now = new Date();
    const fifteenMinutesAgo = new Date(now.getTime() - 15 * 60 * 1000);
    const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

    const recentGlobalCount = await Urgent.countDocuments({ createdAt: { $gte: fifteenMinutesAgo } });
    const remaining = Math.max(0, 15 - recentGlobalCount);

    const recentUrgentsIn24h = await Urgent.find({ createdAt: { $gte: oneDayAgo } }).sort({ createdAt: 1 });
    const recentUrgentsIn15m = await Urgent.find({ createdAt: { $gte: fifteenMinutesAgo } }).sort({ createdAt: 1 });

    // --- Per-Email 24-hour Limit Blocked Emails (15 submissions) - NOT RETURNED ---
    // const blocked24hEmails = [];
    // const email24hMap = new Map();
    // for (const urgent of recentUrgentsIn24h) {
    //     if (!email24hMap.has(urgent.email)) {
    //         email24hMap.set(urgent.email, []);
    //     }
    //     email24hMap.get(urgent.email).push(urgent.createdAt);
    // }
    // for (const [email, dates] of email24hMap.entries()) {
    //     const { isBlocked, unblockDate } = await checkUrgentEmail24hBlock(email, now, oneDayAgo);
    //     if (isBlocked) {
    //         blocked24hEmails.push({
    //             email,
    //             unblockDate: unblockDate.toLocaleString(),
    //             reason: `Exceeded 15 urgent inquiries in 24 hours`
    //         });
    //     }
    // }

    // --- IP-based 24-hour Limit Blocked IPs (5 submissions) ---
    const blockedIPs = []; // Renamed from blocked24hIPs
    const ip24hMap = new Map();
    for (const urgent of recentUrgentsIn24h) {
        if (!ip24hMap.has(urgent.ip)) {
            ip24hMap.set(urgent.ip, []);
        }
        ip24hMap.get(urgent.ip).push(urgent.createdAt);
    }
    for (const [ip, dates] of ip24hMap.entries()) {
        const { isBlocked, unblockDate } = await checkUrgentIp24hBlock(ip, now, oneDayAgo);
        if (isBlocked) {
            blockedIPs.push({
                ip,
                unblockDate: unblockDate.toLocaleString(),
                reason: `Exceeded 5 urgent inquiries from this IP in 24 hours`
            });
        }
    }

    // --- Per-Email 15-minute Blocked Emails (3 submissions) ---
    const blocked15mEmails = [];
    const email15mMap = new Map();
    for (const urgent of recentUrgentsIn15m) {
        if (!email15mMap.has(urgent.email)) {
            email15mMap.set(urgent.email, 0);
        }
        email15mMap.set(urgent.email, email15mMap.get(urgent.email) + 1);
    }
    for (const [email, count] of email15mMap.entries()) {
        if (count >= 3) {
            blocked15mEmails.push({
                email,
                currentSubmissions: count,
                reason: `Blocked: Exceeded 3 urgent inquiries in 15 minutes`
            });
        }
    }

    // --- IP-based 15-minute Blocked IPs (3 submissions) - NOT RETURNED ---
    // const blocked15mIPs = [];
    // const ip15mMap = new Map();
    // for (const urgent of recentUrgentsIn15m) {
    //     if (!ip15mMap.has(urgent.ip)) {
    //         ip15mMap.set(urgent.ip, 0);
    //     }
    //     ip15mMap.set(urgent.ip, ip15mMap.get(urgent.ip) + 1);
    // }
    // for (const [ip, count] of ip15mMap.entries()) {
    //     if (count >= 3) {
    //         blocked15mIPs.push({
    //             ip,
    //             currentSubmissions: count,
    //             reason: `Blocked: Exceeded 3 urgent inquiries from this IP in 15 minutes`
    //         });
    //     }
    // }

    res.json({
        remaining,
        blocked15mEmails,
        blockedIPs,
    });
});

module.exports = UrgentRouter;