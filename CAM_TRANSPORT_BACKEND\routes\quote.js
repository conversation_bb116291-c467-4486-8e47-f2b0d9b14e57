const { Router } = require('express');
const { QuoteForm, GetAllQuotes, DeleteQuote } = require('../controller/quote');
const api_key_auth = require('../middleware/api_key_auth');
const DeckQuote = require('../model/quote');

const QuoteRouter = Router();

/**
 * @swagger
 * /quote:
 *   post:
 *     summary: Submit a new quote request
 *     description: Submit a detailed quote request for transportation services
 *     tags: [Quote]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - contactInfo
 *               - shipmentDetails
 *               - pickupDelivery
 *               - specialRequirements
 *             properties:
 *               contactInfo:
 *                 type: object
 *                 required:
 *                   - fullName
 *                   - phoneNumber
 *                   - emailAddress
 *                   - preferredContactMethod
 *                 properties:
 *                   fullName:
 *                     type: string
 *                   companyName:
 *                     type: string
 *                   phoneNumber:
 *                     type: string
 *                   emailAddress:
 *                     type: string
 *                   preferredContactMethod:
 *                     type: string
 *                     enum: [Phone, Email]
 *               shipmentDetails:
 *                 type: object
 *                 required:
 *                   - itemDescription
 *                   - loadType
 *                   - loadDimensions
 *                   - approximateWeight
 *                 properties:
 *                   itemDescription:
 *                     type: string
 *                   loadType:
 *                     type: string
 *                     enum: [Standard Flatbed, Step Deck, Lowboy, Oversized / Over dimensional]
 *                   loadDimensions:
 *                     type: object
 *                     properties:
 *                       length:
 *                         type: number
 *                       width:
 *                         type: number
 *                       height:
 *                         type: number
 *                       unit:
 *                         type: string
 *                         enum: [ft, m]
 *                   approximateWeight:
 *                     type: object
 *                     properties:
 *                       value:
 *                         type: number
 *                       unit:
 *                         type: string
 *                         enum: [lbs, kgs]
 *               pickupDelivery:
 *                 type: object
 *                 required:
 *                   - pickupLocation
 *                   - deliveryLocation
 *                   - preferredPickupDate
 *                   - preferredDeliveryDate
 *                 properties:
 *                   pickupLocation:
 *                     type: object
 *                     properties:
 *                       city:
 *                         type: string
 *                       stateOrProvince:
 *                         type: string
 *                   deliveryLocation:
 *                     type: object
 *                     properties:
 *                       city:
 *                         type: string
 *                       stateOrProvince:
 *                         type: string
 *                   preferredPickupDate:
 *                     type: string
 *                     format: date
 *                   preferredDeliveryDate:
 *                     type: string
 *                     format: date
 *                   deliveryAssistanceRequired:
 *                     type: boolean
 *               specialRequirements:
 *                 type: object
 *                 properties:
 *                   requiresPermitsOrEscorts:
 *                     type: boolean
 *                   specialHandlingInstructions:
 *                     type: string
 *                   deliveryType:
 *                     type: string
 *                     enum: [Recurring, One-time]
 *     responses:
 *       201:
 *         description: Quote request submitted successfully
 *       400:
 *         description: Invalid input or validation error
 *       500:
 *         description: Internal server error
 * 
 * /quote/get-quotes:
 *   get:
 *     summary: Get all quote requests
 *     description: Retrieve all quote requests (requires API key authentication)
 *     tags: [Quote]
 *     security:
 *       - ApiKeyAuth: []
 *     responses:
 *       200:
 *         description: List of all quote requests
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 quotes:
 *                   type: array
 *                   items:
 *                     type: object
 *                 count:
 *                   type: integer
 *                   example: 10
 *       401:
 *         description: Unauthorized - Invalid or missing API key
 *       500:
 *         description: Internal server error
 *
 * /quote/delete-quote/{id}:
 *   delete:
 *     summary: Delete a quote request
 *     description: Delete a specific quote request by ID (requires API key authentication)
 *     tags: [Quote]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: The quote ID to delete
 *     responses:
 *       200:
 *         description: Quote successfully deleted
 *       400:
 *         description: Invalid ID format
 *       401:
 *         description: Unauthorized - Invalid or missing API key
 *       404:
 *         description: Quote not found
 *       500:
 *         description: Internal server error
 */

QuoteRouter.post('/', QuoteForm);
QuoteRouter.get('/get-quotes', api_key_auth, GetAllQuotes);
QuoteRouter.delete('/delete-quote/:id', api_key_auth, DeleteQuote);

// Update quote status
QuoteRouter.patch('/update-status/:quoteId', async (req, res) => {
    try {
        const { quoteId } = req.params;
        const { status } = req.body;

        // Validate status
        const validStatuses = ['pending', 'in-review', 'approved', 'rejected'];
        if (!validStatuses.includes(status)) {
            return res.status(400).json({
                success: false,
                message: 'Invalid status. Must be one of: ' + validStatuses.join(', ')
            });
        }

        // Update quote in database
        const updatedQuote = await DeckQuote.findByIdAndUpdate(
            quoteId,
            {
                status: status,
                updatedAt: new Date()
            },
            { new: true }
        );

        if (!updatedQuote) {
            return res.status(404).json({
                success: false,
                message: 'Quote not found'
            });
        }

        res.json({
            success: true,
            message: 'Quote status updated successfully',
            quote: updatedQuote
        });

    } catch (error) {
        console.error('Error updating quote status:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error.message
        });
    }
});

module.exports = QuoteRouter;