const mongoose = require('mongoose');

const ContactSchema = new mongoose.Schema({
    name: {
        type: String,
        required: true
    },

    email: {
        type: String,
        required: true,
    },

    contact: {
        type: String,
        required: true,
    },

    company: {
        type: String,
        required: false,
    },

    inquiryType: {
        type: String,
        required: true,
        enum: {
            values: ['General Inquiry', 'Book a Shipment', 'Track a Shipment', 'Other'],
            message: 'Invalid inquiry type'
        }
    },

    otherInquiry: {
        type: String,
        required: function () {
            return this.inquiryType === 'Other';
        }
    },

    message: {
        type: String,
        required: true
    },

    _honeypot: {
        type: String,
        default: '',
        select: false // Don't expose in queries
    },

    ip: {
        type: String,
        required: false
    },

    status: {
        type: String,
        required: false,
        enum: {
            values: ['pending', 'in-view', 'completed'],
            message: 'Invalid status'
        },
        default: 'pending'
    }

}, { timestamps: true, autoIndex: false });

const Contact = mongoose.model('Contact', ContactSchema);
module.exports = Contact;
