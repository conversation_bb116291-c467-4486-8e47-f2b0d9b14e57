const { Router } = require('express');
const { ContactForm, GetAllUsers, DeleteUser } = require('../controller/contact');
const rateLimit = require('express-rate-limit');
const Contact = require('../model/contact');
const { checkIp24hBlock } = require('../utils/rateLimitHelpers');

const ContactRouter = Router();

const contactRateLimiter = rateLimit({
    windowMs: 15 * 60 * 1000,
    max: 15,
    message: 'Too many contact submissions from this IP, please try again later',
});

/**
 * @swagger
 * components:
 *   schemas:
 *     Contact:
 *       type: object
 *       required:
 *         - name
 *         - email
 *         - contact
 *         - inquiryType
 *         - message
 *       properties:
 *         name:
 *           type: string
 *           description: Name of the person submitting the contact form (max 100 characters).
 *           example: <PERSON>
 *         email:
 *           type: string
 *           format: email
 *           description: Email address of the contact (valid email format).
 *           example: <EMAIL>
 *         contact:
 *           type: string
 *           description: Contact number consisting of exactly 10 digits.
 *           example: "1234567890"
 *         company:
 *           type: string
 *           description: Optional company name (max 50 characters).
 *           example: Acme Inc.
 *         inquiryType:
 *           type: string
 *           enum:
 *             - General Inquiry
 *             - Book a Shipment
 *             - Track a Shipment
 *             - Other
 *           description: Type of inquiry.
 *           example: Book a Shipment
 *         otherInquiry:
 *           type: string
 *           description: Required if inquiryType is 'Other'. Description of other inquiry (max 150 characters).
 *           example: Request for partnership details.
 *         message:
 *           type: string
 *           description: Message content (max 300 characters).
 *           example: I want to know more about your shipment options.
 *       example:
 *         name: John Doe
 *         email: <EMAIL>
 *         contact: "1234567890"
 *         company: Acme Inc.
 *         inquiryType: Book a Shipment
 *         otherInquiry: ""
 *         message: Please provide details about your shipment rates.
 *
 * /contact:
 *   post:
 *     summary: Submit a contact inquiry
 *     tags:
 *       - Contact
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Contact'
 *     responses:
 *       201:
 *         description: Contact inquiry submitted successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   $ref: '#/components/schemas/Contact'
 *       400:
 *         description: Invalid input data.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 error:
 *                   type: string
 *                   example: Validation error message here
 */
ContactRouter.post('/', contactRateLimiter, ContactForm)

ContactRouter.get('/get-contacts', GetAllUsers)

ContactRouter.delete('/delete-contact/:id', DeleteUser)

// Update contact status
ContactRouter.patch('/update-status/:contactId', async (req, res) => {
    try {
        const { contactId } = req.params;
        const { status } = req.body;

        // Validate status
        const validStatuses = ['pending', 'in-view', 'completed'];
        if (!validStatuses.includes(status)) {
            return res.status(400).json({
                success: false,
                message: 'Invalid status. Must be one of: ' + validStatuses.join(', ')
            });
        }

        // Update contact in database
        const updatedContact = await Contact.findByIdAndUpdate(
            contactId,
            {
                status: status,
                updatedAt: new Date()
            },
            { new: true }
        );

        if (!updatedContact) {
            return res.status(404).json({
                success: false,
                message: 'Contact not found'
            });
        }

        res.json({
            success: true,
            message: 'Contact status updated successfully',
            contact: updatedContact
        });

    } catch (error) {
        console.error('Error updating contact status:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error.message
        });
    }
})

ContactRouter.get('/user-limit', async (req, res) => {
    const now = new Date();
    const fifteenMinutesAgo = new Date(now.getTime() - 15 * 60 * 1000);
    const recentCount = await Contact.countDocuments({
        createdAt: { $gte: fifteenMinutesAgo }
    });
    const remaining = Math.max(0, 15 - recentCount);

    // --- Per-Email 15-minute Limit Blocked Emails (3 submissions) ---
    // Note: This block is short-term and rolling, so we report if they are currently over the limit.
    const recent15mContacts = await Contact.find({
        createdAt: { $gte: fifteenMinutesAgo }
    });

    const email15mMap = new Map();
    for (const contact of recent15mContacts) {
        if (!email15mMap.has(contact.email)) {
            email15mMap.set(contact.email, 0);
        }
        email15mMap.set(contact.email, email15mMap.get(contact.email) + 1);
    }

    const blocked15mEmails = [];
    for (const [email, count] of email15mMap.entries()) {
        if (count >= 3) {
            blocked15mEmails.push({
                email,
                currentSubmissions: count,
                reason: `Exceeded 3 submissions in 15 minutes`
            });
        }
    }

    // --- Per-Email 24-hour Limit Blocked Emails (15 submissions) ---
    const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

    const allContactsIn24h = await Contact.find({
        createdAt: { $gte: oneDayAgo }
    }).sort({ createdAt: 1 });

    const emailMap = new Map();
    const ipMap = new Map();

    for (const contact of allContactsIn24h) {
        if (!emailMap.has(contact.email)) {
            emailMap.set(contact.email, []);
        }
        emailMap.get(contact.email).push(contact.createdAt);

        if (!ipMap.has(contact.ip)) {
            ipMap.set(contact.ip, []);
        }
        ipMap.get(contact.ip).push(contact.createdAt);
    }

    const blockedIPs = [];
    for (const [ip, dates] of ipMap.entries()) {
        const { isBlocked, unblockDate } = await checkIp24hBlock(ip, now, oneDayAgo);
        if (isBlocked) {
            blockedIPs.push({
                ip,
                unblockDate: unblockDate.toLocaleString(),
                reason: `Exceeded 5 submissions from this IP in 24 hours`
            });
        }
    }

    res.json({ remaining, blocked15mEmails, blockedIPs });
});

module.exports = ContactRouter;