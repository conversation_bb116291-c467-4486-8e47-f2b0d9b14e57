{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/views/apps/quotes/QuoteDetailsModal.jsx"], "sourcesContent": ["'use client'\r\n\r\n// React Imports\r\nimport { useState } from 'react'\r\n\r\n// MUI Imports\r\nimport Dialog from '@mui/material/Dialog'\r\nimport DialogTitle from '@mui/material/DialogTitle'\r\nimport DialogContent from '@mui/material/DialogContent'\r\nimport Button from '@mui/material/Button'\r\nimport Typography from '@mui/material/Typography'\r\nimport Grid from '@mui/material/Grid2'\r\nimport Card from '@mui/material/Card'\r\nimport CardContent from '@mui/material/CardContent'\r\nimport Avatar from '@mui/material/Avatar'\r\nimport Divider from '@mui/material/Divider'\r\nimport IconButton from '@mui/material/IconButton'\r\n\r\n// PDF Export\r\nimport jsPDF from 'jspdf'\r\nimport 'jspdf-autotable'\r\n\r\nconst QuoteDetailsModal = ({ open, onClose, quoteData }) => {\r\n  if (!quoteData) return null\r\n\r\n  // Handle PDF download\r\n  const handleDownloadPDF = async () => {\r\n    try {\r\n      const doc = new jsPDF()\r\n\r\n      // Header\r\n      doc.setFontSize(20)\r\n      doc.setTextColor(40, 40, 40)\r\n      doc.setFont(undefined, 'bold')\r\n      doc.text('Quote Details Report', 20, 30)\r\n\r\n      let yPosition = 50\r\n      const lineHeight = 8\r\n\r\n      // Quote header\r\n      doc.setFontSize(16)\r\n      doc.setTextColor(25, 118, 210) // Primary color\r\n      doc.setFont(undefined, 'bold')\r\n      doc.text(`Quote for ${quoteData.contactInfo?.fullName || 'Unknown Person'}`, 20, yPosition)\r\n      yPosition += 12\r\n\r\n      // Draw a line under the name\r\n      doc.setDrawColor(25, 118, 210)\r\n      doc.line(20, yPosition - 2, 190, yPosition - 2)\r\n      yPosition += 8\r\n\r\n      // Contact Information Section\r\n      doc.setFontSize(12)\r\n      doc.setTextColor(0, 0, 0)\r\n      doc.setFont(undefined, 'bold')\r\n      doc.text('Contact Information:', 20, yPosition)\r\n      yPosition += 8\r\n\r\n      doc.setFontSize(10)\r\n      doc.setFont(undefined, 'normal')\r\n\r\n      const contactInfo = [\r\n        `Full Name: ${quoteData.contactInfo?.fullName || 'Not provided'}`,\r\n        `Email: ${quoteData.contactInfo?.emailAddress || 'Not provided'}`,\r\n        `Phone: ${quoteData.contactInfo?.phoneNumber || 'Not provided'}`,\r\n        `Company: ${quoteData.contactInfo?.companyName || 'Not provided'}`\r\n      ]\r\n\r\n      contactInfo.forEach(info => {\r\n        doc.text(info, 20, yPosition)\r\n        yPosition += lineHeight\r\n      })\r\n\r\n      yPosition += 5\r\n\r\n      // Shipment Details Section\r\n      doc.setFontSize(12)\r\n      doc.setFont(undefined, 'bold')\r\n      doc.text('Shipment Details:', 20, yPosition)\r\n      yPosition += 8\r\n\r\n      doc.setFontSize(10)\r\n      doc.setFont(undefined, 'normal')\r\n\r\n      const shipmentDetails = [\r\n        `Freight Type: ${quoteData.shipmentDetails?.loadType || 'Not specified'}`,\r\n        `Weight: ${quoteData.shipmentDetails?.approximateWeight ?\r\n          `${quoteData.shipmentDetails.approximateWeight.value} ${quoteData.shipmentDetails.approximateWeight.unit}` :\r\n          'Not specified'}`,\r\n        `Dimensions: ${quoteData.shipmentDetails?.loadDimensions ?\r\n          `${quoteData.shipmentDetails.loadDimensions.length}x${quoteData.shipmentDetails.loadDimensions.width}x${quoteData.shipmentDetails.loadDimensions.height} ${quoteData.shipmentDetails.loadDimensions.unit}` :\r\n          'Not specified'}`,\r\n        `Commodity: ${quoteData.shipmentDetails?.itemDescription || 'Not specified'}`\r\n      ]\r\n\r\n      shipmentDetails.forEach(detail => {\r\n        doc.text(detail, 20, yPosition)\r\n        yPosition += lineHeight\r\n      })\r\n\r\n      yPosition += 5\r\n\r\n      // Pickup & Delivery Section\r\n      doc.setFontSize(12)\r\n      doc.setFont(undefined, 'bold')\r\n      doc.text('Pickup & Delivery:', 20, yPosition)\r\n      yPosition += 8\r\n\r\n      doc.setFontSize(10)\r\n      doc.setFont(undefined, 'normal')\r\n\r\n      const pickupDelivery = [\r\n        `Pickup Location: ${quoteData.pickupDelivery?.pickupLocation ?\r\n          `${quoteData.pickupDelivery.pickupLocation.city}, ${quoteData.pickupDelivery.pickupLocation.stateOrProvince}` :\r\n          'Not specified'}`,\r\n        `Delivery Location: ${quoteData.pickupDelivery?.deliveryLocation ?\r\n          `${quoteData.pickupDelivery.deliveryLocation.city}, ${quoteData.pickupDelivery.deliveryLocation.stateOrProvince}` :\r\n          'Not specified'}`,\r\n        `Pickup Date: ${(() => {\r\n          const formatDateTime = (dateString) => {\r\n            if (!dateString) return 'Not specified'\r\n            try {\r\n              const date = new Date(dateString)\r\n              if (isNaN(date.getTime())) return dateString\r\n              const dateOptions = { month: '2-digit', day: '2-digit', year: 'numeric' }\r\n              const timeOptions = { hour: '2-digit', minute: '2-digit', hour12: true }\r\n              const formattedDate = date.toLocaleDateString('en-US', dateOptions)\r\n              const formattedTime = date.toLocaleTimeString('en-US', timeOptions)\r\n              return `${formattedDate} ${formattedTime}`\r\n            } catch (error) {\r\n              return dateString\r\n            }\r\n          }\r\n          return formatDateTime(quoteData.pickupDelivery?.preferredPickupDate)\r\n        })()}`,\r\n        `Delivery Date: ${(() => {\r\n          const formatDateTime = (dateString) => {\r\n            if (!dateString) return 'Not specified'\r\n            try {\r\n              const date = new Date(dateString)\r\n              if (isNaN(date.getTime())) return dateString\r\n              const dateOptions = { month: '2-digit', day: '2-digit', year: 'numeric' }\r\n              const timeOptions = { hour: '2-digit', minute: '2-digit', hour12: true }\r\n              const formattedDate = date.toLocaleDateString('en-US', dateOptions)\r\n              const formattedTime = date.toLocaleTimeString('en-US', timeOptions)\r\n              return `${formattedDate} ${formattedTime}`\r\n            } catch (error) {\r\n              return dateString\r\n            }\r\n          }\r\n          return formatDateTime(quoteData.pickupDelivery?.preferredDeliveryDate)\r\n        })()}`\r\n      ]\r\n\r\n      pickupDelivery.forEach(detail => {\r\n        doc.text(detail, 20, yPosition)\r\n        yPosition += lineHeight\r\n      })\r\n\r\n      // Footer\r\n      yPosition += 20\r\n      doc.setFontSize(8)\r\n      doc.setTextColor(100, 100, 100)\r\n      doc.text(`Generated on ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()}`, 20, yPosition)\r\n      doc.text('CAM Transport - Quote Management System', 20, yPosition + 5)\r\n\r\n      // Save the PDF\r\n      const fileName = `quote-${quoteData.contactInfo?.fullName?.replace(/\\s+/g, '-') || 'unknown'}-${new Date().toISOString().split('T')[0]}.pdf`\r\n      doc.save(fileName)\r\n    } catch (error) {\r\n      console.error('Error generating PDF:', error)\r\n      alert('Failed to generate PDF. Please try again.')\r\n    }\r\n  }\r\n\r\n  return (\r\n    <Dialog open={open} onClose={onClose} maxWidth=\"lg\" fullWidth>\r\n      <DialogTitle className=\"flex items-center justify-between\">\r\n        <Typography variant=\"h4\" style={{ fontSize: '1.8rem' }}>Quote Details</Typography>\r\n        <div className=\"flex items-center gap-2\">\r\n          <Button\r\n            variant=\"outlined\"\r\n            color=\"primary\"\r\n            size=\"large\"\r\n            startIcon={<i className=\"tabler-file-type-pdf\" />}\r\n            onClick={handleDownloadPDF}\r\n            className=\"font-bold\"\r\n            sx={{ fontSize: '1rem', padding: '12px 24px' }}\r\n          >\r\n            Download PDF\r\n          </Button>\r\n          <IconButton onClick={onClose}>\r\n            <i className=\"tabler-x\" />\r\n          </IconButton>\r\n        </div>\r\n      </DialogTitle>\r\n\r\n      <DialogContent>\r\n        <Grid container spacing={6}>\r\n          {/* Contact Profile Section */}\r\n          <Grid size={{ xs: 12 }}>\r\n            <Card>\r\n              <CardContent>\r\n                <div className=\"flex items-center gap-6 mb-6\">\r\n                  <Avatar\r\n                    sx={{ width: 120, height: 120, fontSize: '3.5rem', fontWeight: 'bold', backgroundColor: '#1976d2' }}\r\n                  >\r\n                    {quoteData.contactInfo?.fullName?.charAt(0)?.toUpperCase() || 'Q'}\r\n                  </Avatar>\r\n                  <div>\r\n                    <Typography variant=\"h3\" className=\"mb-3 font-bold\" style={{ fontSize: '2.2rem' }}>\r\n                      {quoteData.contactInfo?.fullName || 'Unknown Contact'}\r\n                    </Typography>\r\n                    <Typography variant=\"h5\" color=\"text.secondary\" className=\"font-medium\" style={{ fontSize: '1.4rem' }}>\r\n                      {quoteData.contactInfo?.emailAddress || 'No email provided'}\r\n                    </Typography>\r\n                  </div>\r\n                </div>\r\n\r\n                <Divider className=\"mb-6\" />\r\n\r\n                {/* Contact Details Grid */}\r\n                <Grid container spacing={4}>\r\n                  <Grid size={{ xs: 12, sm: 6 }}>\r\n                    <Typography variant=\"h6\" color=\"text.secondary\" className=\"mb-3 font-medium\" style={{ fontSize: '1.2rem' }}>\r\n                      Phone Number\r\n                    </Typography>\r\n                    <Typography variant=\"h5\" className=\"font-bold\" style={{ fontSize: '1.2rem' }}>\r\n                      {quoteData.contactInfo?.phoneNumber || 'Not provided'}\r\n                    </Typography>\r\n                  </Grid>\r\n\r\n                  <Grid size={{ xs: 12, sm: 6 }}>\r\n                    <Typography variant=\"h6\" color=\"text.secondary\" className=\"mb-3 font-medium\" style={{ fontSize: '1.2rem' }}>\r\n                      Company\r\n                    </Typography>\r\n                    <Typography variant=\"h5\" className=\"font-bold\" style={{ fontSize: '1.2rem' }}>\r\n                      {quoteData.contactInfo?.companyName || 'Not provided'}\r\n                    </Typography>\r\n                  </Grid>\r\n\r\n                  <Grid size={{ xs: 12, sm: 6 }}>\r\n                    <Typography variant=\"h6\" color=\"text.secondary\" className=\"mb-3 font-medium\" style={{ fontSize: '1.2rem' }}>\r\n                      Preferred Contact Method\r\n                    </Typography>\r\n                    <Typography variant=\"h5\" className=\"font-bold\" style={{ fontSize: '1.2rem' }}>\r\n                      {quoteData.contactInfo?.preferredContactMethod || 'Not specified'}\r\n                    </Typography>\r\n                  </Grid>\r\n\r\n                  <Grid size={{ xs: 12, sm: 6 }}>\r\n                    <Typography variant=\"h6\" color=\"text.secondary\" className=\"mb-3 font-medium\" style={{ fontSize: '1.2rem' }}>\r\n                      Quote Date\r\n                    </Typography>\r\n                    <Typography variant=\"h5\" className=\"font-bold\" style={{ fontSize: '1.2rem' }}>\r\n                      {(() => {\r\n                        const formatDateTime = (dateString) => {\r\n                          if (!dateString) return 'Not available'\r\n\r\n                          try {\r\n                            const date = new Date(dateString)\r\n                            if (isNaN(date.getTime())) return dateString\r\n\r\n                            const dateOptions = { month: '2-digit', day: '2-digit', year: 'numeric' }\r\n                            const timeOptions = { hour: '2-digit', minute: '2-digit', hour12: true }\r\n\r\n                            const formattedDate = date.toLocaleDateString('en-US', dateOptions)\r\n                            const formattedTime = date.toLocaleTimeString('en-US', timeOptions)\r\n\r\n                            return `${formattedDate} ${formattedTime}`\r\n                          } catch (error) {\r\n                            return dateString\r\n                          }\r\n                        }\r\n\r\n                        return formatDateTime(quoteData.createdAt)\r\n                      })()}\r\n                    </Typography>\r\n                  </Grid>\r\n                </Grid>\r\n              </CardContent>\r\n            </Card>\r\n          </Grid>\r\n\r\n          {/* Shipment Details Section */}\r\n          <Grid size={{ xs: 12, md: 6 }}>\r\n            <Card className=\"mb-4\">\r\n              <CardContent>\r\n                <Typography variant=\"h5\" className=\"font-bold mb-3\" style={{ fontSize: '1.4rem', borderBottom: '2px solid #1976d2', paddingBottom: '4px' }}>\r\n                  SHIPMENT DETAILS\r\n                </Typography>\r\n                <Typography variant=\"body1\" style={{ fontSize: '1.1rem', lineHeight: '1.6' }}>\r\n                  <strong>Freight Type:</strong> {quoteData.shipmentDetails?.loadType || 'Not specified'}<br />\r\n                  <strong>Weight:</strong> {quoteData.shipmentDetails?.approximateWeight ?\r\n                    `${quoteData.shipmentDetails.approximateWeight.value} ${quoteData.shipmentDetails.approximateWeight.unit}` :\r\n                    'Not specified'}<br />\r\n                  <strong>Dimensions:</strong> {quoteData.shipmentDetails?.loadDimensions ?\r\n                    `${quoteData.shipmentDetails.loadDimensions.length}x${quoteData.shipmentDetails.loadDimensions.width}x${quoteData.shipmentDetails.loadDimensions.height} ${quoteData.shipmentDetails.loadDimensions.unit}` :\r\n                    'Not specified'}<br />\r\n                  <strong>Commodity:</strong> {quoteData.shipmentDetails?.itemDescription || 'Not specified'}\r\n                </Typography>\r\n              </CardContent>\r\n            </Card>\r\n          </Grid>\r\n\r\n          {/* Pickup & Delivery Section */}\r\n          <Grid size={{ xs: 12, md: 6 }}>\r\n            <Card className=\"mb-4\">\r\n              <CardContent>\r\n                <Typography variant=\"h5\" className=\"font-bold mb-3\" style={{ fontSize: '1.4rem', borderBottom: '2px solid #1976d2', paddingBottom: '4px' }}>\r\n                  PICKUP & DELIVERY\r\n                </Typography>\r\n                <Typography variant=\"body1\" style={{ fontSize: '1.1rem', lineHeight: '1.6' }}>\r\n                  <strong>Pickup Location:</strong> {quoteData.pickupDelivery?.pickupLocation ?\r\n                    `${quoteData.pickupDelivery.pickupLocation.city}, ${quoteData.pickupDelivery.pickupLocation.stateOrProvince}` :\r\n                    'Not specified'}<br />\r\n                  <strong>Delivery Location:</strong> {quoteData.pickupDelivery?.deliveryLocation ?\r\n                    `${quoteData.pickupDelivery.deliveryLocation.city}, ${quoteData.pickupDelivery.deliveryLocation.stateOrProvince}` :\r\n                    'Not specified'}<br />\r\n                  <strong>Pickup Date:</strong> {(() => {\r\n                    const formatDateTime = (dateString) => {\r\n                      if (!dateString) return 'Not specified'\r\n                      try {\r\n                        const date = new Date(dateString)\r\n                        if (isNaN(date.getTime())) return dateString\r\n                        const dateOptions = { month: '2-digit', day: '2-digit', year: 'numeric' }\r\n                        const timeOptions = { hour: '2-digit', minute: '2-digit', hour12: true }\r\n                        const formattedDate = date.toLocaleDateString('en-US', dateOptions)\r\n                        const formattedTime = date.toLocaleTimeString('en-US', timeOptions)\r\n                        return `${formattedDate} ${formattedTime}`\r\n                      } catch (error) {\r\n                        return dateString\r\n                      }\r\n                    }\r\n                    return formatDateTime(quoteData.pickupDelivery?.preferredPickupDate)\r\n                  })()}<br />\r\n                  <strong>Delivery Date:</strong> {(() => {\r\n                    const formatDateTime = (dateString) => {\r\n                      if (!dateString) return 'Not specified'\r\n                      try {\r\n                        const date = new Date(dateString)\r\n                        if (isNaN(date.getTime())) return dateString\r\n                        const dateOptions = { month: '2-digit', day: '2-digit', year: 'numeric' }\r\n                        const timeOptions = { hour: '2-digit', minute: '2-digit', hour12: true }\r\n                        const formattedDate = date.toLocaleDateString('en-US', dateOptions)\r\n                        const formattedTime = date.toLocaleTimeString('en-US', timeOptions)\r\n                        return `${formattedDate} ${formattedTime}`\r\n                      } catch (error) {\r\n                        return dateString\r\n                      }\r\n                    }\r\n                    return formatDateTime(quoteData.pickupDelivery?.preferredDeliveryDate)\r\n                  })()}<br />\r\n                  <strong>Delivery Assistance Required:</strong> {typeof quoteData.pickupDelivery?.deliveryAssistanceRequired === 'boolean' ?\r\n                    (quoteData.pickupDelivery.deliveryAssistanceRequired ? 'Yes' : 'No') :\r\n                    'Not specified'}\r\n                </Typography>\r\n              </CardContent>\r\n            </Card>\r\n          </Grid>\r\n\r\n          {/* Special Requirements Section */}\r\n          {quoteData.specialRequirements && (\r\n            <Grid size={{ xs: 12 }}>\r\n              <Card className=\"mb-4\">\r\n                <CardContent>\r\n                  <Typography variant=\"h5\" className=\"font-bold mb-3\" style={{ fontSize: '1.4rem', borderBottom: '2px solid #1976d2', paddingBottom: '4px' }}>\r\n                    SPECIAL REQUIREMENTS\r\n                  </Typography>\r\n                  <Typography variant=\"body1\" style={{ fontSize: '1.1rem', lineHeight: '1.6' }}>\r\n                    <strong>Delivery Type:</strong> {quoteData.specialRequirements.deliveryType || 'Not specified'}<br />\r\n                    <strong>Requires Permits/Escorts:</strong> {quoteData.specialRequirements.requiresPermitsOrEscorts ? 'Yes' : 'No'}<br />\r\n                    <strong>Special Handling Instructions:</strong> {quoteData.specialRequirements.specialHandlingInstructions || 'None specified'}\r\n                  </Typography>\r\n                </CardContent>\r\n              </Card>\r\n            </Grid>\r\n          )}\r\n        </Grid>\r\n      </DialogContent>\r\n    </Dialog>\r\n  )\r\n}\r\n\r\nexport default QuoteDetailsModal\r\n"], "names": [], "mappings": ";;;;AAEA,gBAAgB;AAChB;AAEA,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA,aAAa;AACb;AACA;AApBA;;;;;;;;;;;;;;;;AAsBA,MAAM,oBAAoB,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE;IACrD,IAAI,CAAC,WAAW,OAAO;IAEvB,sBAAsB;IACtB,MAAM,oBAAoB;QACxB,IAAI;YACF,MAAM,MAAM,IAAI,mJAAA,CAAA,UAAK;YAErB,SAAS;YACT,IAAI,WAAW,CAAC;YAChB,IAAI,YAAY,CAAC,IAAI,IAAI;YACzB,IAAI,OAAO,CAAC,WAAW;YACvB,IAAI,IAAI,CAAC,wBAAwB,IAAI;YAErC,IAAI,YAAY;YAChB,MAAM,aAAa;YAEnB,eAAe;YACf,IAAI,WAAW,CAAC;YAChB,IAAI,YAAY,CAAC,IAAI,KAAK,KAAK,gBAAgB;;YAC/C,IAAI,OAAO,CAAC,WAAW;YACvB,IAAI,IAAI,CAAC,CAAC,UAAU,EAAE,UAAU,WAAW,EAAE,YAAY,kBAAkB,EAAE,IAAI;YACjF,aAAa;YAEb,6BAA6B;YAC7B,IAAI,YAAY,CAAC,IAAI,KAAK;YAC1B,IAAI,IAAI,CAAC,IAAI,YAAY,GAAG,KAAK,YAAY;YAC7C,aAAa;YAEb,8BAA8B;YAC9B,IAAI,WAAW,CAAC;YAChB,IAAI,YAAY,CAAC,GAAG,GAAG;YACvB,IAAI,OAAO,CAAC,WAAW;YACvB,IAAI,IAAI,CAAC,wBAAwB,IAAI;YACrC,aAAa;YAEb,IAAI,WAAW,CAAC;YAChB,IAAI,OAAO,CAAC,WAAW;YAEvB,MAAM,cAAc;gBAClB,CAAC,WAAW,EAAE,UAAU,WAAW,EAAE,YAAY,gBAAgB;gBACjE,CAAC,OAAO,EAAE,UAAU,WAAW,EAAE,gBAAgB,gBAAgB;gBACjE,CAAC,OAAO,EAAE,UAAU,WAAW,EAAE,eAAe,gBAAgB;gBAChE,CAAC,SAAS,EAAE,UAAU,WAAW,EAAE,eAAe,gBAAgB;aACnE;YAED,YAAY,OAAO,CAAC,CAAA;gBAClB,IAAI,IAAI,CAAC,MAAM,IAAI;gBACnB,aAAa;YACf;YAEA,aAAa;YAEb,2BAA2B;YAC3B,IAAI,WAAW,CAAC;YAChB,IAAI,OAAO,CAAC,WAAW;YACvB,IAAI,IAAI,CAAC,qBAAqB,IAAI;YAClC,aAAa;YAEb,IAAI,WAAW,CAAC;YAChB,IAAI,OAAO,CAAC,WAAW;YAEvB,MAAM,kBAAkB;gBACtB,CAAC,cAAc,EAAE,UAAU,eAAe,EAAE,YAAY,iBAAiB;gBACzE,CAAC,QAAQ,EAAE,UAAU,eAAe,EAAE,oBACpC,GAAG,UAAU,eAAe,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC,EAAE,UAAU,eAAe,CAAC,iBAAiB,CAAC,IAAI,EAAE,GAC1G,iBAAiB;gBACnB,CAAC,YAAY,EAAE,UAAU,eAAe,EAAE,iBACxC,GAAG,UAAU,eAAe,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,EAAE,UAAU,eAAe,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,EAAE,UAAU,eAAe,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,EAAE,UAAU,eAAe,CAAC,cAAc,CAAC,IAAI,EAAE,GAC1M,iBAAiB;gBACnB,CAAC,WAAW,EAAE,UAAU,eAAe,EAAE,mBAAmB,iBAAiB;aAC9E;YAED,gBAAgB,OAAO,CAAC,CAAA;gBACtB,IAAI,IAAI,CAAC,QAAQ,IAAI;gBACrB,aAAa;YACf;YAEA,aAAa;YAEb,4BAA4B;YAC5B,IAAI,WAAW,CAAC;YAChB,IAAI,OAAO,CAAC,WAAW;YACvB,IAAI,IAAI,CAAC,sBAAsB,IAAI;YACnC,aAAa;YAEb,IAAI,WAAW,CAAC;YAChB,IAAI,OAAO,CAAC,WAAW;YAEvB,MAAM,iBAAiB;gBACrB,CAAC,iBAAiB,EAAE,UAAU,cAAc,EAAE,iBAC5C,GAAG,UAAU,cAAc,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,EAAE,UAAU,cAAc,CAAC,cAAc,CAAC,eAAe,EAAE,GAC7G,iBAAiB;gBACnB,CAAC,mBAAmB,EAAE,UAAU,cAAc,EAAE,mBAC9C,GAAG,UAAU,cAAc,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE,EAAE,UAAU,cAAc,CAAC,gBAAgB,CAAC,eAAe,EAAE,GACjH,iBAAiB;gBACnB,CAAC,aAAa,EAAE,CAAC;oBACf,MAAM,iBAAiB,CAAC;wBACtB,IAAI,CAAC,YAAY,OAAO;wBACxB,IAAI;4BACF,MAAM,OAAO,IAAI,KAAK;4BACtB,IAAI,MAAM,KAAK,OAAO,KAAK,OAAO;4BAClC,MAAM,cAAc;gCAAE,OAAO;gCAAW,KAAK;gCAAW,MAAM;4BAAU;4BACxE,MAAM,cAAc;gCAAE,MAAM;gCAAW,QAAQ;gCAAW,QAAQ;4BAAK;4BACvE,MAAM,gBAAgB,KAAK,kBAAkB,CAAC,SAAS;4BACvD,MAAM,gBAAgB,KAAK,kBAAkB,CAAC,SAAS;4BACvD,OAAO,GAAG,cAAc,CAAC,EAAE,eAAe;wBAC5C,EAAE,OAAO,OAAO;4BACd,OAAO;wBACT;oBACF;oBACA,OAAO,eAAe,UAAU,cAAc,EAAE;gBAClD,CAAC,KAAK;gBACN,CAAC,eAAe,EAAE,CAAC;oBACjB,MAAM,iBAAiB,CAAC;wBACtB,IAAI,CAAC,YAAY,OAAO;wBACxB,IAAI;4BACF,MAAM,OAAO,IAAI,KAAK;4BACtB,IAAI,MAAM,KAAK,OAAO,KAAK,OAAO;4BAClC,MAAM,cAAc;gCAAE,OAAO;gCAAW,KAAK;gCAAW,MAAM;4BAAU;4BACxE,MAAM,cAAc;gCAAE,MAAM;gCAAW,QAAQ;gCAAW,QAAQ;4BAAK;4BACvE,MAAM,gBAAgB,KAAK,kBAAkB,CAAC,SAAS;4BACvD,MAAM,gBAAgB,KAAK,kBAAkB,CAAC,SAAS;4BACvD,OAAO,GAAG,cAAc,CAAC,EAAE,eAAe;wBAC5C,EAAE,OAAO,OAAO;4BACd,OAAO;wBACT;oBACF;oBACA,OAAO,eAAe,UAAU,cAAc,EAAE;gBAClD,CAAC,KAAK;aACP;YAED,eAAe,OAAO,CAAC,CAAA;gBACrB,IAAI,IAAI,CAAC,QAAQ,IAAI;gBACrB,aAAa;YACf;YAEA,SAAS;YACT,aAAa;YACb,IAAI,WAAW,CAAC;YAChB,IAAI,YAAY,CAAC,KAAK,KAAK;YAC3B,IAAI,IAAI,CAAC,CAAC,aAAa,EAAE,IAAI,OAAO,kBAAkB,GAAG,IAAI,EAAE,IAAI,OAAO,kBAAkB,IAAI,EAAE,IAAI;YACtG,IAAI,IAAI,CAAC,2CAA2C,IAAI,YAAY;YAEpE,eAAe;YACf,MAAM,WAAW,CAAC,MAAM,EAAE,UAAU,WAAW,EAAE,UAAU,QAAQ,QAAQ,QAAQ,UAAU,CAAC,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC;YAC5I,IAAI,IAAI,CAAC;QACX,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM;QACR;IACF;IAEA,qBACE,8OAAC,qJAAA,CAAA,UAAM;QAAC,MAAM;QAAM,SAAS;QAAS,UAAS;QAAK,SAAS;;0BAC3D,8OAAC,+JAAA,CAAA,UAAW;gBAAC,WAAU;;kCACrB,8OAAC,6JAAA,CAAA,UAAU;wBAAC,SAAQ;wBAAK,OAAO;4BAAE,UAAU;wBAAS;kCAAG;;;;;;kCACxD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,qJAAA,CAAA,UAAM;gCACL,SAAQ;gCACR,OAAM;gCACN,MAAK;gCACL,yBAAW,8OAAC;oCAAE,WAAU;;;;;;gCACxB,SAAS;gCACT,WAAU;gCACV,IAAI;oCAAE,UAAU;oCAAQ,SAAS;gCAAY;0CAC9C;;;;;;0CAGD,8OAAC,6JAAA,CAAA,UAAU;gCAAC,SAAS;0CACnB,cAAA,8OAAC;oCAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;0BAKnB,8OAAC,mKAAA,CAAA,UAAa;0BACZ,cAAA,8OAAC,mJAAA,CAAA,UAAI;oBAAC,SAAS;oBAAC,SAAS;;sCAEvB,8OAAC,mJAAA,CAAA,UAAI;4BAAC,MAAM;gCAAE,IAAI;4BAAG;sCACnB,cAAA,8OAAC,iJAAA,CAAA,UAAI;0CACH,cAAA,8OAAC,+JAAA,CAAA,UAAW;;sDACV,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,qJAAA,CAAA,UAAM;oDACL,IAAI;wDAAE,OAAO;wDAAK,QAAQ;wDAAK,UAAU;wDAAU,YAAY;wDAAQ,iBAAiB;oDAAU;8DAEjG,UAAU,WAAW,EAAE,UAAU,OAAO,IAAI,iBAAiB;;;;;;8DAEhE,8OAAC;;sEACC,8OAAC,6JAAA,CAAA,UAAU;4DAAC,SAAQ;4DAAK,WAAU;4DAAiB,OAAO;gEAAE,UAAU;4DAAS;sEAC7E,UAAU,WAAW,EAAE,YAAY;;;;;;sEAEtC,8OAAC,6JAAA,CAAA,UAAU;4DAAC,SAAQ;4DAAK,OAAM;4DAAiB,WAAU;4DAAc,OAAO;gEAAE,UAAU;4DAAS;sEACjG,UAAU,WAAW,EAAE,gBAAgB;;;;;;;;;;;;;;;;;;sDAK9C,8OAAC,uJAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;sDAGnB,8OAAC,mJAAA,CAAA,UAAI;4CAAC,SAAS;4CAAC,SAAS;;8DACvB,8OAAC,mJAAA,CAAA,UAAI;oDAAC,MAAM;wDAAE,IAAI;wDAAI,IAAI;oDAAE;;sEAC1B,8OAAC,6JAAA,CAAA,UAAU;4DAAC,SAAQ;4DAAK,OAAM;4DAAiB,WAAU;4DAAmB,OAAO;gEAAE,UAAU;4DAAS;sEAAG;;;;;;sEAG5G,8OAAC,6JAAA,CAAA,UAAU;4DAAC,SAAQ;4DAAK,WAAU;4DAAY,OAAO;gEAAE,UAAU;4DAAS;sEACxE,UAAU,WAAW,EAAE,eAAe;;;;;;;;;;;;8DAI3C,8OAAC,mJAAA,CAAA,UAAI;oDAAC,MAAM;wDAAE,IAAI;wDAAI,IAAI;oDAAE;;sEAC1B,8OAAC,6JAAA,CAAA,UAAU;4DAAC,SAAQ;4DAAK,OAAM;4DAAiB,WAAU;4DAAmB,OAAO;gEAAE,UAAU;4DAAS;sEAAG;;;;;;sEAG5G,8OAAC,6JAAA,CAAA,UAAU;4DAAC,SAAQ;4DAAK,WAAU;4DAAY,OAAO;gEAAE,UAAU;4DAAS;sEACxE,UAAU,WAAW,EAAE,eAAe;;;;;;;;;;;;8DAI3C,8OAAC,mJAAA,CAAA,UAAI;oDAAC,MAAM;wDAAE,IAAI;wDAAI,IAAI;oDAAE;;sEAC1B,8OAAC,6JAAA,CAAA,UAAU;4DAAC,SAAQ;4DAAK,OAAM;4DAAiB,WAAU;4DAAmB,OAAO;gEAAE,UAAU;4DAAS;sEAAG;;;;;;sEAG5G,8OAAC,6JAAA,CAAA,UAAU;4DAAC,SAAQ;4DAAK,WAAU;4DAAY,OAAO;gEAAE,UAAU;4DAAS;sEACxE,UAAU,WAAW,EAAE,0BAA0B;;;;;;;;;;;;8DAItD,8OAAC,mJAAA,CAAA,UAAI;oDAAC,MAAM;wDAAE,IAAI;wDAAI,IAAI;oDAAE;;sEAC1B,8OAAC,6JAAA,CAAA,UAAU;4DAAC,SAAQ;4DAAK,OAAM;4DAAiB,WAAU;4DAAmB,OAAO;gEAAE,UAAU;4DAAS;sEAAG;;;;;;sEAG5G,8OAAC,6JAAA,CAAA,UAAU;4DAAC,SAAQ;4DAAK,WAAU;4DAAY,OAAO;gEAAE,UAAU;4DAAS;sEACxE,CAAC;gEACA,MAAM,iBAAiB,CAAC;oEACtB,IAAI,CAAC,YAAY,OAAO;oEAExB,IAAI;wEACF,MAAM,OAAO,IAAI,KAAK;wEACtB,IAAI,MAAM,KAAK,OAAO,KAAK,OAAO;wEAElC,MAAM,cAAc;4EAAE,OAAO;4EAAW,KAAK;4EAAW,MAAM;wEAAU;wEACxE,MAAM,cAAc;4EAAE,MAAM;4EAAW,QAAQ;4EAAW,QAAQ;wEAAK;wEAEvE,MAAM,gBAAgB,KAAK,kBAAkB,CAAC,SAAS;wEACvD,MAAM,gBAAgB,KAAK,kBAAkB,CAAC,SAAS;wEAEvD,OAAO,GAAG,cAAc,CAAC,EAAE,eAAe;oEAC5C,EAAE,OAAO,OAAO;wEACd,OAAO;oEACT;gEACF;gEAEA,OAAO,eAAe,UAAU,SAAS;4DAC3C,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCASb,8OAAC,mJAAA,CAAA,UAAI;4BAAC,MAAM;gCAAE,IAAI;gCAAI,IAAI;4BAAE;sCAC1B,cAAA,8OAAC,iJAAA,CAAA,UAAI;gCAAC,WAAU;0CACd,cAAA,8OAAC,+JAAA,CAAA,UAAW;;sDACV,8OAAC,6JAAA,CAAA,UAAU;4CAAC,SAAQ;4CAAK,WAAU;4CAAiB,OAAO;gDAAE,UAAU;gDAAU,cAAc;gDAAqB,eAAe;4CAAM;sDAAG;;;;;;sDAG5I,8OAAC,6JAAA,CAAA,UAAU;4CAAC,SAAQ;4CAAQ,OAAO;gDAAE,UAAU;gDAAU,YAAY;4CAAM;;8DACzE,8OAAC;8DAAO;;;;;;gDAAsB;gDAAE,UAAU,eAAe,EAAE,YAAY;8DAAgB,8OAAC;;;;;8DACxF,8OAAC;8DAAO;;;;;;gDAAgB;gDAAE,UAAU,eAAe,EAAE,oBACnD,GAAG,UAAU,eAAe,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC,EAAE,UAAU,eAAe,CAAC,iBAAiB,CAAC,IAAI,EAAE,GAC1G;8DAAgB,8OAAC;;;;;8DACnB,8OAAC;8DAAO;;;;;;gDAAoB;gDAAE,UAAU,eAAe,EAAE,iBACvD,GAAG,UAAU,eAAe,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,EAAE,UAAU,eAAe,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,EAAE,UAAU,eAAe,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,EAAE,UAAU,eAAe,CAAC,cAAc,CAAC,IAAI,EAAE,GAC1M;8DAAgB,8OAAC;;;;;8DACnB,8OAAC;8DAAO;;;;;;gDAAmB;gDAAE,UAAU,eAAe,EAAE,mBAAmB;;;;;;;;;;;;;;;;;;;;;;;sCAOnF,8OAAC,mJAAA,CAAA,UAAI;4BAAC,MAAM;gCAAE,IAAI;gCAAI,IAAI;4BAAE;sCAC1B,cAAA,8OAAC,iJAAA,CAAA,UAAI;gCAAC,WAAU;0CACd,cAAA,8OAAC,+JAAA,CAAA,UAAW;;sDACV,8OAAC,6JAAA,CAAA,UAAU;4CAAC,SAAQ;4CAAK,WAAU;4CAAiB,OAAO;gDAAE,UAAU;gDAAU,cAAc;gDAAqB,eAAe;4CAAM;sDAAG;;;;;;sDAG5I,8OAAC,6JAAA,CAAA,UAAU;4CAAC,SAAQ;4CAAQ,OAAO;gDAAE,UAAU;gDAAU,YAAY;4CAAM;;8DACzE,8OAAC;8DAAO;;;;;;gDAAyB;gDAAE,UAAU,cAAc,EAAE,iBAC3D,GAAG,UAAU,cAAc,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,EAAE,UAAU,cAAc,CAAC,cAAc,CAAC,eAAe,EAAE,GAC7G;8DAAgB,8OAAC;;;;;8DACnB,8OAAC;8DAAO;;;;;;gDAA2B;gDAAE,UAAU,cAAc,EAAE,mBAC7D,GAAG,UAAU,cAAc,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE,EAAE,UAAU,cAAc,CAAC,gBAAgB,CAAC,eAAe,EAAE,GACjH;8DAAgB,8OAAC;;;;;8DACnB,8OAAC;8DAAO;;;;;;gDAAqB;gDAAE,CAAC;oDAC9B,MAAM,iBAAiB,CAAC;wDACtB,IAAI,CAAC,YAAY,OAAO;wDACxB,IAAI;4DACF,MAAM,OAAO,IAAI,KAAK;4DACtB,IAAI,MAAM,KAAK,OAAO,KAAK,OAAO;4DAClC,MAAM,cAAc;gEAAE,OAAO;gEAAW,KAAK;gEAAW,MAAM;4DAAU;4DACxE,MAAM,cAAc;gEAAE,MAAM;gEAAW,QAAQ;gEAAW,QAAQ;4DAAK;4DACvE,MAAM,gBAAgB,KAAK,kBAAkB,CAAC,SAAS;4DACvD,MAAM,gBAAgB,KAAK,kBAAkB,CAAC,SAAS;4DACvD,OAAO,GAAG,cAAc,CAAC,EAAE,eAAe;wDAC5C,EAAE,OAAO,OAAO;4DACd,OAAO;wDACT;oDACF;oDACA,OAAO,eAAe,UAAU,cAAc,EAAE;gDAClD,CAAC;8DAAI,8OAAC;;;;;8DACN,8OAAC;8DAAO;;;;;;gDAAuB;gDAAE,CAAC;oDAChC,MAAM,iBAAiB,CAAC;wDACtB,IAAI,CAAC,YAAY,OAAO;wDACxB,IAAI;4DACF,MAAM,OAAO,IAAI,KAAK;4DACtB,IAAI,MAAM,KAAK,OAAO,KAAK,OAAO;4DAClC,MAAM,cAAc;gEAAE,OAAO;gEAAW,KAAK;gEAAW,MAAM;4DAAU;4DACxE,MAAM,cAAc;gEAAE,MAAM;gEAAW,QAAQ;gEAAW,QAAQ;4DAAK;4DACvE,MAAM,gBAAgB,KAAK,kBAAkB,CAAC,SAAS;4DACvD,MAAM,gBAAgB,KAAK,kBAAkB,CAAC,SAAS;4DACvD,OAAO,GAAG,cAAc,CAAC,EAAE,eAAe;wDAC5C,EAAE,OAAO,OAAO;4DACd,OAAO;wDACT;oDACF;oDACA,OAAO,eAAe,UAAU,cAAc,EAAE;gDAClD,CAAC;8DAAI,8OAAC;;;;;8DACN,8OAAC;8DAAO;;;;;;gDAAsC;gDAAE,OAAO,UAAU,cAAc,EAAE,+BAA+B,YAC7G,UAAU,cAAc,CAAC,0BAA0B,GAAG,QAAQ,OAC/D;;;;;;;;;;;;;;;;;;;;;;;wBAOT,UAAU,mBAAmB,kBAC5B,8OAAC,mJAAA,CAAA,UAAI;4BAAC,MAAM;gCAAE,IAAI;4BAAG;sCACnB,cAAA,8OAAC,iJAAA,CAAA,UAAI;gCAAC,WAAU;0CACd,cAAA,8OAAC,+JAAA,CAAA,UAAW;;sDACV,8OAAC,6JAAA,CAAA,UAAU;4CAAC,SAAQ;4CAAK,WAAU;4CAAiB,OAAO;gDAAE,UAAU;gDAAU,cAAc;gDAAqB,eAAe;4CAAM;sDAAG;;;;;;sDAG5I,8OAAC,6JAAA,CAAA,UAAU;4CAAC,SAAQ;4CAAQ,OAAO;gDAAE,UAAU;gDAAU,YAAY;4CAAM;;8DACzE,8OAAC;8DAAO;;;;;;gDAAuB;gDAAE,UAAU,mBAAmB,CAAC,YAAY,IAAI;8DAAgB,8OAAC;;;;;8DAChG,8OAAC;8DAAO;;;;;;gDAAkC;gDAAE,UAAU,mBAAmB,CAAC,wBAAwB,GAAG,QAAQ;8DAAK,8OAAC;;;;;8DACnH,8OAAC;8DAAO;;;;;;gDAAuC;gDAAE,UAAU,mBAAmB,CAAC,2BAA2B,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUlI;uCAEe", "debugId": null}}, {"offset": {"line": 918, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/views/apps/quotes/StatusDropdown.jsx"], "sourcesContent": ["// React Imports\r\nimport React, { useState } from 'react'\r\n\r\n// MUI Imports\r\nimport Chip from '@mui/material/Chip'\r\nimport Menu from '@mui/material/Menu'\r\nimport MenuItem from '@mui/material/MenuItem'\r\nimport ListItemIcon from '@mui/material/ListItemIcon'\r\nimport ListItemText from '@mui/material/ListItemText'\r\n\r\n// Status configuration for quotes\r\nconst statusConfig = {\r\n  pending: { \r\n    label: 'Pending', \r\n    color: 'warning',\r\n    icon: 'tabler-clock',\r\n    bgColor: '#fff3cd',\r\n    textColor: '#856404'\r\n  },\r\n  'in-review': { \r\n    label: 'In Review', \r\n    color: 'info',\r\n    icon: 'tabler-eye',\r\n    bgColor: '#d1ecf1',\r\n    textColor: '#0c5460'\r\n  },\r\n  approved: { \r\n    label: 'Approved', \r\n    color: 'success',\r\n    icon: 'tabler-check',\r\n    bgColor: '#d4edda',\r\n    textColor: '#155724'\r\n  },\r\n  rejected: { \r\n    label: 'Rejected', \r\n    color: 'error',\r\n    icon: 'tabler-x',\r\n    bgColor: '#f8d7da',\r\n    textColor: '#721c24'\r\n  }\r\n}\r\n\r\nconst StatusDropdown = ({ currentStatus, onStatusChange, quoteId }) => {\r\n  const [anchorEl, setAnchorEl] = useState(null)\r\n  const open = Boolean(anchorEl)\r\n\r\n  const handleClick = (event) => {\r\n    setAnchorEl(event.currentTarget)\r\n  }\r\n\r\n  const handleClose = () => {\r\n    setAnchorEl(null)\r\n  }\r\n\r\n  const handleStatusSelect = (newStatus) => {\r\n    onStatusChange(quoteId, newStatus)\r\n    handleClose()\r\n  }\r\n\r\n  const currentConfig = statusConfig[currentStatus] || statusConfig.pending\r\n\r\n  return (\r\n    <>\r\n      <Chip\r\n        label={currentConfig.label}\r\n        color={currentConfig.color}\r\n        variant=\"tonal\"\r\n        size=\"small\"\r\n        icon={<i className={currentConfig.icon} style={{ fontSize: '14px' }} />}\r\n        onClick={handleClick}\r\n        sx={{\r\n          cursor: 'pointer',\r\n          minWidth: '90px',\r\n          height: '28px',\r\n          fontSize: '0.75rem',\r\n          fontWeight: 500,\r\n          '&:hover': {\r\n            backgroundColor: currentConfig.bgColor,\r\n            color: currentConfig.textColor,\r\n            transform: 'scale(1.02)'\r\n          },\r\n          transition: 'all 0.2s ease-in-out'\r\n        }}\r\n      />\r\n      <Menu\r\n        anchorEl={anchorEl}\r\n        open={open}\r\n        onClose={handleClose}\r\n        anchorOrigin={{\r\n          vertical: 'bottom',\r\n          horizontal: 'left'\r\n        }}\r\n        transformOrigin={{\r\n          vertical: 'top',\r\n          horizontal: 'left'\r\n        }}\r\n        PaperProps={{\r\n          sx: {\r\n            minWidth: 140,\r\n            boxShadow: '0 4px 20px rgba(0,0,0,0.1)',\r\n            border: '1px solid rgba(0,0,0,0.05)'\r\n          }\r\n        }}\r\n      >\r\n        {Object.entries(statusConfig).map(([status, config]) => (\r\n          <MenuItem\r\n            key={status}\r\n            onClick={() => handleStatusSelect(status)}\r\n            selected={currentStatus === status}\r\n            sx={{\r\n              '&:hover': {\r\n                backgroundColor: config.bgColor,\r\n                color: config.textColor\r\n              },\r\n              '&.Mui-selected': {\r\n                backgroundColor: config.bgColor,\r\n                color: config.textColor,\r\n                '&:hover': {\r\n                  backgroundColor: config.bgColor,\r\n                  color: config.textColor\r\n                }\r\n              }\r\n            }}\r\n          >\r\n            <ListItemIcon>\r\n              <i \r\n                className={config.icon} \r\n                style={{ \r\n                  fontSize: '16px',\r\n                  color: currentStatus === status ? config.textColor : 'inherit'\r\n                }} \r\n              />\r\n            </ListItemIcon>\r\n            <ListItemText \r\n              primary={config.label}\r\n              sx={{\r\n                '& .MuiListItemText-primary': {\r\n                  fontSize: '0.875rem',\r\n                  fontWeight: currentStatus === status ? 600 : 400\r\n                }\r\n              }}\r\n            />\r\n          </MenuItem>\r\n        ))}\r\n      </Menu>\r\n    </>\r\n  )\r\n}\r\n\r\nexport default StatusDropdown\r\n"], "names": [], "mappings": "AAAA,gBAAgB;;;;;AAChB;AAEA,cAAc;AACd;AACA;AACA;AACA;AACA;;;;;;;;AAEA,kCAAkC;AAClC,MAAM,eAAe;IACnB,SAAS;QACP,OAAO;QACP,OAAO;QACP,MAAM;QACN,SAAS;QACT,WAAW;IACb;IACA,aAAa;QACX,OAAO;QACP,OAAO;QACP,MAAM;QACN,SAAS;QACT,WAAW;IACb;IACA,UAAU;QACR,OAAO;QACP,OAAO;QACP,MAAM;QACN,SAAS;QACT,WAAW;IACb;IACA,UAAU;QACR,OAAO;QACP,OAAO;QACP,MAAM;QACN,SAAS;QACT,WAAW;IACb;AACF;AAEA,MAAM,iBAAiB,CAAC,EAAE,aAAa,EAAE,cAAc,EAAE,OAAO,EAAE;IAChE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,OAAO,QAAQ;IAErB,MAAM,cAAc,CAAC;QACnB,YAAY,MAAM,aAAa;IACjC;IAEA,MAAM,cAAc;QAClB,YAAY;IACd;IAEA,MAAM,qBAAqB,CAAC;QAC1B,eAAe,SAAS;QACxB;IACF;IAEA,MAAM,gBAAgB,YAAY,CAAC,cAAc,IAAI,aAAa,OAAO;IAEzE,qBACE;;0BACE,8OAAC,iJAAA,CAAA,UAAI;gBACH,OAAO,cAAc,KAAK;gBAC1B,OAAO,cAAc,KAAK;gBAC1B,SAAQ;gBACR,MAAK;gBACL,oBAAM,8OAAC;oBAAE,WAAW,cAAc,IAAI;oBAAE,OAAO;wBAAE,UAAU;oBAAO;;;;;;gBAClE,SAAS;gBACT,IAAI;oBACF,QAAQ;oBACR,UAAU;oBACV,QAAQ;oBACR,UAAU;oBACV,YAAY;oBACZ,WAAW;wBACT,iBAAiB,cAAc,OAAO;wBACtC,OAAO,cAAc,SAAS;wBAC9B,WAAW;oBACb;oBACA,YAAY;gBACd;;;;;;0BAEF,8OAAC,iJAAA,CAAA,UAAI;gBACH,UAAU;gBACV,MAAM;gBACN,SAAS;gBACT,cAAc;oBACZ,UAAU;oBACV,YAAY;gBACd;gBACA,iBAAiB;oBACf,UAAU;oBACV,YAAY;gBACd;gBACA,YAAY;oBACV,IAAI;wBACF,UAAU;wBACV,WAAW;wBACX,QAAQ;oBACV;gBACF;0BAEC,OAAO,OAAO,CAAC,cAAc,GAAG,CAAC,CAAC,CAAC,QAAQ,OAAO,iBACjD,8OAAC,yJAAA,CAAA,UAAQ;wBAEP,SAAS,IAAM,mBAAmB;wBAClC,UAAU,kBAAkB;wBAC5B,IAAI;4BACF,WAAW;gCACT,iBAAiB,OAAO,OAAO;gCAC/B,OAAO,OAAO,SAAS;4BACzB;4BACA,kBAAkB;gCAChB,iBAAiB,OAAO,OAAO;gCAC/B,OAAO,OAAO,SAAS;gCACvB,WAAW;oCACT,iBAAiB,OAAO,OAAO;oCAC/B,OAAO,OAAO,SAAS;gCACzB;4BACF;wBACF;;0CAEA,8OAAC,iKAAA,CAAA,UAAY;0CACX,cAAA,8OAAC;oCACC,WAAW,OAAO,IAAI;oCACtB,OAAO;wCACL,UAAU;wCACV,OAAO,kBAAkB,SAAS,OAAO,SAAS,GAAG;oCACvD;;;;;;;;;;;0CAGJ,8OAAC,iKAAA,CAAA,UAAY;gCACX,SAAS,OAAO,KAAK;gCACrB,IAAI;oCACF,8BAA8B;wCAC5B,UAAU;wCACV,YAAY,kBAAkB,SAAS,MAAM;oCAC/C;gCACF;;;;;;;uBAlCG;;;;;;;;;;;;AAyCjB;uCAEe", "debugId": null}}, {"offset": {"line": 1106, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/%40core/components/mui/TextField.jsx"], "sourcesContent": ["'use client'\r\n\r\n// React Imports\r\nimport { forwardRef } from 'react'\r\n\r\n// MUI Imports\r\nimport { styled } from '@mui/material/styles'\r\nimport TextField from '@mui/material/TextField'\r\n\r\nconst TextFieldStyled = styled(TextField)(({ theme }) => ({\r\n  '& .MuiInputLabel-root': {\r\n    transform: 'none',\r\n    width: 'fit-content',\r\n    maxWidth: '100%',\r\n    lineHeight: 1.153,\r\n    position: 'relative',\r\n    fontSize: theme.typography.body2.fontSize,\r\n    marginBottom: theme.spacing(1),\r\n    color: 'var(--mui-palette-text-primary)',\r\n    '&:not(.Mui-error).MuiFormLabel-colorPrimary.Mui-focused': {\r\n      color: 'var(--mui-palette-primary-main) !important'\r\n    },\r\n    '&.Mui-disabled': {\r\n      color: 'var(--mui-palette-text-disabled)'\r\n    },\r\n    '&.Mui-error': {\r\n      color: 'var(--mui-palette-error-main)'\r\n    }\r\n  },\r\n  '& .MuiInputBase-root': {\r\n    backgroundColor: 'transparent !important',\r\n    border: `1px solid var(--mui-palette-customColors-inputBorder)`,\r\n    '&:not(.Mui-focused):not(.Mui-disabled):not(.Mui-error):hover': {\r\n      borderColor: 'var(--mui-palette-action-active)'\r\n    },\r\n    '&:before, &:after': {\r\n      display: 'none'\r\n    },\r\n    '&.MuiInputBase-sizeSmall': {\r\n      borderRadius: 'var(--mui-shape-borderRadius)'\r\n    },\r\n    '&.Mui-error': {\r\n      borderColor: 'var(--mui-palette-error-main)'\r\n    },\r\n    '&.Mui-focused': {\r\n      borderWidth: 2,\r\n      '& .MuiInputBase-input:not(.MuiInputBase-readOnly):not([readonly])::placeholder': {\r\n        transform: 'translateX(4px)'\r\n      },\r\n      '& :not(textarea).MuiFilledInput-input': {\r\n        padding: '6.25px 13px'\r\n      },\r\n      '&:not(.Mui-error).MuiInputBase-colorPrimary': {\r\n        borderColor: 'var(--mui-palette-primary-main)',\r\n        boxShadow: 'var(--mui-customShadows-primary-sm)'\r\n      },\r\n      '&.MuiInputBase-colorSecondary': {\r\n        borderColor: 'var(--mui-palette-secondary-main)'\r\n      },\r\n      '&.MuiInputBase-colorInfo': {\r\n        borderColor: 'var(--mui-palette-info-main)'\r\n      },\r\n      '&.MuiInputBase-colorSuccess': {\r\n        borderColor: 'var(--mui-palette-success-main)'\r\n      },\r\n      '&.MuiInputBase-colorWarning': {\r\n        borderColor: 'var(--mui-palette-warning-main)'\r\n      },\r\n      '&.MuiInputBase-colorError': {\r\n        borderColor: 'var(--mui-palette-error-main)'\r\n      },\r\n      '&.Mui-error': {\r\n        borderColor: 'var(--mui-palette-error-main)'\r\n      }\r\n    },\r\n    '&.Mui-disabled': {\r\n      backgroundColor: 'var(--mui-palette-action-hover) !important'\r\n    }\r\n  },\r\n\r\n  // Adornments\r\n  '& .MuiInputAdornment-root': {\r\n    marginBlockStart: '0px !important',\r\n    '&.MuiInputAdornment-positionStart + .MuiInputBase-input:not(textarea)': {\r\n      paddingInlineStart: '0px !important'\r\n    }\r\n  },\r\n  '& .MuiInputBase-inputAdornedEnd.MuiInputBase-input': {\r\n    paddingInlineEnd: '0px !important'\r\n  },\r\n  '& .MuiInputBase-sizeSmall.MuiInputBase-adornedStart.Mui-focused': {\r\n    paddingInlineStart: '13px',\r\n    '& .MuiInputBase-input': {\r\n      paddingInlineStart: '0px !important'\r\n    }\r\n  },\r\n  '& .MuiInputBase-sizeSmall.MuiInputBase-adornedStart:not(.MuiAutocomplete-inputRoot)': {\r\n    paddingInlineStart: '14px'\r\n  },\r\n  '& .MuiInputBase-sizeSmall.MuiInputBase-adornedEnd:not(.MuiAutocomplete-inputRoot)': {\r\n    paddingInlineEnd: '14px'\r\n  },\r\n  '& .MuiInputBase-sizeSmall.MuiInputBase-adornedEnd.Mui-focused:not(.MuiAutocomplete-inputRoot)': {\r\n    paddingInlineEnd: '13px',\r\n    '& .MuiInputBase-input': {\r\n      paddingInlineEnd: '0px !important'\r\n    }\r\n  },\r\n  '& :not(.MuiInputBase-sizeSmall).MuiInputBase-adornedStart.Mui-focused': {\r\n    paddingInlineStart: '15px',\r\n    '& .MuiInputBase-input': {\r\n      paddingInlineStart: '0px !important'\r\n    }\r\n  },\r\n  '& :not(.MuiInputBase-sizeSmall).MuiInputBase-adornedStart': {\r\n    paddingInlineStart: '16px'\r\n  },\r\n  '& :not(.MuiInputBase-sizeSmall).MuiInputBase-adornedEnd.Mui-focused': {\r\n    paddingInlineEnd: '15px',\r\n    '& .MuiInputBase-input': {\r\n      paddingInlineEnd: '0px !important'\r\n    }\r\n  },\r\n  '& :not(.MuiInputBase-sizeSmall).MuiInputBase-adornedEnd': {\r\n    paddingInlineEnd: '16px'\r\n  },\r\n  '& .MuiInputAdornment-sizeMedium': {\r\n    'i, svg': {\r\n      fontSize: '1.25rem'\r\n    }\r\n  },\r\n  '& .MuiInputBase-input': {\r\n    '&:not(textarea).MuiInputBase-inputSizeSmall': {\r\n      padding: '7.25px 14px'\r\n    },\r\n    '&:not(.MuiInputBase-readOnly):not([readonly])::placeholder': {\r\n      transition: theme.transitions.create(['opacity', 'transform'], {\r\n        duration: theme.transitions.duration.shorter\r\n      })\r\n    }\r\n  },\r\n  '& :not(.MuiInputBase-sizeSmall).MuiInputBase-root': {\r\n    borderRadius: '8px',\r\n    fontSize: '17px',\r\n    lineHeight: '1.41',\r\n    '& .MuiInputBase-input': {\r\n      padding: '10.8px 16px'\r\n    },\r\n    '&.Mui-focused': {\r\n      '& .MuiInputBase-input': {\r\n        padding: '9.8px 15px'\r\n      }\r\n    }\r\n  },\r\n  '& .MuiFormHelperText-root': {\r\n    lineHeight: 1.154,\r\n    margin: theme.spacing(1, 0, 0),\r\n    fontSize: theme.typography.body2.fontSize,\r\n    '&.Mui-error': {\r\n      color: 'var(--mui-palette-error-main)'\r\n    },\r\n    '&.Mui-disabled': {\r\n      color: 'var(--mui-palette-text-disabled)'\r\n    }\r\n  },\r\n\r\n  // For Select\r\n  '& .MuiSelect-select.MuiInputBase-inputSizeSmall, & .MuiNativeSelect-select.MuiInputBase-inputSizeSmall': {\r\n    '& ~ i, & ~ svg': {\r\n      inlineSize: '1.125rem',\r\n      blockSize: '1.125rem'\r\n    }\r\n  },\r\n  '& .MuiSelect-select': {\r\n    // lineHeight: 1.5,\r\n    minHeight: 'unset !important',\r\n    lineHeight: '1.4375em',\r\n    '&.MuiInputBase-input': {\r\n      paddingInlineEnd: '32px !important'\r\n    }\r\n  },\r\n  '& .Mui-focused .MuiSelect-select': {\r\n    '& ~ i, & ~ svg': {\r\n      right: '0.9375rem'\r\n    }\r\n  },\r\n  '& .MuiSelect-select:focus, & .MuiNativeSelect-select:focus': {\r\n    backgroundColor: 'transparent'\r\n  },\r\n\r\n  // For Autocomplete\r\n  '& :not(.MuiInputBase-sizeSmall).MuiAutocomplete-inputRoot': {\r\n    paddingBlock: '5.55px',\r\n    '& .MuiAutocomplete-input': {\r\n      paddingInline: '8px !important',\r\n      paddingBlock: '5.25px !important'\r\n    },\r\n    '&.Mui-focused .MuiAutocomplete-input': {\r\n      paddingInlineStart: '7px !important'\r\n    },\r\n    '&.Mui-focused': {\r\n      paddingBlock: '4.55px !important'\r\n    },\r\n    '& .MuiAutocomplete-endAdornment': {\r\n      top: 'calc(50% - 12px)'\r\n    }\r\n  },\r\n  '& .MuiAutocomplete-inputRoot.MuiInputBase-sizeSmall': {\r\n    paddingBlock: '4.75px !important',\r\n    paddingInlineStart: '10px',\r\n    '&.Mui-focused': {\r\n      paddingBlock: '3.75px !important',\r\n      paddingInlineStart: '9px',\r\n      '.MuiAutocomplete-input': {\r\n        paddingBlock: '2.5px',\r\n        paddingInline: '3px !important'\r\n      }\r\n    },\r\n    '& .MuiAutocomplete-input': {\r\n      paddingInline: '3px !important'\r\n    }\r\n  },\r\n  '& .MuiAutocomplete-inputRoot': {\r\n    display: 'flex',\r\n    gap: '0.25rem',\r\n    '& .MuiAutocomplete-tag': {\r\n      margin: 0\r\n    }\r\n  },\r\n  '& .MuiAutocomplete-inputRoot.Mui-focused .MuiAutocomplete-endAdornment': {\r\n    right: '.9375rem'\r\n  },\r\n\r\n  // For Textarea\r\n  '& .MuiInputBase-multiline': {\r\n    '&.MuiInputBase-sizeSmall': {\r\n      padding: '6px 14px',\r\n      '&.Mui-focused': {\r\n        padding: '5px 13px'\r\n      }\r\n    },\r\n    '& textarea.MuiInputBase-inputSizeSmall:placeholder-shown': {\r\n      overflowX: 'hidden'\r\n    }\r\n  }\r\n}))\r\n\r\nconst CustomTextField = forwardRef((props, ref) => {\r\n  const { size = 'small', slotProps, ...rest } = props\r\n\r\n  return (\r\n    <TextFieldStyled\r\n      size={size}\r\n      inputRef={ref}\r\n      {...rest}\r\n      variant='filled'\r\n      slotProps={{\r\n        ...slotProps,\r\n        inputLabel: { ...slotProps?.inputLabel, shrink: true }\r\n      }}\r\n    />\r\n  )\r\n})\r\n\r\nexport default CustomTextField\r\n"], "names": [], "mappings": ";;;;AAEA,gBAAgB;AAChB;AAEA,cAAc;AACd;AACA;AAPA;;;;;AASA,MAAM,kBAAkB,CAAA,GAAA,0MAAA,CAAA,SAAM,AAAD,EAAE,2JAAA,CAAA,UAAS,EAAE,CAAC,EAAE,KAAK,EAAE,GAAK,CAAC;QACxD,yBAAyB;YACvB,WAAW;YACX,OAAO;YACP,UAAU;YACV,YAAY;YACZ,UAAU;YACV,UAAU,MAAM,UAAU,CAAC,KAAK,CAAC,QAAQ;YACzC,cAAc,MAAM,OAAO,CAAC;YAC5B,OAAO;YACP,2DAA2D;gBACzD,OAAO;YACT;YACA,kBAAkB;gBAChB,OAAO;YACT;YACA,eAAe;gBACb,OAAO;YACT;QACF;QACA,wBAAwB;YACtB,iBAAiB;YACjB,QAAQ,CAAC,qDAAqD,CAAC;YAC/D,gEAAgE;gBAC9D,aAAa;YACf;YACA,qBAAqB;gBACnB,SAAS;YACX;YACA,4BAA4B;gBAC1B,cAAc;YAChB;YACA,eAAe;gBACb,aAAa;YACf;YACA,iBAAiB;gBACf,aAAa;gBACb,kFAAkF;oBAChF,WAAW;gBACb;gBACA,yCAAyC;oBACvC,SAAS;gBACX;gBACA,+CAA+C;oBAC7C,aAAa;oBACb,WAAW;gBACb;gBACA,iCAAiC;oBAC/B,aAAa;gBACf;gBACA,4BAA4B;oBAC1B,aAAa;gBACf;gBACA,+BAA+B;oBAC7B,aAAa;gBACf;gBACA,+BAA+B;oBAC7B,aAAa;gBACf;gBACA,6BAA6B;oBAC3B,aAAa;gBACf;gBACA,eAAe;oBACb,aAAa;gBACf;YACF;YACA,kBAAkB;gBAChB,iBAAiB;YACnB;QACF;QAEA,aAAa;QACb,6BAA6B;YAC3B,kBAAkB;YAClB,yEAAyE;gBACvE,oBAAoB;YACtB;QACF;QACA,sDAAsD;YACpD,kBAAkB;QACpB;QACA,mEAAmE;YACjE,oBAAoB;YACpB,yBAAyB;gBACvB,oBAAoB;YACtB;QACF;QACA,uFAAuF;YACrF,oBAAoB;QACtB;QACA,qFAAqF;YACnF,kBAAkB;QACpB;QACA,iGAAiG;YAC/F,kBAAkB;YAClB,yBAAyB;gBACvB,kBAAkB;YACpB;QACF;QACA,yEAAyE;YACvE,oBAAoB;YACpB,yBAAyB;gBACvB,oBAAoB;YACtB;QACF;QACA,6DAA6D;YAC3D,oBAAoB;QACtB;QACA,uEAAuE;YACrE,kBAAkB;YAClB,yBAAyB;gBACvB,kBAAkB;YACpB;QACF;QACA,2DAA2D;YACzD,kBAAkB;QACpB;QACA,mCAAmC;YACjC,UAAU;gBACR,UAAU;YACZ;QACF;QACA,yBAAyB;YACvB,+CAA+C;gBAC7C,SAAS;YACX;YACA,8DAA8D;gBAC5D,YAAY,MAAM,WAAW,CAAC,MAAM,CAAC;oBAAC;oBAAW;iBAAY,EAAE;oBAC7D,UAAU,MAAM,WAAW,CAAC,QAAQ,CAAC,OAAO;gBAC9C;YACF;QACF;QACA,qDAAqD;YACnD,cAAc;YACd,UAAU;YACV,YAAY;YACZ,yBAAyB;gBACvB,SAAS;YACX;YACA,iBAAiB;gBACf,yBAAyB;oBACvB,SAAS;gBACX;YACF;QACF;QACA,6BAA6B;YAC3B,YAAY;YACZ,QAAQ,MAAM,OAAO,CAAC,GAAG,GAAG;YAC5B,UAAU,MAAM,UAAU,CAAC,KAAK,CAAC,QAAQ;YACzC,eAAe;gBACb,OAAO;YACT;YACA,kBAAkB;gBAChB,OAAO;YACT;QACF;QAEA,aAAa;QACb,0GAA0G;YACxG,kBAAkB;gBAChB,YAAY;gBACZ,WAAW;YACb;QACF;QACA,uBAAuB;YACrB,mBAAmB;YACnB,WAAW;YACX,YAAY;YACZ,wBAAwB;gBACtB,kBAAkB;YACpB;QACF;QACA,oCAAoC;YAClC,kBAAkB;gBAChB,OAAO;YACT;QACF;QACA,8DAA8D;YAC5D,iBAAiB;QACnB;QAEA,mBAAmB;QACnB,6DAA6D;YAC3D,cAAc;YACd,4BAA4B;gBAC1B,eAAe;gBACf,cAAc;YAChB;YACA,wCAAwC;gBACtC,oBAAoB;YACtB;YACA,iBAAiB;gBACf,cAAc;YAChB;YACA,mCAAmC;gBACjC,KAAK;YACP;QACF;QACA,uDAAuD;YACrD,cAAc;YACd,oBAAoB;YACpB,iBAAiB;gBACf,cAAc;gBACd,oBAAoB;gBACpB,0BAA0B;oBACxB,cAAc;oBACd,eAAe;gBACjB;YACF;YACA,4BAA4B;gBAC1B,eAAe;YACjB;QACF;QACA,gCAAgC;YAC9B,SAAS;YACT,KAAK;YACL,0BAA0B;gBACxB,QAAQ;YACV;QACF;QACA,0EAA0E;YACxE,OAAO;QACT;QAEA,eAAe;QACf,6BAA6B;YAC3B,4BAA4B;gBAC1B,SAAS;gBACT,iBAAiB;oBACf,SAAS;gBACX;YACF;YACA,4DAA4D;gBAC1D,WAAW;YACb;QACF;IACF,CAAC;AAED,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,CAAC,OAAO;IACzC,MAAM,EAAE,OAAO,OAAO,EAAE,SAAS,EAAE,GAAG,MAAM,GAAG;IAE/C,qBACE,8OAAC;QACC,MAAM;QACN,UAAU;QACT,GAAG,IAAI;QACR,SAAQ;QACR,WAAW;YACT,GAAG,SAAS;YACZ,YAAY;gBAAE,GAAG,WAAW,UAAU;gBAAE,QAAQ;YAAK;QACvD;;;;;;AAGN;uCAEe", "debugId": null}}, {"offset": {"line": 1383, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/%40core/components/mui/Avatar.jsx"], "sourcesContent": ["'use client'\r\n\r\n// React Imports\r\nimport { forwardRef } from 'react'\r\n\r\n// MUI Imports\r\nimport MuiAvatar from '@mui/material/Avatar'\r\nimport { lighten, styled } from '@mui/material/styles'\r\n\r\nconst Avatar = styled(MuiAvatar)(({ skin, color, size, theme }) => {\r\n  return {\r\n    ...(color &&\r\n      skin === 'light' && {\r\n        backgroundColor: `var(--mui-palette-${color}-lightOpacity)`,\r\n        color: `var(--mui-palette-${color}-main)`\r\n      }),\r\n    ...(color &&\r\n      skin === 'light-static' && {\r\n        backgroundColor: lighten(theme.palette[color].main, 0.84),\r\n        color: `var(--mui-palette-${color}-main)`\r\n      }),\r\n    ...(color &&\r\n      skin === 'filled' && {\r\n        backgroundColor: `var(--mui-palette-${color}-main)`,\r\n        color: `var(--mui-palette-${color}-contrastText)`\r\n      }),\r\n    ...(size && {\r\n      height: size,\r\n      width: size\r\n    })\r\n  }\r\n})\r\n\r\nconst CustomAvatar = forwardRef((props, ref) => {\r\n  // Props\r\n  const { color, skin = 'filled', ...rest } = props\r\n\r\n  return <Avatar color={color} skin={skin} ref={ref} {...rest} />\r\n})\r\n\r\nexport default CustomAvatar\r\n"], "names": [], "mappings": ";;;;AAEA,gBAAgB;AAChB;AAEA,cAAc;AACd;AACA;AAAA;AAPA;;;;;AASA,MAAM,SAAS,CAAA,GAAA,0MAAA,CAAA,SAAM,AAAD,EAAE,qJAAA,CAAA,UAAS,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE;IAC5D,OAAO;QACL,GAAI,SACF,SAAS,WAAW;YAClB,iBAAiB,CAAC,kBAAkB,EAAE,MAAM,cAAc,CAAC;YAC3D,OAAO,CAAC,kBAAkB,EAAE,MAAM,MAAM,CAAC;QAC3C,CAAC;QACH,GAAI,SACF,SAAS,kBAAkB;YACzB,iBAAiB,CAAA,GAAA,8KAAA,CAAA,UAAO,AAAD,EAAE,MAAM,OAAO,CAAC,MAAM,CAAC,IAAI,EAAE;YACpD,OAAO,CAAC,kBAAkB,EAAE,MAAM,MAAM,CAAC;QAC3C,CAAC;QACH,GAAI,SACF,SAAS,YAAY;YACnB,iBAAiB,CAAC,kBAAkB,EAAE,MAAM,MAAM,CAAC;YACnD,OAAO,CAAC,kBAAkB,EAAE,MAAM,cAAc,CAAC;QACnD,CAAC;QACH,GAAI,QAAQ;YACV,QAAQ;YACR,OAAO;QACT,CAAC;IACH;AACF;AAEA,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,CAAC,OAAO;IACtC,QAAQ;IACR,MAAM,EAAE,KAAK,EAAE,OAAO,QAAQ,EAAE,GAAG,MAAM,GAAG;IAE5C,qBAAO,8OAAC;QAAO,OAAO;QAAO,MAAM;QAAM,KAAK;QAAM,GAAG,IAAI;;;;;;AAC7D;uCAEe", "debugId": null}}, {"offset": {"line": 1439, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/components/TablePaginationComponent.jsx"], "sourcesContent": ["// MUI Imports\r\nimport Pagination from '@mui/material/Pagination'\r\nimport Typography from '@mui/material/Typography'\r\n\r\nconst TablePaginationComponent = ({ table }) => {\r\n  return (\r\n    <div className='flex justify-between items-center flex-wrap pli-6 border-bs bs-auto plb-[12.5px] gap-2'>\r\n      <Typography color='text.disabled'>\r\n        {`Showing ${\r\n          table.getFilteredRowModel().rows.length === 0\r\n            ? 0\r\n            : table.getState().pagination.pageIndex * table.getState().pagination.pageSize + 1\r\n        }\r\n        to ${Math.min((table.getState().pagination.pageIndex + 1) * table.getState().pagination.pageSize, table.getFilteredRowModel().rows.length)} of ${table.getFilteredRowModel().rows.length} entries`}\r\n      </Typography>\r\n      <Pagination\r\n        shape='rounded'\r\n        color='primary'\r\n        variant='tonal'\r\n        count={Math.ceil(table.getFilteredRowModel().rows.length / table.getState().pagination.pageSize)}\r\n        page={table.getState().pagination.pageIndex + 1}\r\n        onChange={(_, page) => {\r\n          table.setPageIndex(page - 1)\r\n        }}\r\n        showFirstButton\r\n        showLastButton\r\n      />\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default TablePaginationComponent\r\n"], "names": [], "mappings": "AAAA,cAAc;;;;;AACd;AACA;;;;AAEA,MAAM,2BAA2B,CAAC,EAAE,KAAK,EAAE;IACzC,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,6JAAA,CAAA,UAAU;gBAAC,OAAM;0BACf,CAAC,QAAQ,EACR,MAAM,mBAAmB,GAAG,IAAI,CAAC,MAAM,KAAK,IACxC,IACA,MAAM,QAAQ,GAAG,UAAU,CAAC,SAAS,GAAG,MAAM,QAAQ,GAAG,UAAU,CAAC,QAAQ,GAAG,EACpF;WACE,EAAE,KAAK,GAAG,CAAC,CAAC,MAAM,QAAQ,GAAG,UAAU,CAAC,SAAS,GAAG,CAAC,IAAI,MAAM,QAAQ,GAAG,UAAU,CAAC,QAAQ,EAAE,MAAM,mBAAmB,GAAG,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,MAAM,mBAAmB,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC;;;;;;0BAEpM,8OAAC,6JAAA,CAAA,UAAU;gBACT,OAAM;gBACN,OAAM;gBACN,SAAQ;gBACR,OAAO,KAAK,IAAI,CAAC,MAAM,mBAAmB,GAAG,IAAI,CAAC,MAAM,GAAG,MAAM,QAAQ,GAAG,UAAU,CAAC,QAAQ;gBAC/F,MAAM,MAAM,QAAQ,GAAG,UAAU,CAAC,SAAS,GAAG;gBAC9C,UAAU,CAAC,GAAG;oBACZ,MAAM,YAAY,CAAC,OAAO;gBAC5B;gBACA,eAAe;gBACf,cAAc;;;;;;;;;;;;AAItB;uCAEe", "debugId": null}}, {"offset": {"line": 1596, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/services/quoteApi.js"], "sourcesContent": ["import axios from 'axios';\r\n\r\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8090';\r\nconst API_KEY = process.env.NEXT_PUBLIC_API_KEY;\r\n\r\n/**\r\n * @typedef {Object} ContactInfo\r\n * @property {string} firstName\r\n * @property {string} lastName\r\n * @property {string} email\r\n * @property {string} phone\r\n * @property {string} [company]\r\n */\r\n\r\n/**\r\n * @typedef {Object} ShipmentDetails\r\n * @property {string} freightType\r\n * @property {string} weight\r\n * @property {string} dimensions\r\n * @property {string} commodity\r\n */\r\n\r\n/**\r\n * @typedef {Object} PickupDelivery\r\n * @property {string} pickupLocation\r\n * @property {string} deliveryLocation\r\n * @property {string} pickupDate\r\n * @property {string} deliveryDate\r\n */\r\n\r\n/**\r\n * @typedef {Object} SpecialRequirements\r\n * @property {boolean} liftGate\r\n * @property {boolean} insideDelivery\r\n * @property {boolean} notification\r\n * @property {string} [other]\r\n */\r\n\r\n/**\r\n * @typedef {Object} Quote\r\n * @property {string} _id\r\n * @property {ContactInfo} contactInfo\r\n * @property {ShipmentDetails} shipmentDetails\r\n * @property {PickupDelivery} pickupDelivery\r\n * @property {SpecialRequirements} [specialRequirements]\r\n * @property {string} createdAt\r\n * @property {string} updatedAt\r\n */\r\n\r\n/**\r\n * @typedef {Object} QuotesResponse\r\n * @property {boolean} success\r\n * @property {string} message\r\n * @property {Quote[]} quotes\r\n */\r\n\r\n/**\r\n * Fetches all quotes from the API\r\n * @returns {Promise<QuotesResponse>}\r\n * @throws {Error} If the API call fails\r\n */\r\nexport const fetchQuotes = async () => {\r\n  if (!API_KEY) {\r\n    throw new Error('API key is not configured. Please set NEXT_PUBLIC_API_KEY in your environment variables.');\r\n  }\r\n\r\n  try {\r\n    const response = await axios.get(`${API_BASE_URL}/quote/get-quotes`, {\r\n      headers: {\r\n        'x-api-key': API_KEY,\r\n        'Content-Type': 'application/json'\r\n      }\r\n    });\r\n\r\n    console.log(response.data);\r\n    return response.data;\r\n  } catch (error) {\r\n    if (error.response) {\r\n      switch (error.response.status) {\r\n        case 401:\r\n          throw new Error('Invalid API key');\r\n        case 404:\r\n          // Return empty quotes array with success true\r\n          return { success: true, message: 'No quotes found', quotes: [] };\r\n        case 500:\r\n          throw new Error('Server error occurred');\r\n        default:\r\n          throw new Error(`Unexpected error: ${error.response.data.message}`);\r\n      }\r\n    }\r\n    throw new Error('Network error occurred');\r\n  }\r\n};\r\n\r\n/**\r\n * Updates a quote's status\r\n * @param {string} quoteId - The ID of the quote to update\r\n * @param {string} status - The new status (pending, in-review, approved, rejected)\r\n * @returns {Promise<{success: boolean, message: string}>}\r\n * @throws {Error} If the API call fails\r\n */\r\nexport const updateQuoteStatus = async (quoteId, status) => {\r\n  if (!API_KEY) {\r\n    throw new Error('API key is not configured. Please set NEXT_PUBLIC_API_KEY in your environment variables.');\r\n  }\r\n\r\n  if (!quoteId) {\r\n    throw new Error('Quote ID is required');\r\n  }\r\n\r\n  if (!status) {\r\n    throw new Error('Status is required');\r\n  }\r\n\r\n  try {\r\n    console.log('API: Updating quote status', quoteId, 'to', status);\r\n    console.log('API URL:', `${API_BASE_URL}/quote/update-status/${quoteId}`);\r\n\r\n    const response = await axios.patch(`${API_BASE_URL}/quote/update-status/${quoteId}`,\r\n      { status },\r\n      {\r\n        headers: {\r\n          'x-api-key': API_KEY,\r\n          'Content-Type': 'application/json'\r\n        }\r\n      }\r\n    );\r\n\r\n    console.log('API Response status:', response.status);\r\n    console.log('API Success result:', response.data);\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('Error updating quote status:', error);\r\n    if (error.response) {\r\n      switch (error.response.status) {\r\n        case 401:\r\n          throw new Error('Invalid API key');\r\n        case 404:\r\n          throw new Error('Quote not found');\r\n        case 500:\r\n          throw new Error('Server error occurred while updating quote status');\r\n        default:\r\n          throw new Error(`Unexpected error: ${error.response.data.message || 'Failed to update quote status'}`);\r\n      }\r\n    }\r\n    throw new Error('Network error occurred while updating quote status');\r\n  }\r\n};\r\n\r\n/**\r\n * Deletes a quote by ID\r\n * @param {string} quoteId - The ID of the quote to delete\r\n * @returns {Promise<{success: boolean, message: string}>}\r\n * @throws {Error} If the API call fails\r\n */\r\nexport const deleteQuote = async (quoteId) => {\r\n  if (!API_KEY) {\r\n    throw new Error('API key is not configured. Please set NEXT_PUBLIC_API_KEY in your environment variables.');\r\n  }\r\n\r\n  if (!quoteId) {\r\n    throw new Error('Quote ID is required');\r\n  }\r\n\r\n  try {\r\n    const response = await axios.delete(`${API_BASE_URL}/quote/delete-quote/${quoteId}`, {\r\n      headers: {\r\n        'x-api-key': API_KEY,\r\n        'Content-Type': 'application/json'\r\n      }\r\n    });\r\n\r\n    console.log('Quote deleted successfully:', response.data);\r\n    return response.data;\r\n  } catch (error) {\r\n    if (error.response) {\r\n      switch (error.response.status) {\r\n        case 401:\r\n          throw new Error('Invalid API key');\r\n        case 404:\r\n          throw new Error('Quote not found');\r\n        case 500:\r\n          throw new Error('Server error occurred while deleting quote');\r\n        default:\r\n          throw new Error(`Unexpected error: ${error.response.data.message || 'Failed to delete quote'}`);\r\n      }\r\n    }\r\n    throw new Error('Network error occurred while deleting quote');\r\n  }\r\n};\r\n"], "names": [], "mappings": ";;;;;AAAA;;AAEA,MAAM,eAAe,6DAAwC;AAC7D,MAAM;AA0DC,MAAM,cAAc;IACzB,uCAAc;;IAEd;IAEA,IAAI;QACF,MAAM,WAAW,MAAM,qIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,GAAG,aAAa,iBAAiB,CAAC,EAAE;YACnE,SAAS;gBACP,aAAa;gBACb,gBAAgB;YAClB;QACF;QAEA,QAAQ,GAAG,CAAC,SAAS,IAAI;QACzB,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,IAAI,MAAM,QAAQ,EAAE;YAClB,OAAQ,MAAM,QAAQ,CAAC,MAAM;gBAC3B,KAAK;oBACH,MAAM,IAAI,MAAM;gBAClB,KAAK;oBACH,8CAA8C;oBAC9C,OAAO;wBAAE,SAAS;wBAAM,SAAS;wBAAmB,QAAQ,EAAE;oBAAC;gBACjE,KAAK;oBACH,MAAM,IAAI,MAAM;gBAClB;oBACE,MAAM,IAAI,MAAM,CAAC,kBAAkB,EAAE,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE;YACtE;QACF;QACA,MAAM,IAAI,MAAM;IAClB;AACF;AASO,MAAM,oBAAoB,OAAO,SAAS;IAC/C,uCAAc;;IAEd;IAEA,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IAEA,IAAI,CAAC,QAAQ;QACX,MAAM,IAAI,MAAM;IAClB;IAEA,IAAI;QACF,QAAQ,GAAG,CAAC,8BAA8B,SAAS,MAAM;QACzD,QAAQ,GAAG,CAAC,YAAY,GAAG,aAAa,qBAAqB,EAAE,SAAS;QAExE,MAAM,WAAW,MAAM,qIAAA,CAAA,UAAK,CAAC,KAAK,CAAC,GAAG,aAAa,qBAAqB,EAAE,SAAS,EACjF;YAAE;QAAO,GACT;YACE,SAAS;gBACP,aAAa;gBACb,gBAAgB;YAClB;QACF;QAGF,QAAQ,GAAG,CAAC,wBAAwB,SAAS,MAAM;QACnD,QAAQ,GAAG,CAAC,uBAAuB,SAAS,IAAI;QAChD,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,IAAI,MAAM,QAAQ,EAAE;YAClB,OAAQ,MAAM,QAAQ,CAAC,MAAM;gBAC3B,KAAK;oBACH,MAAM,IAAI,MAAM;gBAClB,KAAK;oBACH,MAAM,IAAI,MAAM;gBAClB,KAAK;oBACH,MAAM,IAAI,MAAM;gBAClB;oBACE,MAAM,IAAI,MAAM,CAAC,kBAAkB,EAAE,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO,IAAI,iCAAiC;YACzG;QACF;QACA,MAAM,IAAI,MAAM;IAClB;AACF;AAQO,MAAM,cAAc,OAAO;IAChC,uCAAc;;IAEd;IAEA,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IAEA,IAAI;QACF,MAAM,WAAW,MAAM,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC,GAAG,aAAa,oBAAoB,EAAE,SAAS,EAAE;YACnF,SAAS;gBACP,aAAa;gBACb,gBAAgB;YAClB;QACF;QAEA,QAAQ,GAAG,CAAC,+BAA+B,SAAS,IAAI;QACxD,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,IAAI,MAAM,QAAQ,EAAE;YAClB,OAAQ,MAAM,QAAQ,CAAC,MAAM;gBAC3B,KAAK;oBACH,MAAM,IAAI,MAAM;gBAClB,KAAK;oBACH,MAAM,IAAI,MAAM;gBAClB,KAAK;oBACH,MAAM,IAAI,MAAM;gBAClB;oBACE,MAAM,IAAI,MAAM,CAAC,kBAAkB,EAAE,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO,IAAI,0BAA0B;YAClG;QACF;QACA,MAAM,IAAI,MAAM;IAClB;AACF", "debugId": null}}, {"offset": {"line": 1717, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/@core/styles/table.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"cellWithInput\": \"table-module__Mig-TG__cellWithInput\",\n  \"table\": \"table-module__Mig-TG__table\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA"}}, {"offset": {"line": 1727, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/views/apps/quotes/QuotesTable.jsx"], "sourcesContent": ["'use client'\r\n\r\n// React Imports\r\nimport { useState, useEffect, useMemo } from 'react'\r\n\r\n// MUI Imports\r\nimport Card from '@mui/material/Card'\r\nimport CardHeader from '@mui/material/CardHeader'\r\nimport CardContent from '@mui/material/CardContent'\r\nimport Button from '@mui/material/Button'\r\nimport Typography from '@mui/material/Typography'\r\nimport IconButton from '@mui/material/IconButton'\r\nimport Chip from '@mui/material/Chip'\r\nimport Box from '@mui/material/Box'\r\nimport Alert from '@mui/material/Alert'\r\n\r\n// Third-party Imports\r\nimport classnames from 'classnames'\r\nimport { rankItem } from '@tanstack/match-sorter-utils'\r\nimport {\r\n    createColumnHelper,\r\n    flexRender,\r\n    getCoreRowModel,\r\n    useReactTable,\r\n    getFilteredRowModel,\r\n    getFacetedRowModel,\r\n    getFacetedUniqueValues,\r\n    getFacetedMinMaxValues,\r\n    getPaginationRowModel,\r\n    getSortedRowModel\r\n} from '@tanstack/react-table'\r\n\r\n// Component Imports\r\nimport QuoteDetailsModal from './QuoteDetailsModal'\r\nimport StatusDropdown from './StatusDropdown'\r\nimport CustomTextField from '@core/components/mui/TextField'\r\nimport CustomAvatar from '@core/components/mui/Avatar'\r\nimport TablePaginationComponent from '@components/TablePaginationComponent'\r\n\r\n// API Imports\r\nimport { fetchQuotes, deleteQuote, updateQuoteStatus } from '@/services/quoteApi'\r\n\r\n// Style Imports\r\nimport tableStyles from '@core/styles/table.module.css'\r\n\r\n// Fuzzy Filter function\r\nconst fuzzyFilter = (row, columnId, value, addMeta) => {\r\n    const itemRank = rankItem(row.getValue(columnId), value)\r\n    addMeta({\r\n        itemRank\r\n    })\r\n    return itemRank.passed\r\n}\r\n\r\n// DebouncedInput component\r\nconst DebouncedInput = ({ value: initialValue, onChange, debounce = 500, ...props }) => {\r\n    const [value, setValue] = useState(initialValue)\r\n\r\n    useEffect(() => {\r\n        setValue(initialValue)\r\n    }, [initialValue])\r\n\r\n    useEffect(() => {\r\n        const timeout = setTimeout(() => {\r\n            onChange(value)\r\n        }, debounce)\r\n\r\n        return () => clearTimeout(timeout)\r\n    }, [value])\r\n\r\n    return <CustomTextField {...props} value={value} onChange={e => setValue(e.target.value)} />\r\n}\r\n\r\n// Column Definitions\r\nconst columnHelper = createColumnHelper()\r\n\r\nconst QuotesTable = () => {\r\n    // States\r\n    const [data, setData] = useState([])\r\n    const [filteredData, setFilteredData] = useState([])\r\n    const [globalFilter, setGlobalFilter] = useState('')\r\n    const [loading, setLoading] = useState(true)\r\n    const [error, setError] = useState(null)\r\n    const [selectedQuote, setSelectedQuote] = useState(null)\r\n    const [showModal, setShowModal] = useState(false)\r\n\r\n    // Load quotes data\r\n    useEffect(() => {\r\n        const loadQuotes = async () => {\r\n            try {\r\n                setLoading(true)\r\n                setError(null)\r\n                const response = await fetchQuotes()\r\n                if (response.success) {\r\n                    // Load saved statuses from localStorage\r\n                    const savedStatuses = JSON.parse(localStorage.getItem('quoteStatuses') || '{}')\r\n\r\n                    // Add default status to quotes that don't have one, prioritizing localStorage\r\n                    const quotesWithStatus = response.quotes.map(quote => ({\r\n                        ...quote,\r\n                        status: savedStatuses[quote._id] || quote.status || 'pending'\r\n                    }))\r\n                    setData(quotesWithStatus)\r\n                    setFilteredData(quotesWithStatus)\r\n                } else {\r\n                    setError('Failed to load quotes')\r\n                }\r\n            } catch (err) {\r\n                console.error('Error loading quotes:', err)\r\n                setError(err.message || 'Failed to load quotes. Please try again.')\r\n                setData([])\r\n                setFilteredData([])\r\n            } finally {\r\n                setLoading(false)\r\n            }\r\n        }\r\n\r\n        loadQuotes()\r\n    }, [])\r\n\r\n    // Handlers\r\n    const handleViewDetails = (quote) => {\r\n        setSelectedQuote(quote)\r\n        setShowModal(true)\r\n    }\r\n\r\n    const handleCloseModal = () => {\r\n        setShowModal(false)\r\n        setSelectedQuote(null)\r\n    }\r\n\r\n    const handleDeleteQuote = async (quoteId) => {\r\n        const quote = data.find(q => q._id === quoteId)\r\n        const contactName = quote?.contactInfo?.fullName || 'Unknown'\r\n\r\n        if (window.confirm(`Are you sure you want to delete the quote for ${contactName}? This action cannot be undone.`)) {\r\n            try {\r\n                await deleteQuote(quoteId)\r\n                const updatedQuotes = data.filter(quote => quote._id !== quoteId)\r\n                setData(updatedQuotes)\r\n                setFilteredData(updatedQuotes)\r\n                alert('Quote deleted successfully!')\r\n            } catch (error) {\r\n                console.error('Error deleting quote:', error)\r\n                alert(`Failed to delete quote: ${error.message}`)\r\n            }\r\n        }\r\n    }\r\n\r\n    // Handle status change\r\n    const handleStatusChange = async (quoteId, newStatus) => {\r\n        console.log('Updating status for quote:', quoteId, 'to:', newStatus)\r\n\r\n        try {\r\n            // Call API to update status in backend\r\n            await updateQuoteStatus(quoteId, newStatus)\r\n            console.log('Quote status updated in backend successfully')\r\n\r\n            // Update local state only after successful backend update\r\n            const updateQuoteInState = (quotes) =>\r\n                quotes.map(quote =>\r\n                    quote._id === quoteId\r\n                        ? { ...quote, status: newStatus }\r\n                        : quote\r\n                )\r\n\r\n            setData(updateQuoteInState)\r\n            setFilteredData(updateQuoteInState)\r\n        } catch (error) {\r\n            console.error('Failed to update quote status:', error)\r\n            alert('Failed to update status. Please try again.')\r\n        }\r\n\r\n        console.log('Quote status updated in frontend successfully')\r\n    }\r\n\r\n    // Action button styles\r\n    const actionButtonStyles = {\r\n        color: 'text.secondary',\r\n        '&:hover': {\r\n            backgroundColor: 'action.hover',\r\n            transform: 'scale(1.1)'\r\n        },\r\n        transition: 'all 0.2s ease-in-out',\r\n        minWidth: '32px',\r\n        width: '32px',\r\n        height: '32px',\r\n        padding: '4px'\r\n    }\r\n\r\n    // Column definitions\r\n    const columns = useMemo(\r\n        () => [\r\n            columnHelper.accessor('contactInfo.fullName', {\r\n                header: 'Contact Person',\r\n                cell: ({ row }) => (\r\n                    <div className='flex items-center gap-4'>\r\n                        <CustomAvatar\r\n                            variant='rounded'\r\n                            color='primary'\r\n                            skin='light'\r\n                            size={34}\r\n                        >\r\n                            {row.original.contactInfo?.fullName?.charAt(0)?.toUpperCase() || 'Q'}\r\n                        </CustomAvatar>\r\n                        <div className='flex flex-col'>\r\n                            <Typography color='text.primary' className='font-medium' style={{ fontSize: '1.1rem' }}>\r\n                                {row.original.contactInfo?.fullName || 'Unknown'}\r\n                            </Typography>\r\n                            <Typography variant='body1' color='text.primary' className='font-medium' style={{ fontSize: '1rem', letterSpacing: '1px' }}>\r\n                                {row.original.contactInfo?.emailAddress || 'No email'}\r\n                            </Typography>\r\n                        </div>\r\n                    </div>\r\n                )\r\n            }),\r\n            columnHelper.accessor('contactInfo.phoneNumber', {\r\n                header: 'Contact',\r\n                cell: ({ row }) => (\r\n                    <Typography color='text.primary' className='font-medium' style={{ fontSize: '1.1rem' }}>\r\n                        {row.original.contactInfo?.phoneNumber || 'Not provided'}\r\n                    </Typography>\r\n                )\r\n            }),\r\n            columnHelper.accessor('shipmentDetails.loadType', {\r\n                header: 'Freight Type',\r\n                cell: ({ row }) => (\r\n                    <Typography color='text.primary' className='font-medium' style={{ fontSize: '1.1rem' }}>\r\n                        {row.original.shipmentDetails?.loadType || 'Not specified'}\r\n                    </Typography>\r\n                )\r\n            }),\r\n            columnHelper.accessor('pickupDelivery', {\r\n                header: 'Pickup Location',\r\n                cell: ({ row }) => {\r\n                    const pickup = row.original.pickupDelivery?.pickupLocation\r\n                    return (\r\n                        <Typography color='text.primary' className='font-medium' style={{ fontSize: '1.1rem' }}>\r\n                            {pickup ? `${pickup.city}, ${pickup.stateOrProvince}` : 'Not specified'}\r\n                        </Typography>\r\n                    )\r\n                }\r\n            }),\r\n            columnHelper.accessor('deliveryLocation', {\r\n                header: 'Delivery Location',\r\n                cell: ({ row }) => {\r\n                    const delivery = row.original.pickupDelivery?.deliveryLocation\r\n                    return (\r\n                        <Typography color='text.primary' className='font-medium' style={{ fontSize: '1.1rem' }}>\r\n                            {delivery ? `${delivery.city}, ${delivery.stateOrProvince}` : 'Not specified'}\r\n                        </Typography>\r\n                    )\r\n                }\r\n            }),\r\n            columnHelper.accessor('createdAt', {\r\n                header: 'Date Created',\r\n                cell: ({ row }) => {\r\n                    const formatDateTime = (dateString) => {\r\n                        if (!dateString) return 'Not available'\r\n\r\n                        try {\r\n                            // Handle both ISO string and already formatted dates\r\n                            const date = new Date(dateString)\r\n                            if (isNaN(date.getTime())) return dateString // Return original if invalid date\r\n\r\n                            // Format: MM/DD/YYYY HH:MM AM/PM (using actual submission time from database)\r\n                            const dateOptions = {\r\n                                month: '2-digit',\r\n                                day: '2-digit',\r\n                                year: 'numeric'\r\n                            }\r\n                            const timeOptions = {\r\n                                hour: '2-digit',\r\n                                minute: '2-digit',\r\n                                hour12: true\r\n                            }\r\n\r\n                            const formattedDate = date.toLocaleDateString('en-US', dateOptions)\r\n                            const formattedTime = date.toLocaleTimeString('en-US', timeOptions)\r\n\r\n                            return `${formattedDate} ${formattedTime}`\r\n                        } catch (error) {\r\n                            return dateString // Return original if formatting fails\r\n                        }\r\n                    }\r\n\r\n                    const date = row.original.createdAt || row.original.pickupDelivery?.preferredPickupDate\r\n                    return (\r\n                        <div className='flex flex-col'>\r\n                            <Typography color='text.primary' style={{ fontSize: '0.95rem', lineHeight: '1.2' }}>\r\n                                {formatDateTime(date)}\r\n                            </Typography>\r\n                        </div>\r\n                    )\r\n                }\r\n            }),\r\n            columnHelper.accessor('status', {\r\n                header: 'Status',\r\n                cell: ({ row }) => {\r\n                    const status = row.original.status || 'pending'\r\n\r\n                    return (\r\n                        <div className='flex items-center justify-start gap-1 min-w-[110px] sm:min-w-[120px] pr-3'>\r\n                            <StatusDropdown\r\n                                currentStatus={status}\r\n                                onStatusChange={handleStatusChange}\r\n                                quoteId={row.original._id}\r\n                            />\r\n                        </div>\r\n                    )\r\n                },\r\n                enableSorting: false\r\n            }),\r\n            columnHelper.accessor('action', {\r\n                header: 'Action',\r\n                cell: ({ row }) => (\r\n                    <div className='flex items-center gap-1'>\r\n                        {/* Delete */}\r\n                        <IconButton\r\n                            onClick={() => handleDeleteQuote(row.original._id)}\r\n                            title=\"Delete Quote\"\r\n                            size='small'\r\n                            sx={{\r\n                                color: 'text.secondary',\r\n                                '&:hover': {\r\n                                    color: 'error.main',\r\n                                    backgroundColor: 'error.light',\r\n                                    transform: 'scale(1.1)'\r\n                                },\r\n                                transition: 'all 0.2s ease-in-out'\r\n                            }}\r\n                        >\r\n                            <i className='tabler-trash' />\r\n                        </IconButton>\r\n\r\n                        {/* View Details */}\r\n                        <IconButton\r\n                            onClick={() => handleViewDetails(row.original)}\r\n                            title=\"View Details\"\r\n                            size='small'\r\n                            sx={{\r\n                                color: 'text.secondary',\r\n                                '&:hover': {\r\n                                    color: 'info.main',\r\n                                    backgroundColor: 'info.light',\r\n                                    transform: 'scale(1.1)'\r\n                                },\r\n                                transition: 'all 0.2s ease-in-out'\r\n                            }}\r\n                        >\r\n                            <i className='tabler-info-circle' />\r\n                        </IconButton>\r\n                    </div>\r\n                ),\r\n                enableSorting: false\r\n            })\r\n        ],\r\n        [data, filteredData]\r\n    )\r\n\r\n    // React Table\r\n    const table = useReactTable({\r\n        data: filteredData,\r\n        columns,\r\n        filterFns: {\r\n            fuzzy: fuzzyFilter\r\n        },\r\n        state: {\r\n            globalFilter\r\n        },\r\n        initialState: {\r\n            pagination: {\r\n                pageSize: 10\r\n            }\r\n        },\r\n        globalFilterFn: fuzzyFilter,\r\n        onGlobalFilterChange: setGlobalFilter,\r\n        getCoreRowModel: getCoreRowModel(),\r\n        getFilteredRowModel: getFilteredRowModel(),\r\n        getSortedRowModel: getSortedRowModel(),\r\n        getPaginationRowModel: getPaginationRowModel(),\r\n        getFacetedRowModel: getFacetedRowModel(),\r\n        getFacetedUniqueValues: getFacetedUniqueValues(),\r\n        getFacetedMinMaxValues: getFacetedMinMaxValues()\r\n    })\r\n\r\n    // Loading state\r\n    if (loading) {\r\n        return (\r\n            <Card>\r\n                <CardContent>\r\n                    <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 200 }}>\r\n                        <Typography>Loading quotes...</Typography>\r\n                    </Box>\r\n                </CardContent>\r\n            </Card>\r\n        )\r\n    }\r\n\r\n    // Error state\r\n    if (error) {\r\n        return (\r\n            <Card>\r\n                <CardContent>\r\n                    <Alert severity=\"error\" sx={{ mb: 4 }}>\r\n                        {error}\r\n                    </Alert>\r\n                    <Box sx={{ textAlign: 'center' }}>\r\n                        <Button\r\n                            variant=\"contained\"\r\n                            onClick={() => window.location.reload()}\r\n                            startIcon={<i className='tabler-refresh' />}\r\n                        >\r\n                            Retry\r\n                        </Button>\r\n                    </Box>\r\n                </CardContent>\r\n            </Card>\r\n        )\r\n    }\r\n\r\n    return (\r\n        <>\r\n            <Card>\r\n                <CardHeader\r\n                    title='Quotes Management'\r\n                    subheader='View and manage quote requests'\r\n                    className='pb-2'\r\n                />\r\n                <CardContent>\r\n                    <div className='flex justify-between gap-4 p-6 border-be'>\r\n                        <div className='flex items-center gap-2'>\r\n                            <Typography className='text-xl font-bold'>\r\n                                Quotes\r\n                            </Typography>\r\n                        </div>\r\n                        <div className='flex items-center gap-4'>\r\n                            <DebouncedInput\r\n                                value={globalFilter ?? ''}\r\n                                onChange={value => setGlobalFilter(String(value))}\r\n                                placeholder='Search quotes...'\r\n                                className='max-sm:is-full'\r\n                            />\r\n                            <IconButton\r\n                                variant='outlined'\r\n                                color='text.primary'\r\n                                onClick={() => window.location.reload()}\r\n                                className='max-sm:is-full'\r\n                            >\r\n                                <i className={`tabler-refresh ${loading ? 'animate-spin' : ''}`} />\r\n                            </IconButton>\r\n                        </div>\r\n                    </div>\r\n                    <div className='overflow-x-auto'>\r\n                        <table className={tableStyles.table}>\r\n                            <thead>\r\n                                {table.getHeaderGroups().map(headerGroup => (\r\n                                    <tr key={headerGroup.id}>\r\n                                        {headerGroup.headers.map(header => (\r\n                                            <th key={header.id}>\r\n                                                {header.isPlaceholder ? null : (\r\n                                                    <>\r\n                                                        <div\r\n                                                            className={classnames({\r\n                                                                'flex items-center': header.column.getIsSorted(),\r\n                                                                'cursor-pointer select-none': header.column.getCanSort()\r\n                                                            })}\r\n                                                            onClick={header.column.getToggleSortingHandler()}\r\n                                                        >\r\n                                                            {flexRender(header.column.columnDef.header, header.getContext())}\r\n                                                            {{\r\n                                                                asc: <i className='tabler-chevron-up text-xl' />,\r\n                                                                desc: <i className='tabler-chevron-down text-xl' />\r\n                                                            }[header.column.getIsSorted()] ?? null}\r\n                                                        </div>\r\n                                                    </>\r\n                                                )}\r\n                                            </th>\r\n                                        ))}\r\n                                    </tr>\r\n                                ))}\r\n                            </thead>\r\n                            {table.getFilteredRowModel().rows.length === 0 ? (\r\n                                <tbody>\r\n                                    <tr>\r\n                                        <td colSpan={table.getVisibleFlatColumns().length} className='text-center'>\r\n                                            No quotes available\r\n                                        </td>\r\n                                    </tr>\r\n                                </tbody>\r\n                            ) : (\r\n                                <tbody>\r\n                                    {table\r\n                                        .getRowModel()\r\n                                        .rows.slice(0, table.getState().pagination.pageSize)\r\n                                        .map(row => {\r\n                                            return (\r\n                                                <tr key={row.id} className={classnames({ selected: row.getIsSelected() })}>\r\n                                                    {row.getVisibleCells().map(cell => (\r\n                                                        <td key={cell.id}>{flexRender(cell.column.columnDef.cell, cell.getContext())}</td>\r\n                                                    ))}\r\n                                                </tr>\r\n                                            )\r\n                                        })}\r\n                                </tbody>\r\n                            )}\r\n                        </table>\r\n                    </div>\r\n                    <TablePaginationComponent table={table} />\r\n                </CardContent>\r\n            </Card>\r\n\r\n            {/* Quote Details Modal */}\r\n            {selectedQuote && (\r\n                <QuoteDetailsModal\r\n                    open={showModal}\r\n                    onClose={handleCloseModal}\r\n                    quoteData={selectedQuote}\r\n                />\r\n            )}\r\n        </>\r\n    )\r\n}\r\n\r\nexport default QuotesTable\r\n"], "names": [], "mappings": ";;;;AAEA,gBAAgB;AAChB;AAEA,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA,sBAAsB;AACtB;AACA;AACA;AAAA;AAaA,oBAAoB;AACpB;AACA;AACA;AACA;AACA;AAEA,cAAc;AACd;AAEA,gBAAgB;AAChB;AA3CA;;;;;;;;;;;;;;;;;;;;;;AA6CA,wBAAwB;AACxB,MAAM,cAAc,CAAC,KAAK,UAAU,OAAO;IACvC,MAAM,WAAW,CAAA,GAAA,gLAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,QAAQ,CAAC,WAAW;IAClD,QAAQ;QACJ;IACJ;IACA,OAAO,SAAS,MAAM;AAC1B;AAEA,2BAA2B;AAC3B,MAAM,iBAAiB,CAAC,EAAE,OAAO,YAAY,EAAE,QAAQ,EAAE,WAAW,GAAG,EAAE,GAAG,OAAO;IAC/E,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,SAAS;IACb,GAAG;QAAC;KAAa;IAEjB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,MAAM,UAAU,WAAW;YACvB,SAAS;QACb,GAAG;QAEH,OAAO,IAAM,aAAa;IAC9B,GAAG;QAAC;KAAM;IAEV,qBAAO,8OAAC,gJAAA,CAAA,UAAe;QAAE,GAAG,KAAK;QAAE,OAAO;QAAO,UAAU,CAAA,IAAK,SAAS,EAAE,MAAM,CAAC,KAAK;;;;;;AAC3F;AAEA,qBAAqB;AACrB,MAAM,eAAe,CAAA,GAAA,qKAAA,CAAA,qBAAkB,AAAD;AAEtC,MAAM,cAAc;IAChB,SAAS;IACT,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACnC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACnD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,mBAAmB;IACnB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,MAAM,aAAa;YACf,IAAI;gBACA,WAAW;gBACX,SAAS;gBACT,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,cAAW,AAAD;gBACjC,IAAI,SAAS,OAAO,EAAE;oBAClB,wCAAwC;oBACxC,MAAM,gBAAgB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,oBAAoB;oBAE1E,8EAA8E;oBAC9E,MAAM,mBAAmB,SAAS,MAAM,CAAC,GAAG,CAAC,CAAA,QAAS,CAAC;4BACnD,GAAG,KAAK;4BACR,QAAQ,aAAa,CAAC,MAAM,GAAG,CAAC,IAAI,MAAM,MAAM,IAAI;wBACxD,CAAC;oBACD,QAAQ;oBACR,gBAAgB;gBACpB,OAAO;oBACH,SAAS;gBACb;YACJ,EAAE,OAAO,KAAK;gBACV,QAAQ,KAAK,CAAC,yBAAyB;gBACvC,SAAS,IAAI,OAAO,IAAI;gBACxB,QAAQ,EAAE;gBACV,gBAAgB,EAAE;YACtB,SAAU;gBACN,WAAW;YACf;QACJ;QAEA;IACJ,GAAG,EAAE;IAEL,WAAW;IACX,MAAM,oBAAoB,CAAC;QACvB,iBAAiB;QACjB,aAAa;IACjB;IAEA,MAAM,mBAAmB;QACrB,aAAa;QACb,iBAAiB;IACrB;IAEA,MAAM,oBAAoB,OAAO;QAC7B,MAAM,QAAQ,KAAK,IAAI,CAAC,CAAA,IAAK,EAAE,GAAG,KAAK;QACvC,MAAM,cAAc,OAAO,aAAa,YAAY;QAEpD,IAAI,OAAO,OAAO,CAAC,CAAC,8CAA8C,EAAE,YAAY,+BAA+B,CAAC,GAAG;YAC/G,IAAI;gBACA,MAAM,CAAA,GAAA,2HAAA,CAAA,cAAW,AAAD,EAAE;gBAClB,MAAM,gBAAgB,KAAK,MAAM,CAAC,CAAA,QAAS,MAAM,GAAG,KAAK;gBACzD,QAAQ;gBACR,gBAAgB;gBAChB,MAAM;YACV,EAAE,OAAO,OAAO;gBACZ,QAAQ,KAAK,CAAC,yBAAyB;gBACvC,MAAM,CAAC,wBAAwB,EAAE,MAAM,OAAO,EAAE;YACpD;QACJ;IACJ;IAEA,uBAAuB;IACvB,MAAM,qBAAqB,OAAO,SAAS;QACvC,QAAQ,GAAG,CAAC,8BAA8B,SAAS,OAAO;QAE1D,IAAI;YACA,uCAAuC;YACvC,MAAM,CAAA,GAAA,2HAAA,CAAA,oBAAiB,AAAD,EAAE,SAAS;YACjC,QAAQ,GAAG,CAAC;YAEZ,0DAA0D;YAC1D,MAAM,qBAAqB,CAAC,SACxB,OAAO,GAAG,CAAC,CAAA,QACP,MAAM,GAAG,KAAK,UACR;wBAAE,GAAG,KAAK;wBAAE,QAAQ;oBAAU,IAC9B;YAGd,QAAQ;YACR,gBAAgB;QACpB,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,kCAAkC;YAChD,MAAM;QACV;QAEA,QAAQ,GAAG,CAAC;IAChB;IAEA,uBAAuB;IACvB,MAAM,qBAAqB;QACvB,OAAO;QACP,WAAW;YACP,iBAAiB;YACjB,WAAW;QACf;QACA,YAAY;QACZ,UAAU;QACV,OAAO;QACP,QAAQ;QACR,SAAS;IACb;IAEA,qBAAqB;IACrB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAClB,IAAM;YACF,aAAa,QAAQ,CAAC,wBAAwB;gBAC1C,QAAQ;gBACR,MAAM,CAAC,EAAE,GAAG,EAAE,iBACV,8OAAC;wBAAI,WAAU;;0CACX,8OAAC,6IAAA,CAAA,UAAY;gCACT,SAAQ;gCACR,OAAM;gCACN,MAAK;gCACL,MAAM;0CAEL,IAAI,QAAQ,CAAC,WAAW,EAAE,UAAU,OAAO,IAAI,iBAAiB;;;;;;0CAErE,8OAAC;gCAAI,WAAU;;kDACX,8OAAC,6JAAA,CAAA,UAAU;wCAAC,OAAM;wCAAe,WAAU;wCAAc,OAAO;4CAAE,UAAU;wCAAS;kDAChF,IAAI,QAAQ,CAAC,WAAW,EAAE,YAAY;;;;;;kDAE3C,8OAAC,6JAAA,CAAA,UAAU;wCAAC,SAAQ;wCAAQ,OAAM;wCAAe,WAAU;wCAAc,OAAO;4CAAE,UAAU;4CAAQ,eAAe;wCAAM;kDACpH,IAAI,QAAQ,CAAC,WAAW,EAAE,gBAAgB;;;;;;;;;;;;;;;;;;YAK/D;YACA,aAAa,QAAQ,CAAC,2BAA2B;gBAC7C,QAAQ;gBACR,MAAM,CAAC,EAAE,GAAG,EAAE,iBACV,8OAAC,6JAAA,CAAA,UAAU;wBAAC,OAAM;wBAAe,WAAU;wBAAc,OAAO;4BAAE,UAAU;wBAAS;kCAChF,IAAI,QAAQ,CAAC,WAAW,EAAE,eAAe;;;;;;YAGtD;YACA,aAAa,QAAQ,CAAC,4BAA4B;gBAC9C,QAAQ;gBACR,MAAM,CAAC,EAAE,GAAG,EAAE,iBACV,8OAAC,6JAAA,CAAA,UAAU;wBAAC,OAAM;wBAAe,WAAU;wBAAc,OAAO;4BAAE,UAAU;wBAAS;kCAChF,IAAI,QAAQ,CAAC,eAAe,EAAE,YAAY;;;;;;YAGvD;YACA,aAAa,QAAQ,CAAC,kBAAkB;gBACpC,QAAQ;gBACR,MAAM,CAAC,EAAE,GAAG,EAAE;oBACV,MAAM,SAAS,IAAI,QAAQ,CAAC,cAAc,EAAE;oBAC5C,qBACI,8OAAC,6JAAA,CAAA,UAAU;wBAAC,OAAM;wBAAe,WAAU;wBAAc,OAAO;4BAAE,UAAU;wBAAS;kCAChF,SAAS,GAAG,OAAO,IAAI,CAAC,EAAE,EAAE,OAAO,eAAe,EAAE,GAAG;;;;;;gBAGpE;YACJ;YACA,aAAa,QAAQ,CAAC,oBAAoB;gBACtC,QAAQ;gBACR,MAAM,CAAC,EAAE,GAAG,EAAE;oBACV,MAAM,WAAW,IAAI,QAAQ,CAAC,cAAc,EAAE;oBAC9C,qBACI,8OAAC,6JAAA,CAAA,UAAU;wBAAC,OAAM;wBAAe,WAAU;wBAAc,OAAO;4BAAE,UAAU;wBAAS;kCAChF,WAAW,GAAG,SAAS,IAAI,CAAC,EAAE,EAAE,SAAS,eAAe,EAAE,GAAG;;;;;;gBAG1E;YACJ;YACA,aAAa,QAAQ,CAAC,aAAa;gBAC/B,QAAQ;gBACR,MAAM,CAAC,EAAE,GAAG,EAAE;oBACV,MAAM,iBAAiB,CAAC;wBACpB,IAAI,CAAC,YAAY,OAAO;wBAExB,IAAI;4BACA,qDAAqD;4BACrD,MAAM,OAAO,IAAI,KAAK;4BACtB,IAAI,MAAM,KAAK,OAAO,KAAK,OAAO,WAAW,kCAAkC;;4BAE/E,8EAA8E;4BAC9E,MAAM,cAAc;gCAChB,OAAO;gCACP,KAAK;gCACL,MAAM;4BACV;4BACA,MAAM,cAAc;gCAChB,MAAM;gCACN,QAAQ;gCACR,QAAQ;4BACZ;4BAEA,MAAM,gBAAgB,KAAK,kBAAkB,CAAC,SAAS;4BACvD,MAAM,gBAAgB,KAAK,kBAAkB,CAAC,SAAS;4BAEvD,OAAO,GAAG,cAAc,CAAC,EAAE,eAAe;wBAC9C,EAAE,OAAO,OAAO;4BACZ,OAAO,WAAW,sCAAsC;;wBAC5D;oBACJ;oBAEA,MAAM,OAAO,IAAI,QAAQ,CAAC,SAAS,IAAI,IAAI,QAAQ,CAAC,cAAc,EAAE;oBACpE,qBACI,8OAAC;wBAAI,WAAU;kCACX,cAAA,8OAAC,6JAAA,CAAA,UAAU;4BAAC,OAAM;4BAAe,OAAO;gCAAE,UAAU;gCAAW,YAAY;4BAAM;sCAC5E,eAAe;;;;;;;;;;;gBAIhC;YACJ;YACA,aAAa,QAAQ,CAAC,UAAU;gBAC5B,QAAQ;gBACR,MAAM,CAAC,EAAE,GAAG,EAAE;oBACV,MAAM,SAAS,IAAI,QAAQ,CAAC,MAAM,IAAI;oBAEtC,qBACI,8OAAC;wBAAI,WAAU;kCACX,cAAA,8OAAC,iJAAA,CAAA,UAAc;4BACX,eAAe;4BACf,gBAAgB;4BAChB,SAAS,IAAI,QAAQ,CAAC,GAAG;;;;;;;;;;;gBAIzC;gBACA,eAAe;YACnB;YACA,aAAa,QAAQ,CAAC,UAAU;gBAC5B,QAAQ;gBACR,MAAM,CAAC,EAAE,GAAG,EAAE,iBACV,8OAAC;wBAAI,WAAU;;0CAEX,8OAAC,6JAAA,CAAA,UAAU;gCACP,SAAS,IAAM,kBAAkB,IAAI,QAAQ,CAAC,GAAG;gCACjD,OAAM;gCACN,MAAK;gCACL,IAAI;oCACA,OAAO;oCACP,WAAW;wCACP,OAAO;wCACP,iBAAiB;wCACjB,WAAW;oCACf;oCACA,YAAY;gCAChB;0CAEA,cAAA,8OAAC;oCAAE,WAAU;;;;;;;;;;;0CAIjB,8OAAC,6JAAA,CAAA,UAAU;gCACP,SAAS,IAAM,kBAAkB,IAAI,QAAQ;gCAC7C,OAAM;gCACN,MAAK;gCACL,IAAI;oCACA,OAAO;oCACP,WAAW;wCACP,OAAO;wCACP,iBAAiB;wCACjB,WAAW;oCACf;oCACA,YAAY;gCAChB;0CAEA,cAAA,8OAAC;oCAAE,WAAU;;;;;;;;;;;;;;;;;gBAIzB,eAAe;YACnB;SACH,EACD;QAAC;QAAM;KAAa;IAGxB,cAAc;IACd,MAAM,QAAQ,CAAA,GAAA,sLAAA,CAAA,gBAAa,AAAD,EAAE;QACxB,MAAM;QACN;QACA,WAAW;YACP,OAAO;QACX;QACA,OAAO;YACH;QACJ;QACA,cAAc;YACV,YAAY;gBACR,UAAU;YACd;QACJ;QACA,gBAAgB;QAChB,sBAAsB;QACtB,iBAAiB,CAAA,GAAA,qKAAA,CAAA,kBAAe,AAAD;QAC/B,qBAAqB,CAAA,GAAA,qKAAA,CAAA,sBAAmB,AAAD;QACvC,mBAAmB,CAAA,GAAA,qKAAA,CAAA,oBAAiB,AAAD;QACnC,uBAAuB,CAAA,GAAA,qKAAA,CAAA,wBAAqB,AAAD;QAC3C,oBAAoB,CAAA,GAAA,qKAAA,CAAA,qBAAkB,AAAD;QACrC,wBAAwB,CAAA,GAAA,qKAAA,CAAA,yBAAsB,AAAD;QAC7C,wBAAwB,CAAA,GAAA,qKAAA,CAAA,yBAAsB,AAAD;IACjD;IAEA,gBAAgB;IAChB,IAAI,SAAS;QACT,qBACI,8OAAC,iJAAA,CAAA,UAAI;sBACD,cAAA,8OAAC,+JAAA,CAAA,UAAW;0BACR,cAAA,8OAAC,+IAAA,CAAA,UAAG;oBAAC,IAAI;wBAAE,SAAS;wBAAQ,gBAAgB;wBAAU,YAAY;wBAAU,WAAW;oBAAI;8BACvF,cAAA,8OAAC,6JAAA,CAAA,UAAU;kCAAC;;;;;;;;;;;;;;;;;;;;;IAKhC;IAEA,cAAc;IACd,IAAI,OAAO;QACP,qBACI,8OAAC,iJAAA,CAAA,UAAI;sBACD,cAAA,8OAAC,+JAAA,CAAA,UAAW;;kCACR,8OAAC,mJAAA,CAAA,UAAK;wBAAC,UAAS;wBAAQ,IAAI;4BAAE,IAAI;wBAAE;kCAC/B;;;;;;kCAEL,8OAAC,+IAAA,CAAA,UAAG;wBAAC,IAAI;4BAAE,WAAW;wBAAS;kCAC3B,cAAA,8OAAC,qJAAA,CAAA,UAAM;4BACH,SAAQ;4BACR,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;4BACrC,yBAAW,8OAAC;gCAAE,WAAU;;;;;;sCAC3B;;;;;;;;;;;;;;;;;;;;;;IAOrB;IAEA,qBACI;;0BACI,8OAAC,iJAAA,CAAA,UAAI;;kCACD,8OAAC,6JAAA,CAAA,UAAU;wBACP,OAAM;wBACN,WAAU;wBACV,WAAU;;;;;;kCAEd,8OAAC,+JAAA,CAAA,UAAW;;0CACR,8OAAC;gCAAI,WAAU;;kDACX,8OAAC;wCAAI,WAAU;kDACX,cAAA,8OAAC,6JAAA,CAAA,UAAU;4CAAC,WAAU;sDAAoB;;;;;;;;;;;kDAI9C,8OAAC;wCAAI,WAAU;;0DACX,8OAAC;gDACG,OAAO,gBAAgB;gDACvB,UAAU,CAAA,QAAS,gBAAgB,OAAO;gDAC1C,aAAY;gDACZ,WAAU;;;;;;0DAEd,8OAAC,6JAAA,CAAA,UAAU;gDACP,SAAQ;gDACR,OAAM;gDACN,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;gDACrC,WAAU;0DAEV,cAAA,8OAAC;oDAAE,WAAW,CAAC,eAAe,EAAE,UAAU,iBAAiB,IAAI;;;;;;;;;;;;;;;;;;;;;;;0CAI3E,8OAAC;gCAAI,WAAU;0CACX,cAAA,8OAAC;oCAAM,WAAW,4IAAA,CAAA,UAAW,CAAC,KAAK;;sDAC/B,8OAAC;sDACI,MAAM,eAAe,GAAG,GAAG,CAAC,CAAA,4BACzB,8OAAC;8DACI,YAAY,OAAO,CAAC,GAAG,CAAC,CAAA,uBACrB,8OAAC;sEACI,OAAO,aAAa,GAAG,qBACpB;0EACI,cAAA,8OAAC;oEACG,WAAW,CAAA,GAAA,mIAAA,CAAA,UAAU,AAAD,EAAE;wEAClB,qBAAqB,OAAO,MAAM,CAAC,WAAW;wEAC9C,8BAA8B,OAAO,MAAM,CAAC,UAAU;oEAC1D;oEACA,SAAS,OAAO,MAAM,CAAC,uBAAuB;;wEAE7C,CAAA,GAAA,sLAAA,CAAA,aAAU,AAAD,EAAE,OAAO,MAAM,CAAC,SAAS,CAAC,MAAM,EAAE,OAAO,UAAU;wEAC5D;4EACG,mBAAK,8OAAC;gFAAE,WAAU;;;;;;4EAClB,oBAAM,8OAAC;gFAAE,WAAU;;;;;;wEACvB,CAAC,CAAC,OAAO,MAAM,CAAC,WAAW,GAAG,IAAI;;;;;;;;2DAdzC,OAAO,EAAE;;;;;mDAFjB,YAAY,EAAE;;;;;;;;;;wCAyB9B,MAAM,mBAAmB,GAAG,IAAI,CAAC,MAAM,KAAK,kBACzC,8OAAC;sDACG,cAAA,8OAAC;0DACG,cAAA,8OAAC;oDAAG,SAAS,MAAM,qBAAqB,GAAG,MAAM;oDAAE,WAAU;8DAAc;;;;;;;;;;;;;;;iEAMnF,8OAAC;sDACI,MACI,WAAW,GACX,IAAI,CAAC,KAAK,CAAC,GAAG,MAAM,QAAQ,GAAG,UAAU,CAAC,QAAQ,EAClD,GAAG,CAAC,CAAA;gDACD,qBACI,8OAAC;oDAAgB,WAAW,CAAA,GAAA,mIAAA,CAAA,UAAU,AAAD,EAAE;wDAAE,UAAU,IAAI,aAAa;oDAAG;8DAClE,IAAI,eAAe,GAAG,GAAG,CAAC,CAAA,qBACvB,8OAAC;sEAAkB,CAAA,GAAA,sLAAA,CAAA,aAAU,AAAD,EAAE,KAAK,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,KAAK,UAAU;2DAAhE,KAAK,EAAE;;;;;mDAFf,IAAI,EAAE;;;;;4CAMvB;;;;;;;;;;;;;;;;;0CAKpB,8OAAC,8IAAA,CAAA,UAAwB;gCAAC,OAAO;;;;;;;;;;;;;;;;;;YAKxC,+BACG,8OAAC,oJAAA,CAAA,UAAiB;gBACd,MAAM;gBACN,SAAS;gBACT,WAAW;;;;;;;;AAK/B;uCAEe", "debugId": null}}, {"offset": {"line": 2515, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/app/%5Blang%5D/%28dashboard%29/%28private%29/apps/quotes/page.jsx"], "sourcesContent": ["'use client';\r\n\r\n// Component Imports\r\nimport QuotesTable from '@/views/apps/quotes/QuotesTable';\r\n\r\nconst QuotesPage = () => {\r\n  return (\r\n    <div className='w-full min-h-screen bg-gray-50 p-2 sm:p-4 lg:p-6'>\r\n      <div className='max-w-full mx-auto'>\r\n        <QuotesTable />\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default QuotesPage;\r\n"], "names": [], "mappings": ";;;;AAEA,oBAAoB;AACpB;AAHA;;;AAKA,MAAM,aAAa;IACjB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,8IAAA,CAAA,UAAW;;;;;;;;;;;;;;;AAIpB;uCAEe", "debugId": null}}]}