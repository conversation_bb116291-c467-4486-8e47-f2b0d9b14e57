const mongoose = require('mongoose');

const UrgentInquirySchema = new mongoose.Schema({

    full_name: {
        type: String,
        required: true
    },

    email: {
        type: String,
        required: true
    },

    phone_number: {
        type: String,
        required: true
    },

    urgency_type: {
        type: String,
        enum: ['Shipment Delay', 'Vehicle Breakdown', 'Delivery Issue', 'Lost/Damaged Cargo', 'Delivery Refusal', 'Other(Please Specify)'],
        required: true
    },

    other_urgency: {
        type: String,
        required: function () {
            return this.reference === 'Other(Please specify)';
        }
    },

    ref_number: {
        type: String,
    },

    documents: {
        type: String
    },

    brief_description: {
        type: String,
        required: true
    },
    _honeypot: {
        type: String,
        default: '',
        select: false // Don't expose in queries
    },
    ip: {
        type: String,
        required: false
    },
    status: {
        type: String,
        enum: ['pending', 'in-view', 'completed'],
        default: 'pending'
    }
}, {
    timestamps: true,
})


const Urgent = mongoose.model('UrgentInquiry', UrgentInquirySchema);
module.exports = Urgent;