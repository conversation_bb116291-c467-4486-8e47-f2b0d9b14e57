{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_af0622be._.js", "server/edge/chunks/[root-of-the-server]__5e96eddc._.js", "server/edge/chunks/edge-wrapper_a0335704.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "3KGK2IZEJivyYixf6KYUDaQdI235ZnG7L25SngUYmG8=", "__NEXT_PREVIEW_MODE_ID": "32058d23743202a3d2907796c28c6204", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "a0ce402a2238d0776f18ee395838a481be0d13bfa59bc61ff404ae855c43093f", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "3c4d0de7465b31efe77a5f0dc82f2585a8ff3dfb2f7a36561a73af05033319d5"}}}, "sortedMiddleware": ["/"], "functions": {}}