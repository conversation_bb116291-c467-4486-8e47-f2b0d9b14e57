const mongoose = require('mongoose');

// Connect to MongoDB
mongoose.connect('mongodb://localhost:27017/CAM_TRANSPORT_SYSTEM');

// Define the Login schema (simplified)
const LoginSchema = new mongoose.Schema({
    username: String,
    email: String,
    password: String,
    mfaSecret: String,
    mfaEnabled: Boolean,
    mfaDevices: Array,
    isVerified: <PERSON><PERSON><PERSON>,
    role: String,
    adminId: String
}, { timestamps: true });

const Login = mongoose.model('Login', LoginSchema);

async function listAllUsers() {
    console.log('📋 LISTING ALL USERS IN DATABASE');
    console.log('================================\n');
    
    try {
        // Find all users
        const users = await Login.find({});
        
        if (users.length === 0) {
            console.log('❌ No users found in database');
            return;
        }
        
        console.log(`✅ Found ${users.length} users:\n`);
        
        users.forEach((user, index) => {
            console.log(`${index + 1}. User ID: ${user._id}`);
            console.log(`   Username: ${user.username || 'N/A'}`);
            console.log(`   Email: ${user.email || 'N/A'}`);
            console.log(`   Admin ID: ${user.adminId || 'N/A'}`);
            console.log(`   Role: ${user.role || 'N/A'}`);
            console.log(`   MFA Enabled: ${user.mfaEnabled || false}`);
            console.log(`   MFA Secret: ${user.mfaSecret ? 'Present' : 'None'}`);
            console.log(`   Is Verified: ${user.isVerified || false}`);
            console.log(`   Created: ${user.createdAt || 'N/A'}`);
            console.log('   ---');
        });
        
        // Look for users that might match dharsoda
        console.log('\n🔍 Looking for users with "dharsoda" in email or username:');
        const dhruvUsers = users.filter(user => 
            (user.email && user.email.toLowerCase().includes('dharsoda')) ||
            (user.username && user.username.toLowerCase().includes('dharsoda')) ||
            (user.username && user.username.toLowerCase().includes('dhruv'))
        );
        
        if (dhruvUsers.length > 0) {
            console.log(`✅ Found ${dhruvUsers.length} matching users:`);
            dhruvUsers.forEach(user => {
                console.log(`   - ${user.username} (${user.email}) - MFA: ${user.mfaEnabled}`);
            });
        } else {
            console.log('❌ No users found with "dharsoda" or "dhruv" in email/username');
        }
        
    } catch (error) {
        console.error('❌ Error:', error);
    } finally {
        mongoose.connection.close();
    }
}

listAllUsers();
