const speakeasy = require('speakeasy');

console.log('🔐 MFA DEBUG TOOL');
console.log('================\n');

// Common test secrets that might be used
const testSecrets = [
    'JBSWY3DPEHPK3PXP', // Common test secret
    'GEZDGNBVGY3TQOJQGEZDGNBVGY3TQOJQ', // Another common test
    'MFRGG2LTMFZGK3DBON2HE2LOM4QGG2LTMFZGK3DBON2HE2LOM4QGG2LTMFZGK3DBON2HE2LOM4Q' // Longer test
];

console.log('⏰ Current Time:', new Date().toLocaleString());
console.log('🕐 Unix Timestamp:', Math.floor(Date.now() / 1000));
console.log('');

testSecrets.forEach((secret, index) => {
    console.log(`🔑 Test Secret ${index + 1}: ${secret.substring(0, 8)}...`);
    
    try {
        // Generate current token
        const currentToken = speakeasy.totp({
            secret: secret,
            encoding: 'base32'
        });
        
        console.log(`   Current Token: ${currentToken}`);
        
        // Test verification
        const verified = speakeasy.totp.verify({
            secret: secret,
            encoding: 'base32',
            token: currentToken,
            window: 1
        });
        
        console.log(`   Self-verification: ${verified ? '✅ PASS' : '❌ FAIL'}`);
        
        // Show next few tokens for timing
        console.log('   Next tokens:');
        for (let i = 1; i <= 2; i++) {
            const futureTime = Math.floor(Date.now() / 1000) + (i * 30);
            const futureToken = speakeasy.totp({
                secret: secret,
                encoding: 'base32',
                time: futureTime
            });
            const timeStr = new Date(futureTime * 1000).toLocaleTimeString();
            console.log(`     ${timeStr}: ${futureToken}`);
        }
        
    } catch (error) {
        console.log(`   ❌ Error: ${error.message}`);
    }
    
    console.log('');
});

console.log('📱 INSTRUCTIONS:');
console.log('1. Check which token matches what your authenticator app shows');
console.log('2. If none match, your app might be using a different secret');
console.log('3. Try the tokens shown above in your login');
console.log('4. Check if your phone time is synchronized');
console.log('');

// Show time windows for debugging timing issues
console.log('⏰ TIME WINDOW DEBUG:');
const currentTime = Math.floor(Date.now() / 1000);
const window = 2;

for (let i = -window; i <= window; i++) {
    const time = currentTime + (i * 30);
    const timeStr = new Date(time * 1000).toLocaleTimeString();
    const status = i === 0 ? ' ← CURRENT' : '';
    console.log(`Window ${i}: ${timeStr}${status}`);
}
