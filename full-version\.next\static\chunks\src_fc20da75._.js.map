{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/%40core/components/mui/TextField.jsx"], "sourcesContent": ["'use client'\r\n\r\n// React Imports\r\nimport { forwardRef } from 'react'\r\n\r\n// MUI Imports\r\nimport { styled } from '@mui/material/styles'\r\nimport TextField from '@mui/material/TextField'\r\n\r\nconst TextFieldStyled = styled(TextField)(({ theme }) => ({\r\n  '& .MuiInputLabel-root': {\r\n    transform: 'none',\r\n    width: 'fit-content',\r\n    maxWidth: '100%',\r\n    lineHeight: 1.153,\r\n    position: 'relative',\r\n    fontSize: theme.typography.body2.fontSize,\r\n    marginBottom: theme.spacing(1),\r\n    color: 'var(--mui-palette-text-primary)',\r\n    '&:not(.Mui-error).MuiFormLabel-colorPrimary.Mui-focused': {\r\n      color: 'var(--mui-palette-primary-main) !important'\r\n    },\r\n    '&.Mui-disabled': {\r\n      color: 'var(--mui-palette-text-disabled)'\r\n    },\r\n    '&.Mui-error': {\r\n      color: 'var(--mui-palette-error-main)'\r\n    }\r\n  },\r\n  '& .MuiInputBase-root': {\r\n    backgroundColor: 'transparent !important',\r\n    border: `1px solid var(--mui-palette-customColors-inputBorder)`,\r\n    '&:not(.Mui-focused):not(.Mui-disabled):not(.Mui-error):hover': {\r\n      borderColor: 'var(--mui-palette-action-active)'\r\n    },\r\n    '&:before, &:after': {\r\n      display: 'none'\r\n    },\r\n    '&.MuiInputBase-sizeSmall': {\r\n      borderRadius: 'var(--mui-shape-borderRadius)'\r\n    },\r\n    '&.Mui-error': {\r\n      borderColor: 'var(--mui-palette-error-main)'\r\n    },\r\n    '&.Mui-focused': {\r\n      borderWidth: 2,\r\n      '& .MuiInputBase-input:not(.MuiInputBase-readOnly):not([readonly])::placeholder': {\r\n        transform: 'translateX(4px)'\r\n      },\r\n      '& :not(textarea).MuiFilledInput-input': {\r\n        padding: '6.25px 13px'\r\n      },\r\n      '&:not(.Mui-error).MuiInputBase-colorPrimary': {\r\n        borderColor: 'var(--mui-palette-primary-main)',\r\n        boxShadow: 'var(--mui-customShadows-primary-sm)'\r\n      },\r\n      '&.MuiInputBase-colorSecondary': {\r\n        borderColor: 'var(--mui-palette-secondary-main)'\r\n      },\r\n      '&.MuiInputBase-colorInfo': {\r\n        borderColor: 'var(--mui-palette-info-main)'\r\n      },\r\n      '&.MuiInputBase-colorSuccess': {\r\n        borderColor: 'var(--mui-palette-success-main)'\r\n      },\r\n      '&.MuiInputBase-colorWarning': {\r\n        borderColor: 'var(--mui-palette-warning-main)'\r\n      },\r\n      '&.MuiInputBase-colorError': {\r\n        borderColor: 'var(--mui-palette-error-main)'\r\n      },\r\n      '&.Mui-error': {\r\n        borderColor: 'var(--mui-palette-error-main)'\r\n      }\r\n    },\r\n    '&.Mui-disabled': {\r\n      backgroundColor: 'var(--mui-palette-action-hover) !important'\r\n    }\r\n  },\r\n\r\n  // Adornments\r\n  '& .MuiInputAdornment-root': {\r\n    marginBlockStart: '0px !important',\r\n    '&.MuiInputAdornment-positionStart + .MuiInputBase-input:not(textarea)': {\r\n      paddingInlineStart: '0px !important'\r\n    }\r\n  },\r\n  '& .MuiInputBase-inputAdornedEnd.MuiInputBase-input': {\r\n    paddingInlineEnd: '0px !important'\r\n  },\r\n  '& .MuiInputBase-sizeSmall.MuiInputBase-adornedStart.Mui-focused': {\r\n    paddingInlineStart: '13px',\r\n    '& .MuiInputBase-input': {\r\n      paddingInlineStart: '0px !important'\r\n    }\r\n  },\r\n  '& .MuiInputBase-sizeSmall.MuiInputBase-adornedStart:not(.MuiAutocomplete-inputRoot)': {\r\n    paddingInlineStart: '14px'\r\n  },\r\n  '& .MuiInputBase-sizeSmall.MuiInputBase-adornedEnd:not(.MuiAutocomplete-inputRoot)': {\r\n    paddingInlineEnd: '14px'\r\n  },\r\n  '& .MuiInputBase-sizeSmall.MuiInputBase-adornedEnd.Mui-focused:not(.MuiAutocomplete-inputRoot)': {\r\n    paddingInlineEnd: '13px',\r\n    '& .MuiInputBase-input': {\r\n      paddingInlineEnd: '0px !important'\r\n    }\r\n  },\r\n  '& :not(.MuiInputBase-sizeSmall).MuiInputBase-adornedStart.Mui-focused': {\r\n    paddingInlineStart: '15px',\r\n    '& .MuiInputBase-input': {\r\n      paddingInlineStart: '0px !important'\r\n    }\r\n  },\r\n  '& :not(.MuiInputBase-sizeSmall).MuiInputBase-adornedStart': {\r\n    paddingInlineStart: '16px'\r\n  },\r\n  '& :not(.MuiInputBase-sizeSmall).MuiInputBase-adornedEnd.Mui-focused': {\r\n    paddingInlineEnd: '15px',\r\n    '& .MuiInputBase-input': {\r\n      paddingInlineEnd: '0px !important'\r\n    }\r\n  },\r\n  '& :not(.MuiInputBase-sizeSmall).MuiInputBase-adornedEnd': {\r\n    paddingInlineEnd: '16px'\r\n  },\r\n  '& .MuiInputAdornment-sizeMedium': {\r\n    'i, svg': {\r\n      fontSize: '1.25rem'\r\n    }\r\n  },\r\n  '& .MuiInputBase-input': {\r\n    '&:not(textarea).MuiInputBase-inputSizeSmall': {\r\n      padding: '7.25px 14px'\r\n    },\r\n    '&:not(.MuiInputBase-readOnly):not([readonly])::placeholder': {\r\n      transition: theme.transitions.create(['opacity', 'transform'], {\r\n        duration: theme.transitions.duration.shorter\r\n      })\r\n    }\r\n  },\r\n  '& :not(.MuiInputBase-sizeSmall).MuiInputBase-root': {\r\n    borderRadius: '8px',\r\n    fontSize: '17px',\r\n    lineHeight: '1.41',\r\n    '& .MuiInputBase-input': {\r\n      padding: '10.8px 16px'\r\n    },\r\n    '&.Mui-focused': {\r\n      '& .MuiInputBase-input': {\r\n        padding: '9.8px 15px'\r\n      }\r\n    }\r\n  },\r\n  '& .MuiFormHelperText-root': {\r\n    lineHeight: 1.154,\r\n    margin: theme.spacing(1, 0, 0),\r\n    fontSize: theme.typography.body2.fontSize,\r\n    '&.Mui-error': {\r\n      color: 'var(--mui-palette-error-main)'\r\n    },\r\n    '&.Mui-disabled': {\r\n      color: 'var(--mui-palette-text-disabled)'\r\n    }\r\n  },\r\n\r\n  // For Select\r\n  '& .MuiSelect-select.MuiInputBase-inputSizeSmall, & .MuiNativeSelect-select.MuiInputBase-inputSizeSmall': {\r\n    '& ~ i, & ~ svg': {\r\n      inlineSize: '1.125rem',\r\n      blockSize: '1.125rem'\r\n    }\r\n  },\r\n  '& .MuiSelect-select': {\r\n    // lineHeight: 1.5,\r\n    minHeight: 'unset !important',\r\n    lineHeight: '1.4375em',\r\n    '&.MuiInputBase-input': {\r\n      paddingInlineEnd: '32px !important'\r\n    }\r\n  },\r\n  '& .Mui-focused .MuiSelect-select': {\r\n    '& ~ i, & ~ svg': {\r\n      right: '0.9375rem'\r\n    }\r\n  },\r\n  '& .MuiSelect-select:focus, & .MuiNativeSelect-select:focus': {\r\n    backgroundColor: 'transparent'\r\n  },\r\n\r\n  // For Autocomplete\r\n  '& :not(.MuiInputBase-sizeSmall).MuiAutocomplete-inputRoot': {\r\n    paddingBlock: '5.55px',\r\n    '& .MuiAutocomplete-input': {\r\n      paddingInline: '8px !important',\r\n      paddingBlock: '5.25px !important'\r\n    },\r\n    '&.Mui-focused .MuiAutocomplete-input': {\r\n      paddingInlineStart: '7px !important'\r\n    },\r\n    '&.Mui-focused': {\r\n      paddingBlock: '4.55px !important'\r\n    },\r\n    '& .MuiAutocomplete-endAdornment': {\r\n      top: 'calc(50% - 12px)'\r\n    }\r\n  },\r\n  '& .MuiAutocomplete-inputRoot.MuiInputBase-sizeSmall': {\r\n    paddingBlock: '4.75px !important',\r\n    paddingInlineStart: '10px',\r\n    '&.Mui-focused': {\r\n      paddingBlock: '3.75px !important',\r\n      paddingInlineStart: '9px',\r\n      '.MuiAutocomplete-input': {\r\n        paddingBlock: '2.5px',\r\n        paddingInline: '3px !important'\r\n      }\r\n    },\r\n    '& .MuiAutocomplete-input': {\r\n      paddingInline: '3px !important'\r\n    }\r\n  },\r\n  '& .MuiAutocomplete-inputRoot': {\r\n    display: 'flex',\r\n    gap: '0.25rem',\r\n    '& .MuiAutocomplete-tag': {\r\n      margin: 0\r\n    }\r\n  },\r\n  '& .MuiAutocomplete-inputRoot.Mui-focused .MuiAutocomplete-endAdornment': {\r\n    right: '.9375rem'\r\n  },\r\n\r\n  // For Textarea\r\n  '& .MuiInputBase-multiline': {\r\n    '&.MuiInputBase-sizeSmall': {\r\n      padding: '6px 14px',\r\n      '&.Mui-focused': {\r\n        padding: '5px 13px'\r\n      }\r\n    },\r\n    '& textarea.MuiInputBase-inputSizeSmall:placeholder-shown': {\r\n      overflowX: 'hidden'\r\n    }\r\n  }\r\n}))\r\n\r\nconst CustomTextField = forwardRef((props, ref) => {\r\n  const { size = 'small', slotProps, ...rest } = props\r\n\r\n  return (\r\n    <TextFieldStyled\r\n      size={size}\r\n      inputRef={ref}\r\n      {...rest}\r\n      variant='filled'\r\n      slotProps={{\r\n        ...slotProps,\r\n        inputLabel: { ...slotProps?.inputLabel, shrink: true }\r\n      }}\r\n    />\r\n  )\r\n})\r\n\r\nexport default CustomTextField\r\n"], "names": [], "mappings": ";;;;AAEA,gBAAgB;AAChB;AAEA,cAAc;AACd;AACA;AAPA;;;;;AASA,MAAM,kBAAkB,CAAA,GAAA,6MAAA,CAAA,SAAM,AAAD,EAAE,8JAAA,CAAA,UAAS,EAAE,CAAC,EAAE,KAAK,EAAE,GAAK,CAAC;QACxD,yBAAyB;YACvB,WAAW;YACX,OAAO;YACP,UAAU;YACV,YAAY;YACZ,UAAU;YACV,UAAU,MAAM,UAAU,CAAC,KAAK,CAAC,QAAQ;YACzC,cAAc,MAAM,OAAO,CAAC;YAC5B,OAAO;YACP,2DAA2D;gBACzD,OAAO;YACT;YACA,kBAAkB;gBAChB,OAAO;YACT;YACA,eAAe;gBACb,OAAO;YACT;QACF;QACA,wBAAwB;YACtB,iBAAiB;YACjB,QAAQ,CAAC,qDAAqD,CAAC;YAC/D,gEAAgE;gBAC9D,aAAa;YACf;YACA,qBAAqB;gBACnB,SAAS;YACX;YACA,4BAA4B;gBAC1B,cAAc;YAChB;YACA,eAAe;gBACb,aAAa;YACf;YACA,iBAAiB;gBACf,aAAa;gBACb,kFAAkF;oBAChF,WAAW;gBACb;gBACA,yCAAyC;oBACvC,SAAS;gBACX;gBACA,+CAA+C;oBAC7C,aAAa;oBACb,WAAW;gBACb;gBACA,iCAAiC;oBAC/B,aAAa;gBACf;gBACA,4BAA4B;oBAC1B,aAAa;gBACf;gBACA,+BAA+B;oBAC7B,aAAa;gBACf;gBACA,+BAA+B;oBAC7B,aAAa;gBACf;gBACA,6BAA6B;oBAC3B,aAAa;gBACf;gBACA,eAAe;oBACb,aAAa;gBACf;YACF;YACA,kBAAkB;gBAChB,iBAAiB;YACnB;QACF;QAEA,aAAa;QACb,6BAA6B;YAC3B,kBAAkB;YAClB,yEAAyE;gBACvE,oBAAoB;YACtB;QACF;QACA,sDAAsD;YACpD,kBAAkB;QACpB;QACA,mEAAmE;YACjE,oBAAoB;YACpB,yBAAyB;gBACvB,oBAAoB;YACtB;QACF;QACA,uFAAuF;YACrF,oBAAoB;QACtB;QACA,qFAAqF;YACnF,kBAAkB;QACpB;QACA,iGAAiG;YAC/F,kBAAkB;YAClB,yBAAyB;gBACvB,kBAAkB;YACpB;QACF;QACA,yEAAyE;YACvE,oBAAoB;YACpB,yBAAyB;gBACvB,oBAAoB;YACtB;QACF;QACA,6DAA6D;YAC3D,oBAAoB;QACtB;QACA,uEAAuE;YACrE,kBAAkB;YAClB,yBAAyB;gBACvB,kBAAkB;YACpB;QACF;QACA,2DAA2D;YACzD,kBAAkB;QACpB;QACA,mCAAmC;YACjC,UAAU;gBACR,UAAU;YACZ;QACF;QACA,yBAAyB;YACvB,+CAA+C;gBAC7C,SAAS;YACX;YACA,8DAA8D;gBAC5D,YAAY,MAAM,WAAW,CAAC,MAAM,CAAC;oBAAC;oBAAW;iBAAY,EAAE;oBAC7D,UAAU,MAAM,WAAW,CAAC,QAAQ,CAAC,OAAO;gBAC9C;YACF;QACF;QACA,qDAAqD;YACnD,cAAc;YACd,UAAU;YACV,YAAY;YACZ,yBAAyB;gBACvB,SAAS;YACX;YACA,iBAAiB;gBACf,yBAAyB;oBACvB,SAAS;gBACX;YACF;QACF;QACA,6BAA6B;YAC3B,YAAY;YACZ,QAAQ,MAAM,OAAO,CAAC,GAAG,GAAG;YAC5B,UAAU,MAAM,UAAU,CAAC,KAAK,CAAC,QAAQ;YACzC,eAAe;gBACb,OAAO;YACT;YACA,kBAAkB;gBAChB,OAAO;YACT;QACF;QAEA,aAAa;QACb,0GAA0G;YACxG,kBAAkB;gBAChB,YAAY;gBACZ,WAAW;YACb;QACF;QACA,uBAAuB;YACrB,mBAAmB;YACnB,WAAW;YACX,YAAY;YACZ,wBAAwB;gBACtB,kBAAkB;YACpB;QACF;QACA,oCAAoC;YAClC,kBAAkB;gBAChB,OAAO;YACT;QACF;QACA,8DAA8D;YAC5D,iBAAiB;QACnB;QAEA,mBAAmB;QACnB,6DAA6D;YAC3D,cAAc;YACd,4BAA4B;gBAC1B,eAAe;gBACf,cAAc;YAChB;YACA,wCAAwC;gBACtC,oBAAoB;YACtB;YACA,iBAAiB;gBACf,cAAc;YAChB;YACA,mCAAmC;gBACjC,KAAK;YACP;QACF;QACA,uDAAuD;YACrD,cAAc;YACd,oBAAoB;YACpB,iBAAiB;gBACf,cAAc;gBACd,oBAAoB;gBACpB,0BAA0B;oBACxB,cAAc;oBACd,eAAe;gBACjB;YACF;YACA,4BAA4B;gBAC1B,eAAe;YACjB;QACF;QACA,gCAAgC;YAC9B,SAAS;YACT,KAAK;YACL,0BAA0B;gBACxB,QAAQ;YACV;QACF;QACA,0EAA0E;YACxE,OAAO;QACT;QAEA,eAAe;QACf,6BAA6B;YAC3B,4BAA4B;gBAC1B,SAAS;gBACT,iBAAiB;oBACf,SAAS;gBACX;YACF;YACA,4DAA4D;gBAC1D,WAAW;YACb;QACF;IACF,CAAC;KA5OK;AA8ON,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,QAAE,CAAC,OAAO;IACzC,MAAM,EAAE,OAAO,OAAO,EAAE,SAAS,EAAE,GAAG,MAAM,GAAG;IAE/C,qBACE,6LAAC;QACC,MAAM;QACN,UAAU;QACT,GAAG,IAAI;QACR,SAAQ;QACR,WAAW;YACT,GAAG,SAAS;YACZ,YAAY;gBAAE,GAAG,WAAW,UAAU;gBAAE,QAAQ;YAAK;QACvD;;;;;;AAGN;;uCAEe", "debugId": null}}, {"offset": {"line": 293, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/views/apps/user/jobs/TableFilters.jsx"], "sourcesContent": ["'use client'\r\n\r\n// React Imports\r\nimport { useState, useEffect } from 'react'\r\n\r\n// MUI Imports\r\nimport CardContent from '@mui/material/CardContent'\r\nimport FormControl from '@mui/material/FormControl'\r\nimport InputLabel from '@mui/material/InputLabel'\r\nimport MenuItem from '@mui/material/MenuItem'\r\nimport Select from '@mui/material/Select'\r\nimport Button from '@mui/material/Button'\r\nimport Box from '@mui/material/Box'\r\n\r\n// Component Imports\r\nimport CustomTextField from '@core/components/mui/TextField'\r\n\r\nconst TableFilters = ({ setData, tableData }) => {\r\n  // States\r\n  const [position, setPosition] = useState('')\r\n  const [employmentType, setEmploymentType] = useState('')\r\n  const [experience, setExperience] = useState('')\r\n  const [relocate, setRelocate] = useState('')\r\n  const [commercialLicense, setCommercialLicense] = useState('')\r\n\r\n  useEffect(() => {\r\n    const filteredData = tableData?.filter(user => {\r\n      if (position && (user.position || '').toLowerCase() !== position.toLowerCase()) return false\r\n      if (employmentType && (user.employmentType || user.employment_type || '').toLowerCase() !== employmentType.toLowerCase()) return false\r\n      if (experience && (user.experience || '').toLowerCase() !== experience.toLowerCase()) return false\r\n      if (relocate && (user.relocate || '').toLowerCase() !== relocate.toLowerCase()) return false\r\n      if (commercialLicense && commercialLicense !== 'all') {\r\n        const hasLicense = user.commercialLicense || user.commercial_license || false\r\n        if (commercialLicense === 'yes' && !hasLicense) return false\r\n        if (commercialLicense === 'no' && hasLicense) return false\r\n      }\r\n\r\n      return true\r\n    })\r\n\r\n    setData(filteredData)\r\n  }, [position, employmentType, experience, relocate, commercialLicense, tableData, setData])\r\n\r\n  const clearAllFilters = () => {\r\n    setPosition('')\r\n    setEmploymentType('')\r\n    setExperience('')\r\n    setRelocate('')\r\n    setCommercialLicense('')\r\n  }\r\n\r\n  const hasActiveFilters = position || employmentType || experience || relocate || commercialLicense\r\n\r\n  return (\r\n    <CardContent>\r\n      <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4'>\r\n        <FormControl fullWidth>\r\n          <InputLabel id='position-select'>Position Category</InputLabel>\r\n          <Select\r\n            fullWidth\r\n            id='select-position'\r\n            value={position}\r\n            onChange={e => setPosition(e.target.value)}\r\n            label='Position Category'\r\n            labelId='position-select'\r\n            inputProps={{ placeholder: 'Select Position Category' }}\r\n          >\r\n            <MenuItem value=''>All Positions</MenuItem>\r\n            <MenuItem value='Driving Position'>Driving Position</MenuItem>\r\n            <MenuItem value='Non-Driving Position'>Non-Driving Position</MenuItem>\r\n          </Select>\r\n        </FormControl>\r\n\r\n        <FormControl fullWidth>\r\n          <InputLabel id='employment-type-select'>Employment Type</InputLabel>\r\n          <Select\r\n            fullWidth\r\n            id='select-employment-type'\r\n            value={employmentType}\r\n            onChange={e => setEmploymentType(e.target.value)}\r\n            label='Employment Type'\r\n            labelId='employment-type-select'\r\n            inputProps={{ placeholder: 'Select Employment Type' }}\r\n          >\r\n            <MenuItem value=''>All Employment Types</MenuItem>\r\n            <MenuItem value='Full-time'>Full-time</MenuItem>\r\n            <MenuItem value='Part-time'>Part-time</MenuItem>\r\n            <MenuItem value='Contract'>Contract</MenuItem>\r\n            <MenuItem value='Temporary'>Temporary</MenuItem>\r\n            <MenuItem value='Freelance'>Freelance</MenuItem>\r\n          </Select>\r\n        </FormControl>\r\n\r\n        <FormControl fullWidth>\r\n          <InputLabel id='experience-select'>Experience Level</InputLabel>\r\n          <Select\r\n            fullWidth\r\n            id='select-experience'\r\n            value={experience}\r\n            onChange={e => setExperience(e.target.value)}\r\n            label='Experience Level'\r\n            labelId='experience-select'\r\n            inputProps={{ placeholder: 'Select Experience Level' }}\r\n          >\r\n            <MenuItem value=''>All Experience Levels</MenuItem>\r\n            <MenuItem value='Fresher'>Fresher</MenuItem>\r\n            <MenuItem value='<6 months'>&lt;6 months</MenuItem>\r\n            <MenuItem value='6-12 months'>6-12 months</MenuItem>\r\n            <MenuItem value='1-2 years'>1-2 years</MenuItem>\r\n            <MenuItem value='2-5 years'>2-5 years</MenuItem>\r\n            <MenuItem value='5+ years'>5+ years</MenuItem>\r\n            <MenuItem value='10+ years'>10+ years</MenuItem>\r\n          </Select>\r\n        </FormControl>\r\n\r\n        <FormControl fullWidth>\r\n          <InputLabel id='relocate-select'>Willing to Relocate</InputLabel>\r\n          <Select\r\n            fullWidth\r\n            id='select-relocate'\r\n            value={relocate}\r\n            onChange={e => setRelocate(e.target.value)}\r\n            label='Willing to Relocate'\r\n            labelId='relocate-select'\r\n            inputProps={{ placeholder: 'Select Relocation Preference' }}\r\n          >\r\n            <MenuItem value=''>All Preferences</MenuItem>\r\n            <MenuItem value='Yes'>Yes</MenuItem>\r\n            <MenuItem value='No'>No</MenuItem>\r\n          </Select>\r\n        </FormControl>\r\n\r\n        <FormControl fullWidth>\r\n          <InputLabel id='commercial-license-select'>Commercial License</InputLabel>\r\n          <Select\r\n            fullWidth\r\n            id='select-commercial-license'\r\n            value={commercialLicense}\r\n            onChange={e => setCommercialLicense(e.target.value)}\r\n            label='Commercial License'\r\n            labelId='commercial-license-select'\r\n            inputProps={{ placeholder: 'Select License Status' }}\r\n          >\r\n            <MenuItem value=''>All License Status</MenuItem>\r\n            <MenuItem value='yes'>Has Commercial License</MenuItem>\r\n            <MenuItem value='no'>No Commercial License</MenuItem>\r\n          </Select>\r\n        </FormControl>\r\n      </div>\r\n\r\n      {/* Clear Filters Button */}\r\n      {hasActiveFilters && (\r\n        <Box className='mt-4 flex justify-center'>\r\n          <Button\r\n            variant='outlined'\r\n            color='secondary'\r\n            onClick={clearAllFilters}\r\n            startIcon={<i className='tabler-filter-off' />}\r\n            size='small'\r\n          >\r\n            Clear All Filters\r\n          </Button>\r\n        </Box>\r\n      )}\r\n    </CardContent>\r\n  )\r\n}\r\n\r\nexport default TableFilters\r\n"], "names": [], "mappings": ";;;;AAEA,gBAAgB;AAChB;AAEA,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AAEA,oBAAoB;AACpB;;;AAfA;;;;;;;;;;AAiBA,MAAM,eAAe,CAAC,EAAE,OAAO,EAAE,SAAS,EAAE;;IAC1C,SAAS;IACT,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM,eAAe,WAAW;0CAAO,CAAA;oBACrC,IAAI,YAAY,CAAC,KAAK,QAAQ,IAAI,EAAE,EAAE,WAAW,OAAO,SAAS,WAAW,IAAI,OAAO;oBACvF,IAAI,kBAAkB,CAAC,KAAK,cAAc,IAAI,KAAK,eAAe,IAAI,EAAE,EAAE,WAAW,OAAO,eAAe,WAAW,IAAI,OAAO;oBACjI,IAAI,cAAc,CAAC,KAAK,UAAU,IAAI,EAAE,EAAE,WAAW,OAAO,WAAW,WAAW,IAAI,OAAO;oBAC7F,IAAI,YAAY,CAAC,KAAK,QAAQ,IAAI,EAAE,EAAE,WAAW,OAAO,SAAS,WAAW,IAAI,OAAO;oBACvF,IAAI,qBAAqB,sBAAsB,OAAO;wBACpD,MAAM,aAAa,KAAK,iBAAiB,IAAI,KAAK,kBAAkB,IAAI;wBACxE,IAAI,sBAAsB,SAAS,CAAC,YAAY,OAAO;wBACvD,IAAI,sBAAsB,QAAQ,YAAY,OAAO;oBACvD;oBAEA,OAAO;gBACT;;YAEA,QAAQ;QACV;iCAAG;QAAC;QAAU;QAAgB;QAAY;QAAU;QAAmB;QAAW;KAAQ;IAE1F,MAAM,kBAAkB;QACtB,YAAY;QACZ,kBAAkB;QAClB,cAAc;QACd,YAAY;QACZ,qBAAqB;IACvB;IAEA,MAAM,mBAAmB,YAAY,kBAAkB,cAAc,YAAY;IAEjF,qBACE,6LAAC,kKAAA,CAAA,UAAW;;0BACV,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,kKAAA,CAAA,UAAW;wBAAC,SAAS;;0CACpB,6LAAC,gKAAA,CAAA,UAAU;gCAAC,IAAG;0CAAkB;;;;;;0CACjC,6LAAC,wJAAA,CAAA,UAAM;gCACL,SAAS;gCACT,IAAG;gCACH,OAAO;gCACP,UAAU,CAAA,IAAK,YAAY,EAAE,MAAM,CAAC,KAAK;gCACzC,OAAM;gCACN,SAAQ;gCACR,YAAY;oCAAE,aAAa;gCAA2B;;kDAEtD,6LAAC,4JAAA,CAAA,UAAQ;wCAAC,OAAM;kDAAG;;;;;;kDACnB,6LAAC,4JAAA,CAAA,UAAQ;wCAAC,OAAM;kDAAmB;;;;;;kDACnC,6LAAC,4JAAA,CAAA,UAAQ;wCAAC,OAAM;kDAAuB;;;;;;;;;;;;;;;;;;kCAI3C,6LAAC,kKAAA,CAAA,UAAW;wBAAC,SAAS;;0CACpB,6LAAC,gKAAA,CAAA,UAAU;gCAAC,IAAG;0CAAyB;;;;;;0CACxC,6LAAC,wJAAA,CAAA,UAAM;gCACL,SAAS;gCACT,IAAG;gCACH,OAAO;gCACP,UAAU,CAAA,IAAK,kBAAkB,EAAE,MAAM,CAAC,KAAK;gCAC/C,OAAM;gCACN,SAAQ;gCACR,YAAY;oCAAE,aAAa;gCAAyB;;kDAEpD,6LAAC,4JAAA,CAAA,UAAQ;wCAAC,OAAM;kDAAG;;;;;;kDACnB,6LAAC,4JAAA,CAAA,UAAQ;wCAAC,OAAM;kDAAY;;;;;;kDAC5B,6LAAC,4JAAA,CAAA,UAAQ;wCAAC,OAAM;kDAAY;;;;;;kDAC5B,6LAAC,4JAAA,CAAA,UAAQ;wCAAC,OAAM;kDAAW;;;;;;kDAC3B,6LAAC,4JAAA,CAAA,UAAQ;wCAAC,OAAM;kDAAY;;;;;;kDAC5B,6LAAC,4JAAA,CAAA,UAAQ;wCAAC,OAAM;kDAAY;;;;;;;;;;;;;;;;;;kCAIhC,6LAAC,kKAAA,CAAA,UAAW;wBAAC,SAAS;;0CACpB,6LAAC,gKAAA,CAAA,UAAU;gCAAC,IAAG;0CAAoB;;;;;;0CACnC,6LAAC,wJAAA,CAAA,UAAM;gCACL,SAAS;gCACT,IAAG;gCACH,OAAO;gCACP,UAAU,CAAA,IAAK,cAAc,EAAE,MAAM,CAAC,KAAK;gCAC3C,OAAM;gCACN,SAAQ;gCACR,YAAY;oCAAE,aAAa;gCAA0B;;kDAErD,6LAAC,4JAAA,CAAA,UAAQ;wCAAC,OAAM;kDAAG;;;;;;kDACnB,6LAAC,4JAAA,CAAA,UAAQ;wCAAC,OAAM;kDAAU;;;;;;kDAC1B,6LAAC,4JAAA,CAAA,UAAQ;wCAAC,OAAM;kDAAY;;;;;;kDAC5B,6LAAC,4JAAA,CAAA,UAAQ;wCAAC,OAAM;kDAAc;;;;;;kDAC9B,6LAAC,4JAAA,CAAA,UAAQ;wCAAC,OAAM;kDAAY;;;;;;kDAC5B,6LAAC,4JAAA,CAAA,UAAQ;wCAAC,OAAM;kDAAY;;;;;;kDAC5B,6LAAC,4JAAA,CAAA,UAAQ;wCAAC,OAAM;kDAAW;;;;;;kDAC3B,6LAAC,4JAAA,CAAA,UAAQ;wCAAC,OAAM;kDAAY;;;;;;;;;;;;;;;;;;kCAIhC,6LAAC,kKAAA,CAAA,UAAW;wBAAC,SAAS;;0CACpB,6LAAC,gKAAA,CAAA,UAAU;gCAAC,IAAG;0CAAkB;;;;;;0CACjC,6LAAC,wJAAA,CAAA,UAAM;gCACL,SAAS;gCACT,IAAG;gCACH,OAAO;gCACP,UAAU,CAAA,IAAK,YAAY,EAAE,MAAM,CAAC,KAAK;gCACzC,OAAM;gCACN,SAAQ;gCACR,YAAY;oCAAE,aAAa;gCAA+B;;kDAE1D,6LAAC,4JAAA,CAAA,UAAQ;wCAAC,OAAM;kDAAG;;;;;;kDACnB,6LAAC,4JAAA,CAAA,UAAQ;wCAAC,OAAM;kDAAM;;;;;;kDACtB,6LAAC,4JAAA,CAAA,UAAQ;wCAAC,OAAM;kDAAK;;;;;;;;;;;;;;;;;;kCAIzB,6LAAC,kKAAA,CAAA,UAAW;wBAAC,SAAS;;0CACpB,6LAAC,gKAAA,CAAA,UAAU;gCAAC,IAAG;0CAA4B;;;;;;0CAC3C,6LAAC,wJAAA,CAAA,UAAM;gCACL,SAAS;gCACT,IAAG;gCACH,OAAO;gCACP,UAAU,CAAA,IAAK,qBAAqB,EAAE,MAAM,CAAC,KAAK;gCAClD,OAAM;gCACN,SAAQ;gCACR,YAAY;oCAAE,aAAa;gCAAwB;;kDAEnD,6LAAC,4JAAA,CAAA,UAAQ;wCAAC,OAAM;kDAAG;;;;;;kDACnB,6LAAC,4JAAA,CAAA,UAAQ;wCAAC,OAAM;kDAAM;;;;;;kDACtB,6LAAC,4JAAA,CAAA,UAAQ;wCAAC,OAAM;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;YAM1B,kCACC,6LAAC,kJAAA,CAAA,UAAG;gBAAC,WAAU;0BACb,cAAA,6LAAC,wJAAA,CAAA,UAAM;oBACL,SAAQ;oBACR,OAAM;oBACN,SAAS;oBACT,yBAAW,6LAAC;wBAAE,WAAU;;;;;;oBACxB,MAAK;8BACN;;;;;;;;;;;;;;;;;AAOX;GArJM;KAAA;uCAuJS", "debugId": null}}, {"offset": {"line": 775, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/services/jobApi.js"], "sourcesContent": ["// API service for job application management\r\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8090'\r\n\r\n// Fetch all job applications from backend\r\nexport const fetchJobApplications = async () => {\r\n  try {\r\n    const response = await fetch(`${API_BASE_URL}/jobs/get-jobs`, {\r\n      method: 'GET',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n      },\r\n    })\r\n\r\n    if (!response.ok) {\r\n      throw new Error(`HTTP error! status: ${response.status}`)\r\n    }\r\n\r\n    const applications = await response.json()\r\n\r\n    // Transform backend data to match frontend structure\r\n    return applications.map(app => ({\r\n      id: app._id,\r\n      fullName: `${app.first_name} ${app.last_name}`,\r\n      firstName: app.first_name,\r\n      lastName: app.last_name,\r\n      email: app.email,\r\n      phone: app.phone_number,\r\n      phoneNumber: app.phone_number,\r\n      dob: app.dob,\r\n      address: app.address,\r\n      position: app.position,\r\n      specificDrivingRole: app.specific_driving_role,\r\n      specificNonDrivingRole: app.specific_non_driving_role,\r\n      commercialLicense: app.commercial_license,\r\n      otherJob: app.other_job,\r\n      employmentType: app.employment_type,\r\n      employment_type: app.employment_type,\r\n      preferredStartDate: app.preferred_start_date,\r\n      relocate: app.relocate,\r\n      experience: app.experience,\r\n      resume: app.resume,\r\n      workReason: app.work_reason,\r\n      reference: app.reference,\r\n      otherReference: app.other_reference,\r\n      ip: app.ip,\r\n      status: 'pending', // Default status for new applications (frontend-only)\r\n      appliedDate: app.createdAt, // Keep full timestamp for dynamic time display\r\n      createdAt: app.createdAt,\r\n      updatedAt: app.updatedAt,\r\n      // Add avatar placeholder\r\n      avatar: null\r\n    }))\r\n  } catch (error) {\r\n    console.error('Error fetching job applications:', error)\r\n    throw error\r\n  }\r\n}\r\n\r\n// Update job application status\r\nexport const updateJobApplicationStatus = async (applicationId, status) => {\r\n  try {\r\n    console.log('API: Updating job application status', applicationId, 'to', status)\r\n    console.log('API URL:', `${API_BASE_URL}/jobs/update-status/${applicationId}`)\r\n\r\n    const response = await fetch(`${API_BASE_URL}/jobs/update-status/${applicationId}`, {\r\n      method: 'PATCH',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n      },\r\n      body: JSON.stringify({ status })\r\n    })\r\n\r\n    console.log('API Response status:', response.status)\r\n    console.log('API Response ok:', response.ok)\r\n\r\n    if (!response.ok) {\r\n      const errorText = await response.text()\r\n      console.error('API Error response:', errorText)\r\n      throw new Error(`HTTP error! status: ${response.status} - ${errorText}`)\r\n    }\r\n\r\n    const result = await response.json()\r\n    console.log('API Success result:', result)\r\n    return result\r\n  } catch (error) {\r\n    console.error('❌ Error updating job application status:', error)\r\n    throw error\r\n  }\r\n}\r\n\r\n// Delete job application from backend\r\nexport const deleteJobApplication = async (applicationId) => {\r\n  try {\r\n    const response = await fetch(`${API_BASE_URL}/jobs/delete-job/${applicationId}`, {\r\n      method: 'DELETE',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n      },\r\n    })\r\n\r\n    if (!response.ok) {\r\n      throw new Error(`HTTP error! status: ${response.status}`)\r\n    }\r\n\r\n    const result = await response.json()\r\n    return result\r\n  } catch (error) {\r\n    console.error('Error deleting job application:', error)\r\n    throw error\r\n  }\r\n}\r\n\r\n// Download resume file\r\nexport const downloadResume = async (resumePath, applicantName) => {\r\n  try {\r\n    console.log('=== FRONTEND DOWNLOAD DEBUG ===')\r\n    console.log('Resume path from DB:', resumePath)\r\n    console.log('Applicant name:', applicantName)\r\n    console.log('API Base URL:', API_BASE_URL)\r\n\r\n    // Extract just the filename from the path\r\n    // Handle cases where resumePath might be \"uploads/resumes/filename.pdf\" or just \"filename.pdf\"\r\n    let fileName = resumePath\r\n\r\n    // Remove any path components - split by both forward and back slashes\r\n    if (resumePath.includes('/') || resumePath.includes('\\\\')) {\r\n      const parts = resumePath.split(/[/\\\\]/)\r\n      fileName = parts[parts.length - 1] // Get the last part (filename)\r\n    }\r\n\r\n    console.log('Original resume path:', resumePath)\r\n    console.log('Extracted filename:', fileName)\r\n\r\n    // Double check - if fileName still contains path separators, something is wrong\r\n    if (fileName.includes('/') || fileName.includes('\\\\')) {\r\n      console.error('❌ Filename still contains path separators:', fileName)\r\n      // Force extract just the actual filename\r\n      fileName = fileName.replace(/.*[/\\\\]/, '')\r\n      console.log('Force extracted filename:', fileName)\r\n    }\r\n\r\n    // Validate the filename before making the request\r\n    if (!fileName || fileName.trim() === '') {\r\n      throw new Error('Invalid filename extracted from resume path')\r\n    }\r\n\r\n    // Use the dedicated download route\r\n    const downloadUrl = `${API_BASE_URL}/jobs/download-resume/${fileName}`\r\n    console.log('Download URL:', downloadUrl)\r\n\r\n    // Final validation - make sure URL doesn't have double paths\r\n    if (downloadUrl.includes('uploads/resumes') && downloadUrl.split('uploads/resumes').length > 2) {\r\n      console.error('❌ URL contains duplicate path components:', downloadUrl)\r\n      throw new Error('Invalid download URL generated')\r\n    }\r\n\r\n    const response = await fetch(downloadUrl, {\r\n      method: 'GET',\r\n      mode: 'cors',\r\n      credentials: 'omit',\r\n      headers: {\r\n        'Accept': '*/*'\r\n      }\r\n    })\r\n\r\n    console.log('Response status:', response.status)\r\n    console.log('Response ok:', response.ok)\r\n    console.log('Response headers:', Object.fromEntries(response.headers.entries()))\r\n\r\n    if (!response.ok) {\r\n      const errorText = await response.text()\r\n      console.error('Response error:', errorText)\r\n      throw new Error(`HTTP ${response.status}: ${errorText}`)\r\n    }\r\n\r\n    const blob = await response.blob()\r\n    console.log('✅ Download successful! Blob size:', blob.size, 'Type:', blob.type)\r\n\r\n    // Create download link\r\n    const url = window.URL.createObjectURL(blob)\r\n    const link = document.createElement('a')\r\n    link.href = url\r\n\r\n    // Get file extension and create clean filename\r\n    const fileExtension = fileName.split('.').pop() || 'pdf'\r\n    const cleanApplicantName = applicantName.replace(/[^a-zA-Z0-9\\s]/g, '').replace(/\\s+/g, '_')\r\n    const downloadFileName = `${cleanApplicantName}_Resume.${fileExtension}`\r\n\r\n    link.download = downloadFileName\r\n    link.style.display = 'none'\r\n    document.body.appendChild(link)\r\n    link.click()\r\n\r\n    // Cleanup after a short delay\r\n    setTimeout(() => {\r\n      window.URL.revokeObjectURL(url)\r\n      document.body.removeChild(link)\r\n    }, 100)\r\n\r\n    console.log('✅ Resume downloaded successfully:', downloadFileName)\r\n    return { success: true, fileName: downloadFileName }\r\n\r\n  } catch (error) {\r\n    console.error('❌ Error downloading resume:', error)\r\n\r\n    // Try fallback method with direct static file access\r\n    try {\r\n      console.log('Trying fallback method...')\r\n      const fileName = resumePath.split('/').pop() || resumePath\r\n      const fallbackUrl = `${API_BASE_URL}/uploads/resumes/${fileName}`\r\n      console.log('Fallback URL:', fallbackUrl)\r\n\r\n      const fallbackResponse = await fetch(fallbackUrl, {\r\n        method: 'GET',\r\n        mode: 'cors'\r\n      })\r\n\r\n      if (fallbackResponse.ok) {\r\n        const blob = await fallbackResponse.blob()\r\n        const url = window.URL.createObjectURL(blob)\r\n        const link = document.createElement('a')\r\n        link.href = url\r\n\r\n        const fileExtension = fileName.split('.').pop() || 'pdf'\r\n        const cleanApplicantName = applicantName.replace(/[^a-zA-Z0-9\\s]/g, '').replace(/\\s+/g, '_')\r\n        const downloadFileName = `${cleanApplicantName}_Resume.${fileExtension}`\r\n\r\n        link.download = downloadFileName\r\n        link.style.display = 'none'\r\n        document.body.appendChild(link)\r\n        link.click()\r\n\r\n        setTimeout(() => {\r\n          window.URL.revokeObjectURL(url)\r\n          document.body.removeChild(link)\r\n        }, 100)\r\n\r\n        console.log('✅ Fallback download successful:', downloadFileName)\r\n        return { success: true, fileName: downloadFileName }\r\n      }\r\n    } catch (fallbackError) {\r\n      console.error('❌ Fallback method also failed:', fallbackError)\r\n    }\r\n\r\n    throw error\r\n  }\r\n}\r\n\r\n// Get job application by ID\r\nexport const getJobApplicationById = async (applicationId) => {\r\n  try {\r\n    const applications = await fetchJobApplications()\r\n    return applications.find(app => app.id === applicationId)\r\n  } catch (error) {\r\n    console.error('Error fetching job application by ID:', error)\r\n    throw error\r\n  }\r\n}\r\n\r\n// Test function to debug filename extraction\r\nexport const testFilenameExtraction = (resumePath) => {\r\n  console.log('=== FILENAME EXTRACTION TEST ===')\r\n  console.log('Input:', resumePath)\r\n\r\n  let fileName = resumePath\r\n\r\n  if (resumePath.includes('/') || resumePath.includes('\\\\')) {\r\n    const parts = resumePath.split(/[/\\\\]/)\r\n    fileName = parts[parts.length - 1]\r\n  }\r\n\r\n  console.log('Output:', fileName)\r\n  console.log('Expected URL:', `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8090'}/jobs/download-resume/${fileName}`)\r\n\r\n  return fileName\r\n}\r\n"], "names": [], "mappings": "AAAA,6CAA6C;;;;;;;;;AACxB;AAArB,MAAM,eAAe,6DAAmC;AAGjD,MAAM,uBAAuB;IAClC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,cAAc,CAAC,EAAE;YAC5D,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QAEA,MAAM,eAAe,MAAM,SAAS,IAAI;QAExC,qDAAqD;QACrD,OAAO,aAAa,GAAG,CAAC,CAAA,MAAO,CAAC;gBAC9B,IAAI,IAAI,GAAG;gBACX,UAAU,GAAG,IAAI,UAAU,CAAC,CAAC,EAAE,IAAI,SAAS,EAAE;gBAC9C,WAAW,IAAI,UAAU;gBACzB,UAAU,IAAI,SAAS;gBACvB,OAAO,IAAI,KAAK;gBAChB,OAAO,IAAI,YAAY;gBACvB,aAAa,IAAI,YAAY;gBAC7B,KAAK,IAAI,GAAG;gBACZ,SAAS,IAAI,OAAO;gBACpB,UAAU,IAAI,QAAQ;gBACtB,qBAAqB,IAAI,qBAAqB;gBAC9C,wBAAwB,IAAI,yBAAyB;gBACrD,mBAAmB,IAAI,kBAAkB;gBACzC,UAAU,IAAI,SAAS;gBACvB,gBAAgB,IAAI,eAAe;gBACnC,iBAAiB,IAAI,eAAe;gBACpC,oBAAoB,IAAI,oBAAoB;gBAC5C,UAAU,IAAI,QAAQ;gBACtB,YAAY,IAAI,UAAU;gBAC1B,QAAQ,IAAI,MAAM;gBAClB,YAAY,IAAI,WAAW;gBAC3B,WAAW,IAAI,SAAS;gBACxB,gBAAgB,IAAI,eAAe;gBACnC,IAAI,IAAI,EAAE;gBACV,QAAQ;gBACR,aAAa,IAAI,SAAS;gBAC1B,WAAW,IAAI,SAAS;gBACxB,WAAW,IAAI,SAAS;gBACxB,yBAAyB;gBACzB,QAAQ;YACV,CAAC;IACH,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,MAAM;IACR;AACF;AAGO,MAAM,6BAA6B,OAAO,eAAe;IAC9D,IAAI;QACF,QAAQ,GAAG,CAAC,wCAAwC,eAAe,MAAM;QACzE,QAAQ,GAAG,CAAC,YAAY,GAAG,aAAa,oBAAoB,EAAE,eAAe;QAE7E,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,oBAAoB,EAAE,eAAe,EAAE;YAClF,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBAAE;YAAO;QAChC;QAEA,QAAQ,GAAG,CAAC,wBAAwB,SAAS,MAAM;QACnD,QAAQ,GAAG,CAAC,oBAAoB,SAAS,EAAE;QAE3C,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,QAAQ,KAAK,CAAC,uBAAuB;YACrC,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,CAAC,GAAG,EAAE,WAAW;QACzE;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAClC,QAAQ,GAAG,CAAC,uBAAuB;QACnC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4CAA4C;QAC1D,MAAM;IACR;AACF;AAGO,MAAM,uBAAuB,OAAO;IACzC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,iBAAiB,EAAE,eAAe,EAAE;YAC/E,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAClC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,MAAM;IACR;AACF;AAGO,MAAM,iBAAiB,OAAO,YAAY;IAC/C,IAAI;QACF,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,wBAAwB;QACpC,QAAQ,GAAG,CAAC,mBAAmB;QAC/B,QAAQ,GAAG,CAAC,iBAAiB;QAE7B,0CAA0C;QAC1C,+FAA+F;QAC/F,IAAI,WAAW;QAEf,sEAAsE;QACtE,IAAI,WAAW,QAAQ,CAAC,QAAQ,WAAW,QAAQ,CAAC,OAAO;YACzD,MAAM,QAAQ,WAAW,KAAK,CAAC;YAC/B,WAAW,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,CAAC,+BAA+B;;QACpE;QAEA,QAAQ,GAAG,CAAC,yBAAyB;QACrC,QAAQ,GAAG,CAAC,uBAAuB;QAEnC,gFAAgF;QAChF,IAAI,SAAS,QAAQ,CAAC,QAAQ,SAAS,QAAQ,CAAC,OAAO;YACrD,QAAQ,KAAK,CAAC,8CAA8C;YAC5D,yCAAyC;YACzC,WAAW,SAAS,OAAO,CAAC,WAAW;YACvC,QAAQ,GAAG,CAAC,6BAA6B;QAC3C;QAEA,kDAAkD;QAClD,IAAI,CAAC,YAAY,SAAS,IAAI,OAAO,IAAI;YACvC,MAAM,IAAI,MAAM;QAClB;QAEA,mCAAmC;QACnC,MAAM,cAAc,GAAG,aAAa,sBAAsB,EAAE,UAAU;QACtE,QAAQ,GAAG,CAAC,iBAAiB;QAE7B,6DAA6D;QAC7D,IAAI,YAAY,QAAQ,CAAC,sBAAsB,YAAY,KAAK,CAAC,mBAAmB,MAAM,GAAG,GAAG;YAC9F,QAAQ,KAAK,CAAC,6CAA6C;YAC3D,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,MAAM,MAAM,aAAa;YACxC,QAAQ;YACR,MAAM;YACN,aAAa;YACb,SAAS;gBACP,UAAU;YACZ;QACF;QAEA,QAAQ,GAAG,CAAC,oBAAoB,SAAS,MAAM;QAC/C,QAAQ,GAAG,CAAC,gBAAgB,SAAS,EAAE;QACvC,QAAQ,GAAG,CAAC,qBAAqB,OAAO,WAAW,CAAC,SAAS,OAAO,CAAC,OAAO;QAE5E,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,QAAQ,KAAK,CAAC,mBAAmB;YACjC,MAAM,IAAI,MAAM,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,WAAW;QACzD;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,QAAQ,GAAG,CAAC,qCAAqC,KAAK,IAAI,EAAE,SAAS,KAAK,IAAI;QAE9E,uBAAuB;QACvB,MAAM,MAAM,OAAO,GAAG,CAAC,eAAe,CAAC;QACvC,MAAM,OAAO,SAAS,aAAa,CAAC;QACpC,KAAK,IAAI,GAAG;QAEZ,+CAA+C;QAC/C,MAAM,gBAAgB,SAAS,KAAK,CAAC,KAAK,GAAG,MAAM;QACnD,MAAM,qBAAqB,cAAc,OAAO,CAAC,mBAAmB,IAAI,OAAO,CAAC,QAAQ;QACxF,MAAM,mBAAmB,GAAG,mBAAmB,QAAQ,EAAE,eAAe;QAExE,KAAK,QAAQ,GAAG;QAChB,KAAK,KAAK,CAAC,OAAO,GAAG;QACrB,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,KAAK,KAAK;QAEV,8BAA8B;QAC9B,WAAW;YACT,OAAO,GAAG,CAAC,eAAe,CAAC;YAC3B,SAAS,IAAI,CAAC,WAAW,CAAC;QAC5B,GAAG;QAEH,QAAQ,GAAG,CAAC,qCAAqC;QACjD,OAAO;YAAE,SAAS;YAAM,UAAU;QAAiB;IAErD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAE7C,qDAAqD;QACrD,IAAI;YACF,QAAQ,GAAG,CAAC;YACZ,MAAM,WAAW,WAAW,KAAK,CAAC,KAAK,GAAG,MAAM;YAChD,MAAM,cAAc,GAAG,aAAa,iBAAiB,EAAE,UAAU;YACjE,QAAQ,GAAG,CAAC,iBAAiB;YAE7B,MAAM,mBAAmB,MAAM,MAAM,aAAa;gBAChD,QAAQ;gBACR,MAAM;YACR;YAEA,IAAI,iBAAiB,EAAE,EAAE;gBACvB,MAAM,OAAO,MAAM,iBAAiB,IAAI;gBACxC,MAAM,MAAM,OAAO,GAAG,CAAC,eAAe,CAAC;gBACvC,MAAM,OAAO,SAAS,aAAa,CAAC;gBACpC,KAAK,IAAI,GAAG;gBAEZ,MAAM,gBAAgB,SAAS,KAAK,CAAC,KAAK,GAAG,MAAM;gBACnD,MAAM,qBAAqB,cAAc,OAAO,CAAC,mBAAmB,IAAI,OAAO,CAAC,QAAQ;gBACxF,MAAM,mBAAmB,GAAG,mBAAmB,QAAQ,EAAE,eAAe;gBAExE,KAAK,QAAQ,GAAG;gBAChB,KAAK,KAAK,CAAC,OAAO,GAAG;gBACrB,SAAS,IAAI,CAAC,WAAW,CAAC;gBAC1B,KAAK,KAAK;gBAEV,WAAW;oBACT,OAAO,GAAG,CAAC,eAAe,CAAC;oBAC3B,SAAS,IAAI,CAAC,WAAW,CAAC;gBAC5B,GAAG;gBAEH,QAAQ,GAAG,CAAC,mCAAmC;gBAC/C,OAAO;oBAAE,SAAS;oBAAM,UAAU;gBAAiB;YACrD;QACF,EAAE,OAAO,eAAe;YACtB,QAAQ,KAAK,CAAC,kCAAkC;QAClD;QAEA,MAAM;IACR;AACF;AAGO,MAAM,wBAAwB,OAAO;IAC1C,IAAI;QACF,MAAM,eAAe,MAAM;QAC3B,OAAO,aAAa,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;IAC7C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yCAAyC;QACvD,MAAM;IACR;AACF;AAGO,MAAM,yBAAyB,CAAC;IACrC,QAAQ,GAAG,CAAC;IACZ,QAAQ,GAAG,CAAC,UAAU;IAEtB,IAAI,WAAW;IAEf,IAAI,WAAW,QAAQ,CAAC,QAAQ,WAAW,QAAQ,CAAC,OAAO;QACzD,MAAM,QAAQ,WAAW,KAAK,CAAC;QAC/B,WAAW,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE;IACpC;IAEA,QAAQ,GAAG,CAAC,WAAW;IACvB,QAAQ,GAAG,CAAC,iBAAiB,GAAG,6DAAmC,wBAAwB,sBAAsB,EAAE,UAAU;IAE7H,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1028, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/views/apps/user/jobs/ApplicantDetailsModal.jsx"], "sourcesContent": ["'use client'\r\n\r\n// React Imports\r\nimport { useState } from 'react'\r\n\r\n// MUI Imports\r\nimport Dialog from '@mui/material/Dialog'\r\nimport DialogTitle from '@mui/material/DialogTitle'\r\nimport DialogContent from '@mui/material/DialogContent'\r\nimport Button from '@mui/material/Button'\r\nimport Typography from '@mui/material/Typography'\r\nimport Grid from '@mui/material/Grid2'\r\nimport Card from '@mui/material/Card'\r\nimport CardContent from '@mui/material/CardContent'\r\nimport Avatar from '@mui/material/Avatar'\r\nimport Divider from '@mui/material/Divider'\r\nimport IconButton from '@mui/material/IconButton'\r\n\r\n// API Imports\r\nimport { downloadResume } from '@/services/jobApi'\r\n\r\nconst ApplicantDetailsModal = ({ open, onClose, applicantData }) => {\r\n  if (!applicantData) return null\r\n\r\n  // Date formatting function - consistent with other sections\r\n  const formatDateTime = (dateString) => {\r\n    if (!dateString) return 'Not provided'\r\n\r\n    try {\r\n      // Handle both ISO string and already formatted dates\r\n      const date = new Date(dateString)\r\n      if (isNaN(date.getTime())) return dateString // Return original if invalid date\r\n\r\n      // Format: MM/DD/YYYY HH:MM AM/PM (using actual submission time from database)\r\n      const dateOptions = {\r\n        month: '2-digit',\r\n        day: '2-digit',\r\n        year: 'numeric'\r\n      }\r\n      const timeOptions = {\r\n        hour: '2-digit',\r\n        minute: '2-digit',\r\n        hour12: true\r\n      }\r\n\r\n      const formattedDate = date.toLocaleDateString('en-US', dateOptions)\r\n      const formattedTime = date.toLocaleTimeString('en-US', timeOptions)\r\n\r\n      return `${formattedDate} ${formattedTime}`\r\n    } catch (error) {\r\n      return dateString // Return original if formatting fails\r\n    }\r\n  }\r\n\r\n  // Date only formatting function (for Date of Birth)\r\n  const formatDateOnly = (dateString) => {\r\n    if (!dateString) return 'Not provided'\r\n\r\n    try {\r\n      const date = new Date(dateString)\r\n      if (isNaN(date.getTime())) return dateString\r\n\r\n      // Format: MM/DD/YYYY (for Date of Birth, we don't need time)\r\n      const dateOptions = {\r\n        month: '2-digit',\r\n        day: '2-digit',\r\n        year: 'numeric'\r\n      }\r\n\r\n      return date.toLocaleDateString('en-US', dateOptions)\r\n    } catch (error) {\r\n      return dateString\r\n    }\r\n  }\r\n\r\n  const handleDownloadResume = async () => {\r\n    try {\r\n      if (!applicantData.resume) {\r\n        alert('No resume file found for this applicant.')\r\n        return\r\n      }\r\n\r\n      console.log('Downloading resume for:', applicantData.fullName)\r\n      console.log('Resume path:', applicantData.resume)\r\n\r\n      await downloadResume(applicantData.resume, applicantData.fullName)\r\n      alert('Resume downloaded successfully!')\r\n    } catch (error) {\r\n      console.error('Error downloading resume:', error)\r\n\r\n      let errorMessage = 'Failed to download resume. '\r\n      if (error.message.includes('404') || error.message.includes('not found')) {\r\n        errorMessage += 'The resume file was not found on the server.'\r\n      } else if (error.message.includes('network')) {\r\n        errorMessage += 'Please check your internet connection.'\r\n      } else {\r\n        errorMessage += 'Please try again or contact support.'\r\n      }\r\n\r\n      alert(errorMessage)\r\n    }\r\n  }\r\n\r\n  return (\r\n    <Dialog open={open} onClose={onClose} maxWidth=\"lg\" fullWidth>\r\n      <DialogTitle className=\"flex items-center justify-between\">\r\n        <Typography variant=\"h4\" style={{ fontSize: '1.8rem' }}>Applicant Details</Typography>\r\n        <IconButton onClick={onClose}>\r\n          <i className=\"tabler-x\" />\r\n        </IconButton>\r\n      </DialogTitle>\r\n\r\n      <DialogContent>\r\n        <Grid container spacing={6}>\r\n          {/* Applicant Profile Section */}\r\n          <Grid size={{ xs: 12 }}>\r\n            <Card>\r\n              <CardContent>\r\n                <div className=\"flex items-center gap-6 mb-6\">\r\n                  <Avatar\r\n                    sx={{ width: 120, height: 120, fontSize: '3.5rem', fontWeight: 'bold' }}\r\n                  >\r\n                    {applicantData.fullName?.charAt(0)?.toUpperCase()}\r\n                  </Avatar>\r\n                  <div>\r\n                    <Typography variant=\"h3\" className=\"mb-3 font-bold\" style={{ fontSize: '2.2rem' }}>\r\n                      {applicantData.fullName}\r\n                    </Typography>\r\n                    <Typography variant=\"h5\" color=\"text.secondary\" className=\"font-medium\" style={{ fontSize: '1.4rem' }}>\r\n                      {applicantData.email}\r\n                    </Typography>\r\n                  </div>\r\n                </div>\r\n\r\n                <Divider className=\"mb-6\" />\r\n\r\n                {/* Personal Information */}\r\n                <Typography variant=\"h5\" className=\"mb-4 font-bold\" style={{ fontSize: '1.6rem' }}>\r\n                  Personal Information\r\n                </Typography>\r\n                <Grid container spacing={4}>\r\n                  <Grid size={{ xs: 12, sm: 6 }}>\r\n                    <Typography variant=\"h6\" color=\"text.secondary\" className=\"mb-2 font-medium\" style={{ fontSize: '1.1rem' }}>\r\n                      First Name\r\n                    </Typography>\r\n                    <Typography variant=\"h6\" className=\"font-bold\" style={{ fontSize: '1.3rem' }}>\r\n                      {applicantData.first_name || applicantData.fullName?.split(' ')[0] || 'N/A'}\r\n                    </Typography>\r\n                  </Grid>\r\n\r\n                  <Grid size={{ xs: 12, sm: 6 }}>\r\n                    <Typography variant=\"h6\" color=\"text.secondary\" className=\"mb-2 font-medium\" style={{ fontSize: '1.1rem' }}>\r\n                      Last Name\r\n                    </Typography>\r\n                    <Typography variant=\"h6\" className=\"font-bold\" style={{ fontSize: '1.3rem' }}>\r\n                      {applicantData.last_name || applicantData.fullName?.split(' ')[1] || 'N/A'}\r\n                    </Typography>\r\n                  </Grid>\r\n\r\n                  <Grid size={{ xs: 12, sm: 6 }}>\r\n                    <Typography variant=\"h6\" color=\"text.secondary\" className=\"mb-2 font-medium\" style={{ fontSize: '1.1rem' }}>\r\n                      Phone Number\r\n                    </Typography>\r\n                    <Typography variant=\"h6\" className=\"font-bold\" style={{ fontSize: '1.3rem' }}>\r\n                      {applicantData.phone_number || applicantData.phone || '+****************'}\r\n                    </Typography>\r\n                  </Grid>\r\n\r\n                  <Grid size={{ xs: 12, sm: 6 }}>\r\n                    <Typography variant=\"h6\" color=\"text.secondary\" className=\"mb-2 font-medium\" style={{ fontSize: '1.1rem' }}>\r\n                      Date of Birth\r\n                    </Typography>\r\n                    <Typography variant=\"h6\" className=\"font-bold\" style={{ fontSize: '1.3rem' }}>\r\n                      {formatDateOnly(applicantData.dob)}\r\n                    </Typography>\r\n                  </Grid>\r\n\r\n                  <Grid size={{ xs: 12 }}>\r\n                    <Typography variant=\"h6\" color=\"text.secondary\" className=\"mb-2 font-medium\" style={{ fontSize: '1.1rem' }}>\r\n                      Address\r\n                    </Typography>\r\n                    <Typography variant=\"h6\" className=\"font-bold\" style={{ fontSize: '1.3rem' }}>\r\n                      {applicantData.address || 'Not provided'}\r\n                    </Typography>\r\n                  </Grid>\r\n                </Grid>\r\n              </CardContent>\r\n            </Card>\r\n          </Grid>\r\n\r\n          {/* Job Information */}\r\n          <Grid size={{ xs: 12 }}>\r\n            <Card>\r\n              <CardContent>\r\n                <Typography variant=\"h5\" className=\"mb-4 font-bold\" style={{ fontSize: '1.6rem' }}>\r\n                  Job Information\r\n                </Typography>\r\n                <Grid container spacing={4}>\r\n                  <Grid size={{ xs: 12, sm: 6 }}>\r\n                    <Typography variant=\"h6\" color=\"text.secondary\" className=\"mb-2 font-medium\" style={{ fontSize: '1.1rem' }}>\r\n                      Position\r\n                    </Typography>\r\n                    <Typography variant=\"h6\" className=\"font-bold\" style={{ fontSize: '1.3rem' }}>\r\n                      {applicantData.position || 'Software Developer'}\r\n                    </Typography>\r\n                  </Grid>\r\n\r\n                  <Grid size={{ xs: 12, sm: 6 }}>\r\n                    <Typography variant=\"h6\" color=\"text.secondary\" className=\"mb-2 font-medium\" style={{ fontSize: '1.1rem' }}>\r\n                      Employment Type\r\n                    </Typography>\r\n                    <Typography variant=\"h6\" className=\"font-bold\" style={{ fontSize: '1.3rem' }}>\r\n                      {applicantData.employment_type || applicantData.employmentType || 'Full-time'}\r\n                    </Typography>\r\n                  </Grid>\r\n\r\n                  <Grid size={{ xs: 12, sm: 6 }}>\r\n                    <Typography variant=\"h6\" color=\"text.secondary\" className=\"mb-2 font-medium\" style={{ fontSize: '1.1rem' }}>\r\n                      Specific Driving Role\r\n                    </Typography>\r\n                    <Typography variant=\"h6\" className=\"font-bold\" style={{ fontSize: '1.3rem' }}>\r\n                      {applicantData.specific_driving_role || 'Not specified'}\r\n                    </Typography>\r\n                  </Grid>\r\n\r\n                  <Grid size={{ xs: 12, sm: 6 }}>\r\n                    <Typography variant=\"h6\" color=\"text.secondary\" className=\"mb-2 font-medium\" style={{ fontSize: '1.1rem' }}>\r\n                      Specific Non-Driving Role\r\n                    </Typography>\r\n                    <Typography variant=\"h6\" className=\"font-bold\" style={{ fontSize: '1.3rem' }}>\r\n                      {applicantData.specific_non_driving_role || 'Not specified'}\r\n                    </Typography>\r\n                  </Grid>\r\n\r\n                  <Grid size={{ xs: 12, sm: 6 }}>\r\n                    <Typography variant=\"h6\" color=\"text.secondary\" className=\"mb-2 font-medium\" style={{ fontSize: '1.1rem' }}>\r\n                      Commercial License\r\n                    </Typography>\r\n                    <Typography variant=\"h6\" className=\"font-bold\" style={{ fontSize: '1.3rem' }}>\r\n                      {applicantData.commercial_license || 'Not specified'}\r\n                    </Typography>\r\n                  </Grid>\r\n\r\n                  <Grid size={{ xs: 12, sm: 6 }}>\r\n                    <Typography variant=\"h6\" color=\"text.secondary\" className=\"mb-2 font-medium\" style={{ fontSize: '1.1rem' }}>\r\n                      Other Job\r\n                    </Typography>\r\n                    <Typography variant=\"h6\" className=\"font-bold\" style={{ fontSize: '1.3rem' }}>\r\n                      {applicantData.other_job || 'None'}\r\n                    </Typography>\r\n                  </Grid>\r\n\r\n                  <Grid size={{ xs: 12, sm: 6 }}>\r\n                    <Typography variant=\"h6\" color=\"text.secondary\" className=\"mb-2 font-medium\" style={{ fontSize: '1.1rem' }}>\r\n                      Preferred Start Date\r\n                    </Typography>\r\n                    <Typography variant=\"h6\" className=\"font-bold\" style={{ fontSize: '1.3rem' }}>\r\n                      {applicantData.preferred_start_date && applicantData.preferred_start_date !== 'Flexible'\r\n                        ? formatDateOnly(applicantData.preferred_start_date)\r\n                        : (applicantData.preferred_start_date || 'Flexible')}\r\n                    </Typography>\r\n                  </Grid>\r\n\r\n                  <Grid size={{ xs: 12, sm: 6 }}>\r\n                    <Typography variant=\"h6\" color=\"text.secondary\" className=\"mb-2 font-medium\" style={{ fontSize: '1.1rem' }}>\r\n                      Willing to Relocate\r\n                    </Typography>\r\n                    <Typography variant=\"h6\" className=\"font-bold\" style={{ fontSize: '1.3rem' }}>\r\n                      {applicantData.relocate || 'Not specified'}\r\n                    </Typography>\r\n                  </Grid>\r\n\r\n                  <Grid size={{ xs: 12, sm: 6 }}>\r\n                    <Typography variant=\"h6\" color=\"text.secondary\" className=\"mb-2 font-medium\" style={{ fontSize: '1.1rem' }}>\r\n                      Experience\r\n                    </Typography>\r\n                    <Typography variant=\"h6\" className=\"font-bold\" style={{ fontSize: '1.3rem' }}>\r\n                      {applicantData.experience || '2-3 years'}\r\n                    </Typography>\r\n                  </Grid>\r\n\r\n                  <Grid size={{ xs: 12, sm: 6 }}>\r\n                    <Typography variant=\"h6\" color=\"text.secondary\" className=\"mb-2 font-medium\" style={{ fontSize: '1.1rem' }}>\r\n                      Resume\r\n                    </Typography>\r\n                    <div className=\"flex items-center gap-3\">\r\n                      <Typography variant=\"h6\" className=\"font-bold\" style={{ fontSize: '1.3rem' }}>\r\n                        {applicantData.resume ? 'Available' : 'Not uploaded'}\r\n                      </Typography>\r\n                      {applicantData.resume && (\r\n                        <Button\r\n                          variant=\"outlined\"\r\n                          size=\"small\"\r\n                          onClick={handleDownloadResume}\r\n                          startIcon={<i className=\"tabler-download\" />}\r\n                          sx={{\r\n                            fontSize: '0.75rem',\r\n                            padding: '4px 12px',\r\n                            '&:hover': {\r\n                              backgroundColor: 'primary.light',\r\n                              transform: 'scale(1.02)'\r\n                            }\r\n                          }}\r\n                        >\r\n                          Download\r\n                        </Button>\r\n                      )}\r\n                    </div>\r\n                  </Grid>\r\n\r\n                  <Grid size={{ xs: 12 }}>\r\n                    <Typography variant=\"h6\" color=\"text.secondary\" className=\"mb-2 font-medium\" style={{ fontSize: '1.1rem' }}>\r\n                      Work Reason\r\n                    </Typography>\r\n                    <Typography variant=\"h6\" className=\"font-bold\" style={{ fontSize: '1.3rem', lineHeight: '1.4' }}>\r\n                      {applicantData.work_reason || 'Looking for new opportunities and career growth.'}\r\n                    </Typography>\r\n                  </Grid>\r\n\r\n                  <Grid size={{ xs: 12, sm: 6 }}>\r\n                    <Typography variant=\"h6\" color=\"text.secondary\" className=\"mb-2 font-medium\" style={{ fontSize: '1.1rem' }}>\r\n                      Reference\r\n                    </Typography>\r\n                    <Typography variant=\"h6\" className=\"font-bold\" style={{ fontSize: '1.3rem' }}>\r\n                      {applicantData.reference || 'Available upon request'}\r\n                    </Typography>\r\n                  </Grid>\r\n\r\n                  <Grid size={{ xs: 12, sm: 6 }}>\r\n                    <Typography variant=\"h6\" color=\"text.secondary\" className=\"mb-2 font-medium\" style={{ fontSize: '1.1rem' }}>\r\n                      Other Reference\r\n                    </Typography>\r\n                    <Typography variant=\"h6\" className=\"font-bold\" style={{ fontSize: '1.3rem' }}>\r\n                      {applicantData.other_reference || 'None provided'}\r\n                    </Typography>\r\n                  </Grid>\r\n                </Grid>\r\n              </CardContent>\r\n            </Card>\r\n          </Grid>\r\n        </Grid>\r\n      </DialogContent>\r\n    </Dialog>\r\n  )\r\n}\r\n\r\nexport default ApplicantDetailsModal\r\n"], "names": [], "mappings": ";;;;AAEA,gBAAgB;AAChB;AAEA,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA,cAAc;AACd;AAnBA;;;;;;;;;;;;;;;AAqBA,MAAM,wBAAwB,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,aAAa,EAAE;IAC7D,IAAI,CAAC,eAAe,OAAO;IAE3B,4DAA4D;IAC5D,MAAM,iBAAiB,CAAC;QACtB,IAAI,CAAC,YAAY,OAAO;QAExB,IAAI;YACF,qDAAqD;YACrD,MAAM,OAAO,IAAI,KAAK;YACtB,IAAI,MAAM,KAAK,OAAO,KAAK,OAAO,WAAW,kCAAkC;;YAE/E,8EAA8E;YAC9E,MAAM,cAAc;gBAClB,OAAO;gBACP,KAAK;gBACL,MAAM;YACR;YACA,MAAM,cAAc;gBAClB,MAAM;gBACN,QAAQ;gBACR,QAAQ;YACV;YAEA,MAAM,gBAAgB,KAAK,kBAAkB,CAAC,SAAS;YACvD,MAAM,gBAAgB,KAAK,kBAAkB,CAAC,SAAS;YAEvD,OAAO,GAAG,cAAc,CAAC,EAAE,eAAe;QAC5C,EAAE,OAAO,OAAO;YACd,OAAO,WAAW,sCAAsC;;QAC1D;IACF;IAEA,oDAAoD;IACpD,MAAM,iBAAiB,CAAC;QACtB,IAAI,CAAC,YAAY,OAAO;QAExB,IAAI;YACF,MAAM,OAAO,IAAI,KAAK;YACtB,IAAI,MAAM,KAAK,OAAO,KAAK,OAAO;YAElC,6DAA6D;YAC7D,MAAM,cAAc;gBAClB,OAAO;gBACP,KAAK;gBACL,MAAM;YACR;YAEA,OAAO,KAAK,kBAAkB,CAAC,SAAS;QAC1C,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;IAEA,MAAM,uBAAuB;QAC3B,IAAI;YACF,IAAI,CAAC,cAAc,MAAM,EAAE;gBACzB,MAAM;gBACN;YACF;YAEA,QAAQ,GAAG,CAAC,2BAA2B,cAAc,QAAQ;YAC7D,QAAQ,GAAG,CAAC,gBAAgB,cAAc,MAAM;YAEhD,MAAM,CAAA,GAAA,4HAAA,CAAA,iBAAc,AAAD,EAAE,cAAc,MAAM,EAAE,cAAc,QAAQ;YACjE,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAE3C,IAAI,eAAe;YACnB,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,UAAU,MAAM,OAAO,CAAC,QAAQ,CAAC,cAAc;gBACxE,gBAAgB;YAClB,OAAO,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,YAAY;gBAC5C,gBAAgB;YAClB,OAAO;gBACL,gBAAgB;YAClB;YAEA,MAAM;QACR;IACF;IAEA,qBACE,6LAAC,wJAAA,CAAA,UAAM;QAAC,MAAM;QAAM,SAAS;QAAS,UAAS;QAAK,SAAS;;0BAC3D,6LAAC,kKAAA,CAAA,UAAW;gBAAC,WAAU;;kCACrB,6LAAC,gKAAA,CAAA,UAAU;wBAAC,SAAQ;wBAAK,OAAO;4BAAE,UAAU;wBAAS;kCAAG;;;;;;kCACxD,6LAAC,gKAAA,CAAA,UAAU;wBAAC,SAAS;kCACnB,cAAA,6LAAC;4BAAE,WAAU;;;;;;;;;;;;;;;;;0BAIjB,6LAAC,sKAAA,CAAA,UAAa;0BACZ,cAAA,6LAAC,sJAAA,CAAA,UAAI;oBAAC,SAAS;oBAAC,SAAS;;sCAEvB,6LAAC,sJAAA,CAAA,UAAI;4BAAC,MAAM;gCAAE,IAAI;4BAAG;sCACnB,cAAA,6LAAC,oJAAA,CAAA,UAAI;0CACH,cAAA,6LAAC,kKAAA,CAAA,UAAW;;sDACV,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,wJAAA,CAAA,UAAM;oDACL,IAAI;wDAAE,OAAO;wDAAK,QAAQ;wDAAK,UAAU;wDAAU,YAAY;oDAAO;8DAErE,cAAc,QAAQ,EAAE,OAAO,IAAI;;;;;;8DAEtC,6LAAC;;sEACC,6LAAC,gKAAA,CAAA,UAAU;4DAAC,SAAQ;4DAAK,WAAU;4DAAiB,OAAO;gEAAE,UAAU;4DAAS;sEAC7E,cAAc,QAAQ;;;;;;sEAEzB,6LAAC,gKAAA,CAAA,UAAU;4DAAC,SAAQ;4DAAK,OAAM;4DAAiB,WAAU;4DAAc,OAAO;gEAAE,UAAU;4DAAS;sEACjG,cAAc,KAAK;;;;;;;;;;;;;;;;;;sDAK1B,6LAAC,0JAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;sDAGnB,6LAAC,gKAAA,CAAA,UAAU;4CAAC,SAAQ;4CAAK,WAAU;4CAAiB,OAAO;gDAAE,UAAU;4CAAS;sDAAG;;;;;;sDAGnF,6LAAC,sJAAA,CAAA,UAAI;4CAAC,SAAS;4CAAC,SAAS;;8DACvB,6LAAC,sJAAA,CAAA,UAAI;oDAAC,MAAM;wDAAE,IAAI;wDAAI,IAAI;oDAAE;;sEAC1B,6LAAC,gKAAA,CAAA,UAAU;4DAAC,SAAQ;4DAAK,OAAM;4DAAiB,WAAU;4DAAmB,OAAO;gEAAE,UAAU;4DAAS;sEAAG;;;;;;sEAG5G,6LAAC,gKAAA,CAAA,UAAU;4DAAC,SAAQ;4DAAK,WAAU;4DAAY,OAAO;gEAAE,UAAU;4DAAS;sEACxE,cAAc,UAAU,IAAI,cAAc,QAAQ,EAAE,MAAM,IAAI,CAAC,EAAE,IAAI;;;;;;;;;;;;8DAI1E,6LAAC,sJAAA,CAAA,UAAI;oDAAC,MAAM;wDAAE,IAAI;wDAAI,IAAI;oDAAE;;sEAC1B,6LAAC,gKAAA,CAAA,UAAU;4DAAC,SAAQ;4DAAK,OAAM;4DAAiB,WAAU;4DAAmB,OAAO;gEAAE,UAAU;4DAAS;sEAAG;;;;;;sEAG5G,6LAAC,gKAAA,CAAA,UAAU;4DAAC,SAAQ;4DAAK,WAAU;4DAAY,OAAO;gEAAE,UAAU;4DAAS;sEACxE,cAAc,SAAS,IAAI,cAAc,QAAQ,EAAE,MAAM,IAAI,CAAC,EAAE,IAAI;;;;;;;;;;;;8DAIzE,6LAAC,sJAAA,CAAA,UAAI;oDAAC,MAAM;wDAAE,IAAI;wDAAI,IAAI;oDAAE;;sEAC1B,6LAAC,gKAAA,CAAA,UAAU;4DAAC,SAAQ;4DAAK,OAAM;4DAAiB,WAAU;4DAAmB,OAAO;gEAAE,UAAU;4DAAS;sEAAG;;;;;;sEAG5G,6LAAC,gKAAA,CAAA,UAAU;4DAAC,SAAQ;4DAAK,WAAU;4DAAY,OAAO;gEAAE,UAAU;4DAAS;sEACxE,cAAc,YAAY,IAAI,cAAc,KAAK,IAAI;;;;;;;;;;;;8DAI1D,6LAAC,sJAAA,CAAA,UAAI;oDAAC,MAAM;wDAAE,IAAI;wDAAI,IAAI;oDAAE;;sEAC1B,6LAAC,gKAAA,CAAA,UAAU;4DAAC,SAAQ;4DAAK,OAAM;4DAAiB,WAAU;4DAAmB,OAAO;gEAAE,UAAU;4DAAS;sEAAG;;;;;;sEAG5G,6LAAC,gKAAA,CAAA,UAAU;4DAAC,SAAQ;4DAAK,WAAU;4DAAY,OAAO;gEAAE,UAAU;4DAAS;sEACxE,eAAe,cAAc,GAAG;;;;;;;;;;;;8DAIrC,6LAAC,sJAAA,CAAA,UAAI;oDAAC,MAAM;wDAAE,IAAI;oDAAG;;sEACnB,6LAAC,gKAAA,CAAA,UAAU;4DAAC,SAAQ;4DAAK,OAAM;4DAAiB,WAAU;4DAAmB,OAAO;gEAAE,UAAU;4DAAS;sEAAG;;;;;;sEAG5G,6LAAC,gKAAA,CAAA,UAAU;4DAAC,SAAQ;4DAAK,WAAU;4DAAY,OAAO;gEAAE,UAAU;4DAAS;sEACxE,cAAc,OAAO,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAStC,6LAAC,sJAAA,CAAA,UAAI;4BAAC,MAAM;gCAAE,IAAI;4BAAG;sCACnB,cAAA,6LAAC,oJAAA,CAAA,UAAI;0CACH,cAAA,6LAAC,kKAAA,CAAA,UAAW;;sDACV,6LAAC,gKAAA,CAAA,UAAU;4CAAC,SAAQ;4CAAK,WAAU;4CAAiB,OAAO;gDAAE,UAAU;4CAAS;sDAAG;;;;;;sDAGnF,6LAAC,sJAAA,CAAA,UAAI;4CAAC,SAAS;4CAAC,SAAS;;8DACvB,6LAAC,sJAAA,CAAA,UAAI;oDAAC,MAAM;wDAAE,IAAI;wDAAI,IAAI;oDAAE;;sEAC1B,6LAAC,gKAAA,CAAA,UAAU;4DAAC,SAAQ;4DAAK,OAAM;4DAAiB,WAAU;4DAAmB,OAAO;gEAAE,UAAU;4DAAS;sEAAG;;;;;;sEAG5G,6LAAC,gKAAA,CAAA,UAAU;4DAAC,SAAQ;4DAAK,WAAU;4DAAY,OAAO;gEAAE,UAAU;4DAAS;sEACxE,cAAc,QAAQ,IAAI;;;;;;;;;;;;8DAI/B,6LAAC,sJAAA,CAAA,UAAI;oDAAC,MAAM;wDAAE,IAAI;wDAAI,IAAI;oDAAE;;sEAC1B,6LAAC,gKAAA,CAAA,UAAU;4DAAC,SAAQ;4DAAK,OAAM;4DAAiB,WAAU;4DAAmB,OAAO;gEAAE,UAAU;4DAAS;sEAAG;;;;;;sEAG5G,6LAAC,gKAAA,CAAA,UAAU;4DAAC,SAAQ;4DAAK,WAAU;4DAAY,OAAO;gEAAE,UAAU;4DAAS;sEACxE,cAAc,eAAe,IAAI,cAAc,cAAc,IAAI;;;;;;;;;;;;8DAItE,6LAAC,sJAAA,CAAA,UAAI;oDAAC,MAAM;wDAAE,IAAI;wDAAI,IAAI;oDAAE;;sEAC1B,6LAAC,gKAAA,CAAA,UAAU;4DAAC,SAAQ;4DAAK,OAAM;4DAAiB,WAAU;4DAAmB,OAAO;gEAAE,UAAU;4DAAS;sEAAG;;;;;;sEAG5G,6LAAC,gKAAA,CAAA,UAAU;4DAAC,SAAQ;4DAAK,WAAU;4DAAY,OAAO;gEAAE,UAAU;4DAAS;sEACxE,cAAc,qBAAqB,IAAI;;;;;;;;;;;;8DAI5C,6LAAC,sJAAA,CAAA,UAAI;oDAAC,MAAM;wDAAE,IAAI;wDAAI,IAAI;oDAAE;;sEAC1B,6LAAC,gKAAA,CAAA,UAAU;4DAAC,SAAQ;4DAAK,OAAM;4DAAiB,WAAU;4DAAmB,OAAO;gEAAE,UAAU;4DAAS;sEAAG;;;;;;sEAG5G,6LAAC,gKAAA,CAAA,UAAU;4DAAC,SAAQ;4DAAK,WAAU;4DAAY,OAAO;gEAAE,UAAU;4DAAS;sEACxE,cAAc,yBAAyB,IAAI;;;;;;;;;;;;8DAIhD,6LAAC,sJAAA,CAAA,UAAI;oDAAC,MAAM;wDAAE,IAAI;wDAAI,IAAI;oDAAE;;sEAC1B,6LAAC,gKAAA,CAAA,UAAU;4DAAC,SAAQ;4DAAK,OAAM;4DAAiB,WAAU;4DAAmB,OAAO;gEAAE,UAAU;4DAAS;sEAAG;;;;;;sEAG5G,6LAAC,gKAAA,CAAA,UAAU;4DAAC,SAAQ;4DAAK,WAAU;4DAAY,OAAO;gEAAE,UAAU;4DAAS;sEACxE,cAAc,kBAAkB,IAAI;;;;;;;;;;;;8DAIzC,6LAAC,sJAAA,CAAA,UAAI;oDAAC,MAAM;wDAAE,IAAI;wDAAI,IAAI;oDAAE;;sEAC1B,6LAAC,gKAAA,CAAA,UAAU;4DAAC,SAAQ;4DAAK,OAAM;4DAAiB,WAAU;4DAAmB,OAAO;gEAAE,UAAU;4DAAS;sEAAG;;;;;;sEAG5G,6LAAC,gKAAA,CAAA,UAAU;4DAAC,SAAQ;4DAAK,WAAU;4DAAY,OAAO;gEAAE,UAAU;4DAAS;sEACxE,cAAc,SAAS,IAAI;;;;;;;;;;;;8DAIhC,6LAAC,sJAAA,CAAA,UAAI;oDAAC,MAAM;wDAAE,IAAI;wDAAI,IAAI;oDAAE;;sEAC1B,6LAAC,gKAAA,CAAA,UAAU;4DAAC,SAAQ;4DAAK,OAAM;4DAAiB,WAAU;4DAAmB,OAAO;gEAAE,UAAU;4DAAS;sEAAG;;;;;;sEAG5G,6LAAC,gKAAA,CAAA,UAAU;4DAAC,SAAQ;4DAAK,WAAU;4DAAY,OAAO;gEAAE,UAAU;4DAAS;sEACxE,cAAc,oBAAoB,IAAI,cAAc,oBAAoB,KAAK,aAC1E,eAAe,cAAc,oBAAoB,IAChD,cAAc,oBAAoB,IAAI;;;;;;;;;;;;8DAI/C,6LAAC,sJAAA,CAAA,UAAI;oDAAC,MAAM;wDAAE,IAAI;wDAAI,IAAI;oDAAE;;sEAC1B,6LAAC,gKAAA,CAAA,UAAU;4DAAC,SAAQ;4DAAK,OAAM;4DAAiB,WAAU;4DAAmB,OAAO;gEAAE,UAAU;4DAAS;sEAAG;;;;;;sEAG5G,6LAAC,gKAAA,CAAA,UAAU;4DAAC,SAAQ;4DAAK,WAAU;4DAAY,OAAO;gEAAE,UAAU;4DAAS;sEACxE,cAAc,QAAQ,IAAI;;;;;;;;;;;;8DAI/B,6LAAC,sJAAA,CAAA,UAAI;oDAAC,MAAM;wDAAE,IAAI;wDAAI,IAAI;oDAAE;;sEAC1B,6LAAC,gKAAA,CAAA,UAAU;4DAAC,SAAQ;4DAAK,OAAM;4DAAiB,WAAU;4DAAmB,OAAO;gEAAE,UAAU;4DAAS;sEAAG;;;;;;sEAG5G,6LAAC,gKAAA,CAAA,UAAU;4DAAC,SAAQ;4DAAK,WAAU;4DAAY,OAAO;gEAAE,UAAU;4DAAS;sEACxE,cAAc,UAAU,IAAI;;;;;;;;;;;;8DAIjC,6LAAC,sJAAA,CAAA,UAAI;oDAAC,MAAM;wDAAE,IAAI;wDAAI,IAAI;oDAAE;;sEAC1B,6LAAC,gKAAA,CAAA,UAAU;4DAAC,SAAQ;4DAAK,OAAM;4DAAiB,WAAU;4DAAmB,OAAO;gEAAE,UAAU;4DAAS;sEAAG;;;;;;sEAG5G,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,gKAAA,CAAA,UAAU;oEAAC,SAAQ;oEAAK,WAAU;oEAAY,OAAO;wEAAE,UAAU;oEAAS;8EACxE,cAAc,MAAM,GAAG,cAAc;;;;;;gEAEvC,cAAc,MAAM,kBACnB,6LAAC,wJAAA,CAAA,UAAM;oEACL,SAAQ;oEACR,MAAK;oEACL,SAAS;oEACT,yBAAW,6LAAC;wEAAE,WAAU;;;;;;oEACxB,IAAI;wEACF,UAAU;wEACV,SAAS;wEACT,WAAW;4EACT,iBAAiB;4EACjB,WAAW;wEACb;oEACF;8EACD;;;;;;;;;;;;;;;;;;8DAOP,6LAAC,sJAAA,CAAA,UAAI;oDAAC,MAAM;wDAAE,IAAI;oDAAG;;sEACnB,6LAAC,gKAAA,CAAA,UAAU;4DAAC,SAAQ;4DAAK,OAAM;4DAAiB,WAAU;4DAAmB,OAAO;gEAAE,UAAU;4DAAS;sEAAG;;;;;;sEAG5G,6LAAC,gKAAA,CAAA,UAAU;4DAAC,SAAQ;4DAAK,WAAU;4DAAY,OAAO;gEAAE,UAAU;gEAAU,YAAY;4DAAM;sEAC3F,cAAc,WAAW,IAAI;;;;;;;;;;;;8DAIlC,6LAAC,sJAAA,CAAA,UAAI;oDAAC,MAAM;wDAAE,IAAI;wDAAI,IAAI;oDAAE;;sEAC1B,6LAAC,gKAAA,CAAA,UAAU;4DAAC,SAAQ;4DAAK,OAAM;4DAAiB,WAAU;4DAAmB,OAAO;gEAAE,UAAU;4DAAS;sEAAG;;;;;;sEAG5G,6LAAC,gKAAA,CAAA,UAAU;4DAAC,SAAQ;4DAAK,WAAU;4DAAY,OAAO;gEAAE,UAAU;4DAAS;sEACxE,cAAc,SAAS,IAAI;;;;;;;;;;;;8DAIhC,6LAAC,sJAAA,CAAA,UAAI;oDAAC,MAAM;wDAAE,IAAI;wDAAI,IAAI;oDAAE;;sEAC1B,6LAAC,gKAAA,CAAA,UAAU;4DAAC,SAAQ;4DAAK,OAAM;4DAAiB,WAAU;4DAAmB,OAAO;gEAAE,UAAU;4DAAS;sEAAG;;;;;;sEAG5G,6LAAC,gKAAA,CAAA,UAAU;4DAAC,SAAQ;4DAAK,WAAU;4DAAY,OAAO;gEAAE,UAAU;4DAAS;sEACxE,cAAc,eAAe,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWxD;KAnUM;uCAqUS", "debugId": null}}, {"offset": {"line": 2059, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/views/apps/user/jobs/ResumeViewerModal.jsx"], "sourcesContent": ["'use client'\r\n\r\n// React Imports\r\nimport { useState } from 'react'\r\n\r\n// MUI Imports\r\nimport Dialog from '@mui/material/Dialog'\r\nimport DialogTitle from '@mui/material/DialogTitle'\r\nimport DialogContent from '@mui/material/DialogContent'\r\nimport Button from '@mui/material/Button'\r\nimport Typography from '@mui/material/Typography'\r\nimport IconButton from '@mui/material/IconButton'\r\nimport Box from '@mui/material/Box'\r\n\r\nconst ResumeViewerModal = ({ open, onClose, applicantData }) => {\r\n  const handleDownloadResume = () => {\r\n    // Create a sample PDF download functionality\r\n    const element = document.createElement('a')\r\n    const today = new Date()\r\n    const formattedDate = `${today.getMonth() + 1}/${today.getDate()}/${today.getFullYear()}`\r\n    const file = new Blob([`Resume for ${applicantData?.fullName}\\n\\nPersonal Information:\\nName: ${applicantData?.fullName}\\nEmail: ${applicantData?.email}\\nPhone: ${applicantData?.phone_number || applicantData?.phone || 'N/A'}\\n\\nPosition Applied: ${applicantData?.position || 'Software Developer'}\\nExperience: ${applicantData?.experience || '2-3 years'}\\nEmployment Type: ${applicantData?.employment_type || applicantData?.employmentType || 'Full-time'}\\n\\nGenerated on: ${formattedDate}`], { type: 'text/plain' })\r\n    element.href = URL.createObjectURL(file)\r\n    element.download = `${applicantData?.fullName || 'applicant'}_resume.txt`\r\n    document.body.appendChild(element)\r\n    element.click()\r\n    document.body.removeChild(element)\r\n  }\r\n\r\n  if (!applicantData) return null\r\n\r\n  return (\r\n    <Dialog open={open} onClose={onClose} maxWidth=\"md\" fullWidth>\r\n      <DialogTitle className=\"flex items-center justify-between\">\r\n        <Typography variant=\"h5\" style={{ fontSize: '1.5rem' }}>\r\n          Resume - {applicantData.fullName}\r\n        </Typography>\r\n        <div className=\"flex items-center gap-2\">\r\n          <Button\r\n            variant=\"outlined\"\r\n            color=\"primary\"\r\n            size=\"large\"\r\n            startIcon={<i className=\"tabler-download\" />}\r\n            onClick={handleDownloadResume}\r\n            className=\"font-bold\"\r\n            sx={{ fontSize: '1rem', padding: '8px 16px' }}\r\n          >\r\n            Download Resume\r\n          </Button>\r\n          <IconButton onClick={onClose}>\r\n            <i className=\"tabler-x\" />\r\n          </IconButton>\r\n        </div>\r\n      </DialogTitle>\r\n\r\n      <DialogContent>\r\n        <Box sx={{\r\n          minHeight: '600px',\r\n          border: '1px solid #e0e0e0',\r\n          borderRadius: '8px',\r\n          padding: '20px',\r\n          backgroundColor: '#fafafa'\r\n        }}>\r\n          {/* Resume Content */}\r\n          <div className=\"resume-content\">\r\n            <div className=\"text-center mb-6\">\r\n              <Typography variant=\"h4\" className=\"font-bold mb-2\" style={{ fontSize: '2rem' }}>\r\n                {applicantData.fullName}\r\n              </Typography>\r\n              <Typography variant=\"h6\" color=\"text.secondary\" style={{ fontSize: '1.2rem' }}>\r\n                {applicantData.email} | {applicantData.phone_number || applicantData.phone || '+****************'}\r\n              </Typography>\r\n              <Typography variant=\"body1\" color=\"text.secondary\" style={{ fontSize: '1rem' }}>\r\n                {applicantData.address || 'Address not provided'}\r\n              </Typography>\r\n            </div>\r\n\r\n            <div className=\"mb-6\">\r\n              <Typography variant=\"h5\" className=\"font-bold mb-3\" style={{ fontSize: '1.4rem', borderBottom: '2px solid #1976d2', paddingBottom: '4px' }}>\r\n                OBJECTIVE\r\n              </Typography>\r\n              <Typography variant=\"body1\" style={{ fontSize: '1.1rem', lineHeight: '1.6' }}>\r\n                {applicantData.work_reason || 'Seeking a challenging position where I can utilize my skills and experience to contribute to the company\\'s success while growing professionally.'}\r\n              </Typography>\r\n            </div>\r\n\r\n            <div className=\"mb-6\">\r\n              <Typography variant=\"h5\" className=\"font-bold mb-3\" style={{ fontSize: '1.4rem', borderBottom: '2px solid #1976d2', paddingBottom: '4px' }}>\r\n                EXPERIENCE\r\n              </Typography>\r\n              <Typography variant=\"body1\" style={{ fontSize: '1.1rem', lineHeight: '1.6' }}>\r\n                <strong>Experience Level:</strong> {applicantData.experience || '2-3 years'}<br />\r\n                <strong>Position Applied:</strong> {applicantData.position || 'Software Developer'}<br />\r\n                <strong>Employment Type Preference:</strong> {applicantData.employment_type || applicantData.employmentType || 'Full-time'}\r\n              </Typography>\r\n            </div>\r\n\r\n            <div className=\"mb-6\">\r\n              <Typography variant=\"h5\" className=\"font-bold mb-3\" style={{ fontSize: '1.4rem', borderBottom: '2px solid #1976d2', paddingBottom: '4px' }}>\r\n                SKILLS & QUALIFICATIONS\r\n              </Typography>\r\n              <Typography variant=\"body1\" style={{ fontSize: '1.1rem', lineHeight: '1.6' }}>\r\n                {applicantData.specific_driving_role && (\r\n                  <>\r\n                    <strong>Driving Role:</strong> {applicantData.specific_driving_role}<br />\r\n                  </>\r\n                )}\r\n                {applicantData.specific_non_driving_role && (\r\n                  <>\r\n                    <strong>Non-Driving Role:</strong> {applicantData.specific_non_driving_role}<br />\r\n                  </>\r\n                )}\r\n                {applicantData.commercial_license && (\r\n                  <>\r\n                    <strong>Commercial License:</strong> {applicantData.commercial_license}<br />\r\n                  </>\r\n                )}\r\n                {applicantData.other_job && (\r\n                  <>\r\n                    <strong>Other Experience:</strong> {applicantData.other_job}<br />\r\n                  </>\r\n                )}\r\n              </Typography>\r\n            </div>\r\n\r\n            <div className=\"mb-6\">\r\n              <Typography variant=\"h5\" className=\"font-bold mb-3\" style={{ fontSize: '1.4rem', borderBottom: '2px solid #1976d2', paddingBottom: '4px' }}>\r\n                AVAILABILITY\r\n              </Typography>\r\n              <Typography variant=\"body1\" style={{ fontSize: '1.1rem', lineHeight: '1.6' }}>\r\n                <strong>Preferred Start Date:</strong> {applicantData.preferred_start_date || 'Flexible'}<br />\r\n                <strong>Willing to Relocate:</strong> {applicantData.relocate || 'Not specified'}\r\n              </Typography>\r\n            </div>\r\n\r\n            <div className=\"mb-6\">\r\n              <Typography variant=\"h5\" className=\"font-bold mb-3\" style={{ fontSize: '1.4rem', borderBottom: '2px solid #1976d2', paddingBottom: '4px' }}>\r\n                REFERENCES\r\n              </Typography>\r\n              <Typography variant=\"body1\" style={{ fontSize: '1.1rem', lineHeight: '1.6' }}>\r\n                <strong>Primary Reference:</strong> {applicantData.reference || 'Available upon request'}<br />\r\n                {applicantData.other_reference && (\r\n                  <>\r\n                    <strong>Additional Reference:</strong> {applicantData.other_reference}<br />\r\n                  </>\r\n                )}\r\n              </Typography>\r\n            </div>\r\n\r\n            <div className=\"text-center mt-8\">\r\n              <Typography variant=\"body2\" color=\"text.secondary\" style={{ fontSize: '0.9rem' }}>\r\n                Resume generated on {(() => {\r\n                  const today = new Date()\r\n                  return `${today.getMonth() + 1}/${today.getDate()}/${today.getFullYear()}`\r\n                })()}\r\n              </Typography>\r\n            </div>\r\n          </div>\r\n        </Box>\r\n      </DialogContent>\r\n    </Dialog>\r\n  )\r\n}\r\n\r\nexport default ResumeViewerModal\r\n"], "names": [], "mappings": ";;;;AAEA,gBAAgB;AAChB;AAEA,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AAZA;;;;;;;;;;AAcA,MAAM,oBAAoB,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,aAAa,EAAE;IACzD,MAAM,uBAAuB;QAC3B,6CAA6C;QAC7C,MAAM,UAAU,SAAS,aAAa,CAAC;QACvC,MAAM,QAAQ,IAAI;QAClB,MAAM,gBAAgB,GAAG,MAAM,QAAQ,KAAK,EAAE,CAAC,EAAE,MAAM,OAAO,GAAG,CAAC,EAAE,MAAM,WAAW,IAAI;QACzF,MAAM,OAAO,IAAI,KAAK;YAAC,CAAC,WAAW,EAAE,eAAe,SAAS,iCAAiC,EAAE,eAAe,SAAS,SAAS,EAAE,eAAe,MAAM,SAAS,EAAE,eAAe,gBAAgB,eAAe,SAAS,MAAM,sBAAsB,EAAE,eAAe,YAAY,qBAAqB,cAAc,EAAE,eAAe,cAAc,YAAY,mBAAmB,EAAE,eAAe,mBAAmB,eAAe,kBAAkB,YAAY,kBAAkB,EAAE,eAAe;SAAC,EAAE;YAAE,MAAM;QAAa;QAChgB,QAAQ,IAAI,GAAG,IAAI,eAAe,CAAC;QACnC,QAAQ,QAAQ,GAAG,GAAG,eAAe,YAAY,YAAY,WAAW,CAAC;QACzE,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,QAAQ,KAAK;QACb,SAAS,IAAI,CAAC,WAAW,CAAC;IAC5B;IAEA,IAAI,CAAC,eAAe,OAAO;IAE3B,qBACE,6LAAC,wJAAA,CAAA,UAAM;QAAC,MAAM;QAAM,SAAS;QAAS,UAAS;QAAK,SAAS;;0BAC3D,6LAAC,kKAAA,CAAA,UAAW;gBAAC,WAAU;;kCACrB,6LAAC,gKAAA,CAAA,UAAU;wBAAC,SAAQ;wBAAK,OAAO;4BAAE,UAAU;wBAAS;;4BAAG;4BAC5C,cAAc,QAAQ;;;;;;;kCAElC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,wJAAA,CAAA,UAAM;gCACL,SAAQ;gCACR,OAAM;gCACN,MAAK;gCACL,yBAAW,6LAAC;oCAAE,WAAU;;;;;;gCACxB,SAAS;gCACT,WAAU;gCACV,IAAI;oCAAE,UAAU;oCAAQ,SAAS;gCAAW;0CAC7C;;;;;;0CAGD,6LAAC,gKAAA,CAAA,UAAU;gCAAC,SAAS;0CACnB,cAAA,6LAAC;oCAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;0BAKnB,6LAAC,sKAAA,CAAA,UAAa;0BACZ,cAAA,6LAAC,kJAAA,CAAA,UAAG;oBAAC,IAAI;wBACP,WAAW;wBACX,QAAQ;wBACR,cAAc;wBACd,SAAS;wBACT,iBAAiB;oBACnB;8BAEE,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,gKAAA,CAAA,UAAU;wCAAC,SAAQ;wCAAK,WAAU;wCAAiB,OAAO;4CAAE,UAAU;wCAAO;kDAC3E,cAAc,QAAQ;;;;;;kDAEzB,6LAAC,gKAAA,CAAA,UAAU;wCAAC,SAAQ;wCAAK,OAAM;wCAAiB,OAAO;4CAAE,UAAU;wCAAS;;4CACzE,cAAc,KAAK;4CAAC;4CAAI,cAAc,YAAY,IAAI,cAAc,KAAK,IAAI;;;;;;;kDAEhF,6LAAC,gKAAA,CAAA,UAAU;wCAAC,SAAQ;wCAAQ,OAAM;wCAAiB,OAAO;4CAAE,UAAU;wCAAO;kDAC1E,cAAc,OAAO,IAAI;;;;;;;;;;;;0CAI9B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,gKAAA,CAAA,UAAU;wCAAC,SAAQ;wCAAK,WAAU;wCAAiB,OAAO;4CAAE,UAAU;4CAAU,cAAc;4CAAqB,eAAe;wCAAM;kDAAG;;;;;;kDAG5I,6LAAC,gKAAA,CAAA,UAAU;wCAAC,SAAQ;wCAAQ,OAAO;4CAAE,UAAU;4CAAU,YAAY;wCAAM;kDACxE,cAAc,WAAW,IAAI;;;;;;;;;;;;0CAIlC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,gKAAA,CAAA,UAAU;wCAAC,SAAQ;wCAAK,WAAU;wCAAiB,OAAO;4CAAE,UAAU;4CAAU,cAAc;4CAAqB,eAAe;wCAAM;kDAAG;;;;;;kDAG5I,6LAAC,gKAAA,CAAA,UAAU;wCAAC,SAAQ;wCAAQ,OAAO;4CAAE,UAAU;4CAAU,YAAY;wCAAM;;0DACzE,6LAAC;0DAAO;;;;;;4CAA0B;4CAAE,cAAc,UAAU,IAAI;0DAAY,6LAAC;;;;;0DAC7E,6LAAC;0DAAO;;;;;;4CAA0B;4CAAE,cAAc,QAAQ,IAAI;0DAAqB,6LAAC;;;;;0DACpF,6LAAC;0DAAO;;;;;;4CAAoC;4CAAE,cAAc,eAAe,IAAI,cAAc,cAAc,IAAI;;;;;;;;;;;;;0CAInH,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,gKAAA,CAAA,UAAU;wCAAC,SAAQ;wCAAK,WAAU;wCAAiB,OAAO;4CAAE,UAAU;4CAAU,cAAc;4CAAqB,eAAe;wCAAM;kDAAG;;;;;;kDAG5I,6LAAC,gKAAA,CAAA,UAAU;wCAAC,SAAQ;wCAAQ,OAAO;4CAAE,UAAU;4CAAU,YAAY;wCAAM;;4CACxE,cAAc,qBAAqB,kBAClC;;kEACE,6LAAC;kEAAO;;;;;;oDAAsB;oDAAE,cAAc,qBAAqB;kEAAC,6LAAC;;;;;;;4CAGxE,cAAc,yBAAyB,kBACtC;;kEACE,6LAAC;kEAAO;;;;;;oDAA0B;oDAAE,cAAc,yBAAyB;kEAAC,6LAAC;;;;;;;4CAGhF,cAAc,kBAAkB,kBAC/B;;kEACE,6LAAC;kEAAO;;;;;;oDAA4B;oDAAE,cAAc,kBAAkB;kEAAC,6LAAC;;;;;;;4CAG3E,cAAc,SAAS,kBACtB;;kEACE,6LAAC;kEAAO;;;;;;oDAA0B;oDAAE,cAAc,SAAS;kEAAC,6LAAC;;;;;;;;;;;;;;;;;;;0CAMrE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,gKAAA,CAAA,UAAU;wCAAC,SAAQ;wCAAK,WAAU;wCAAiB,OAAO;4CAAE,UAAU;4CAAU,cAAc;4CAAqB,eAAe;wCAAM;kDAAG;;;;;;kDAG5I,6LAAC,gKAAA,CAAA,UAAU;wCAAC,SAAQ;wCAAQ,OAAO;4CAAE,UAAU;4CAAU,YAAY;wCAAM;;0DACzE,6LAAC;0DAAO;;;;;;4CAA8B;4CAAE,cAAc,oBAAoB,IAAI;0DAAW,6LAAC;;;;;0DAC1F,6LAAC;0DAAO;;;;;;4CAA6B;4CAAE,cAAc,QAAQ,IAAI;;;;;;;;;;;;;0CAIrE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,gKAAA,CAAA,UAAU;wCAAC,SAAQ;wCAAK,WAAU;wCAAiB,OAAO;4CAAE,UAAU;4CAAU,cAAc;4CAAqB,eAAe;wCAAM;kDAAG;;;;;;kDAG5I,6LAAC,gKAAA,CAAA,UAAU;wCAAC,SAAQ;wCAAQ,OAAO;4CAAE,UAAU;4CAAU,YAAY;wCAAM;;0DACzE,6LAAC;0DAAO;;;;;;4CAA2B;4CAAE,cAAc,SAAS,IAAI;0DAAyB,6LAAC;;;;;4CACzF,cAAc,eAAe,kBAC5B;;kEACE,6LAAC;kEAAO;;;;;;oDAA8B;oDAAE,cAAc,eAAe;kEAAC,6LAAC;;;;;;;;;;;;;;;;;;;0CAM/E,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,gKAAA,CAAA,UAAU;oCAAC,SAAQ;oCAAQ,OAAM;oCAAiB,OAAO;wCAAE,UAAU;oCAAS;;wCAAG;wCAC3D,CAAC;4CACpB,MAAM,QAAQ,IAAI;4CAClB,OAAO,GAAG,MAAM,QAAQ,KAAK,EAAE,CAAC,EAAE,MAAM,OAAO,GAAG,CAAC,EAAE,MAAM,WAAW,IAAI;wCAC5E,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjB;KAnJM;uCAqJS", "debugId": null}}, {"offset": {"line": 2641, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/%40core/components/mui/Avatar.jsx"], "sourcesContent": ["'use client'\r\n\r\n// React Imports\r\nimport { forwardRef } from 'react'\r\n\r\n// MUI Imports\r\nimport MuiAvatar from '@mui/material/Avatar'\r\nimport { lighten, styled } from '@mui/material/styles'\r\n\r\nconst Avatar = styled(MuiAvatar)(({ skin, color, size, theme }) => {\r\n  return {\r\n    ...(color &&\r\n      skin === 'light' && {\r\n        backgroundColor: `var(--mui-palette-${color}-lightOpacity)`,\r\n        color: `var(--mui-palette-${color}-main)`\r\n      }),\r\n    ...(color &&\r\n      skin === 'light-static' && {\r\n        backgroundColor: lighten(theme.palette[color].main, 0.84),\r\n        color: `var(--mui-palette-${color}-main)`\r\n      }),\r\n    ...(color &&\r\n      skin === 'filled' && {\r\n        backgroundColor: `var(--mui-palette-${color}-main)`,\r\n        color: `var(--mui-palette-${color}-contrastText)`\r\n      }),\r\n    ...(size && {\r\n      height: size,\r\n      width: size\r\n    })\r\n  }\r\n})\r\n\r\nconst CustomAvatar = forwardRef((props, ref) => {\r\n  // Props\r\n  const { color, skin = 'filled', ...rest } = props\r\n\r\n  return <Avatar color={color} skin={skin} ref={ref} {...rest} />\r\n})\r\n\r\nexport default CustomAvatar\r\n"], "names": [], "mappings": ";;;;AAEA,gBAAgB;AAChB;AAEA,cAAc;AACd;AACA;AAAA;AAPA;;;;;AASA,MAAM,SAAS,CAAA,GAAA,6MAAA,CAAA,SAAM,AAAD,EAAE,wJAAA,CAAA,UAAS,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE;IAC5D,OAAO;QACL,GAAI,SACF,SAAS,WAAW;YAClB,iBAAiB,CAAC,kBAAkB,EAAE,MAAM,cAAc,CAAC;YAC3D,OAAO,CAAC,kBAAkB,EAAE,MAAM,MAAM,CAAC;QAC3C,CAAC;QACH,GAAI,SACF,SAAS,kBAAkB;YACzB,iBAAiB,CAAA,GAAA,iLAAA,CAAA,UAAO,AAAD,EAAE,MAAM,OAAO,CAAC,MAAM,CAAC,IAAI,EAAE;YACpD,OAAO,CAAC,kBAAkB,EAAE,MAAM,MAAM,CAAC;QAC3C,CAAC;QACH,GAAI,SACF,SAAS,YAAY;YACnB,iBAAiB,CAAC,kBAAkB,EAAE,MAAM,MAAM,CAAC;YACnD,OAAO,CAAC,kBAAkB,EAAE,MAAM,cAAc,CAAC;QACnD,CAAC;QACH,GAAI,QAAQ;YACV,QAAQ;YACR,OAAO;QACT,CAAC;IACH;AACF;KAtBM;AAwBN,MAAM,6BAAe,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,QAAE,CAAC,OAAO;IACtC,QAAQ;IACR,MAAM,EAAE,KAAK,EAAE,OAAO,QAAQ,EAAE,GAAG,MAAM,GAAG;IAE5C,qBAAO,6LAAC;QAAO,OAAO;QAAO,MAAM;QAAM,KAAK;QAAM,GAAG,IAAI;;;;;;AAC7D;;uCAEe", "debugId": null}}, {"offset": {"line": 2706, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/components/TablePaginationComponent.jsx"], "sourcesContent": ["// MUI Imports\r\nimport Pagination from '@mui/material/Pagination'\r\nimport Typography from '@mui/material/Typography'\r\n\r\nconst TablePaginationComponent = ({ table }) => {\r\n  return (\r\n    <div className='flex justify-between items-center flex-wrap pli-6 border-bs bs-auto plb-[12.5px] gap-2'>\r\n      <Typography color='text.disabled'>\r\n        {`Showing ${\r\n          table.getFilteredRowModel().rows.length === 0\r\n            ? 0\r\n            : table.getState().pagination.pageIndex * table.getState().pagination.pageSize + 1\r\n        }\r\n        to ${Math.min((table.getState().pagination.pageIndex + 1) * table.getState().pagination.pageSize, table.getFilteredRowModel().rows.length)} of ${table.getFilteredRowModel().rows.length} entries`}\r\n      </Typography>\r\n      <Pagination\r\n        shape='rounded'\r\n        color='primary'\r\n        variant='tonal'\r\n        count={Math.ceil(table.getFilteredRowModel().rows.length / table.getState().pagination.pageSize)}\r\n        page={table.getState().pagination.pageIndex + 1}\r\n        onChange={(_, page) => {\r\n          table.setPageIndex(page - 1)\r\n        }}\r\n        showFirstButton\r\n        showLastButton\r\n      />\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default TablePaginationComponent\r\n"], "names": [], "mappings": "AAAA,cAAc;;;;;AACd;AACA;;;;AAEA,MAAM,2BAA2B,CAAC,EAAE,KAAK,EAAE;IACzC,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,gKAAA,CAAA,UAAU;gBAAC,OAAM;0BACf,CAAC,QAAQ,EACR,MAAM,mBAAmB,GAAG,IAAI,CAAC,MAAM,KAAK,IACxC,IACA,MAAM,QAAQ,GAAG,UAAU,CAAC,SAAS,GAAG,MAAM,QAAQ,GAAG,UAAU,CAAC,QAAQ,GAAG,EACpF;WACE,EAAE,KAAK,GAAG,CAAC,CAAC,MAAM,QAAQ,GAAG,UAAU,CAAC,SAAS,GAAG,CAAC,IAAI,MAAM,QAAQ,GAAG,UAAU,CAAC,QAAQ,EAAE,MAAM,mBAAmB,GAAG,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,MAAM,mBAAmB,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC;;;;;;0BAEpM,6LAAC,gKAAA,CAAA,UAAU;gBACT,OAAM;gBACN,OAAM;gBACN,SAAQ;gBACR,OAAO,KAAK,IAAI,CAAC,MAAM,mBAAmB,GAAG,IAAI,CAAC,MAAM,GAAG,MAAM,QAAQ,GAAG,UAAU,CAAC,QAAQ;gBAC/F,MAAM,MAAM,QAAQ,GAAG,UAAU,CAAC,SAAS,GAAG;gBAC9C,UAAU,CAAC,GAAG;oBACZ,MAAM,YAAY,CAAC,OAAO;gBAC5B;gBACA,eAAe;gBACf,cAAc;;;;;;;;;;;;AAItB;KAzBM;uCA2BS", "debugId": null}}, {"offset": {"line": 2764, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/@core/styles/table.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"cellWithInput\": \"table-module__Mig-TG__cellWithInput\",\n  \"table\": \"table-module__Mig-TG__table\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA"}}, {"offset": {"line": 2774, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/CAM-TRANSPORT_MAIN/cam-backend-system/full-version/src/views/apps/user/jobs/ApplyJobTable.jsx"], "sourcesContent": ["'use client'\r\n\r\n// React Imports\r\nimport React, { useState, useMemo, useEffect } from 'react'\r\n\r\n// Next Imports\r\nimport Link from 'next/link'\r\nimport { useParams } from 'next/navigation'\r\n\r\n// MUI Imports\r\nimport Card from '@mui/material/Card'\r\nimport CardHeader from '@mui/material/CardHeader'\r\nimport Button from '@mui/material/Button'\r\nimport Typography from '@mui/material/Typography'\r\nimport Checkbox from '@mui/material/Checkbox'\r\nimport IconButton from '@mui/material/IconButton'\r\nimport { styled } from '@mui/material/styles'\r\nimport TablePagination from '@mui/material/TablePagination'\r\nimport MenuItem from '@mui/material/MenuItem'\r\nimport Chip from '@mui/material/Chip'\r\nimport CircularProgress from '@mui/material/CircularProgress'\r\nimport Alert from '@mui/material/Alert'\r\nimport CardContent from '@mui/material/CardContent'\r\nimport Select from '@mui/material/Select'\r\nimport FormControl from '@mui/material/FormControl'\r\nimport ListItemIcon from '@mui/material/ListItemIcon'\r\nimport Menu from '@mui/material/Menu'\r\n\r\n// Third-party Imports\r\nimport classnames from 'classnames'\r\nimport { rankItem } from '@tanstack/match-sorter-utils'\r\nimport {\r\n  createColumnHelper,\r\n  flexRender,\r\n  getCoreRowModel,\r\n  useReactTable,\r\n  getFilteredRowModel,\r\n  getFacetedRowModel,\r\n  getFacetedUniqueValues,\r\n  getFacetedMinMaxValues,\r\n  getPaginationRowModel,\r\n  getSortedRowModel\r\n} from '@tanstack/react-table'\r\n\r\n// Component Imports\r\nimport TableFilters from './TableFilters'\r\nimport ApplicantDetailsModal from './ApplicantDetailsModal'\r\nimport ResumeViewerModal from './ResumeViewerModal'\r\nimport CustomTextField from '@core/components/mui/TextField'\r\nimport CustomAvatar from '@core/components/mui/Avatar'\r\nimport TablePaginationComponent from '@components/TablePaginationComponent'\r\n\r\n// PDF Export\r\nimport jsPDF from 'jspdf'\r\n// Import autotable plugin\r\nimport 'jspdf-autotable'\r\n\r\n// API Imports\r\nimport { fetchJobApplications, deleteJobApplication, downloadResume, updateJobApplicationStatus } from '@/services/jobApi'\r\n\r\n// Util Imports\r\nimport { getLocalizedUrl } from '@/utils/i18n'\r\n\r\n// Style Imports\r\nimport tableStyles from '@core/styles/table.module.css'\r\n\r\nconst fuzzyFilter = (row, columnId, value, addMeta) => {\r\n  // Rank the item\r\n  const itemRank = rankItem(row.getValue(columnId), value)\r\n\r\n  // Store the itemRank info\r\n  addMeta({\r\n    itemRank\r\n  })\r\n\r\n  // Return if the item should be filtered in/out\r\n  return itemRank.passed\r\n}\r\n\r\nconst DebouncedInput = ({ value: initialValue, onChange, debounce = 500, ...props }) => {\r\n  // States\r\n  const [value, setValue] = useState(initialValue)\r\n\r\n  useEffect(() => {\r\n    setValue(initialValue)\r\n  }, [initialValue])\r\n  useEffect(() => {\r\n    const timeout = setTimeout(() => {\r\n      onChange(value)\r\n    }, debounce)\r\n\r\n    return () => clearTimeout(timeout)\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [value])\r\n\r\n  return <CustomTextField {...props} value={value} onChange={e => setValue(e.target.value)} />\r\n}\r\n\r\n// Column Definitions\r\n// Status configuration\r\nconst statusConfig = {\r\n  pending: {\r\n    label: 'Pending',\r\n    color: 'warning',\r\n    icon: 'tabler-clock',\r\n    description: 'Waiting for review'\r\n  },\r\n  'in-view': {\r\n    label: 'In View',\r\n    color: 'info',\r\n    icon: 'tabler-eye',\r\n    description: 'Being reviewed'\r\n  },\r\n  completed: {\r\n    label: 'Completed',\r\n    color: 'success',\r\n    icon: 'tabler-check',\r\n    description: 'Review completed'\r\n  }\r\n}\r\n\r\n// Status options for dropdown\r\nconst statusOptions = [\r\n  { value: 'pending', label: 'Pending', color: 'warning', icon: 'tabler-clock' },\r\n  { value: 'in-view', label: 'In View', color: 'info', icon: 'tabler-eye' },\r\n  { value: 'completed', label: 'Completed', color: 'success', icon: 'tabler-check' }\r\n]\r\n\r\n// Status Dropdown Component\r\nconst StatusDropdown = ({ currentStatus, onStatusChange, applicationId }) => {\r\n  const [isOpen, setIsOpen] = useState(false)\r\n  const config = statusConfig[currentStatus] || statusConfig.pending\r\n\r\n  const handleStatusSelect = (newStatus) => {\r\n    console.log('StatusDropdown: Selecting status', newStatus, 'for application', applicationId)\r\n    onStatusChange(applicationId, newStatus)\r\n    setIsOpen(false)\r\n  }\r\n\r\n  if (!isOpen) {\r\n    // Show as button/chip when closed\r\n    return (\r\n      <Chip\r\n        icon={<i className={`${config.icon} text-xs sm:text-sm`} />}\r\n        label={config.label}\r\n        color={config.color}\r\n        variant='filled'\r\n        size='small'\r\n        className='text-xs sm:text-sm cursor-pointer'\r\n        onClick={() => setIsOpen(true)}\r\n        title=\"Click to change status\"\r\n        sx={{\r\n          height: { xs: '28px', sm: '32px' },\r\n          width: { xs: '100px', sm: '110px' },\r\n          minWidth: { xs: '100px', sm: '110px' },\r\n          maxWidth: { xs: '100px', sm: '110px' },\r\n          fontSize: { xs: '0.75rem', sm: '0.8rem' },\r\n          cursor: 'pointer',\r\n          transition: 'all 0.2s ease-in-out',\r\n          '& .MuiChip-label': {\r\n            padding: { xs: '0 6px', sm: '0 8px' },\r\n            fontSize: { xs: '0.7rem', sm: '0.75rem' },\r\n            fontWeight: 500,\r\n            whiteSpace: 'nowrap',\r\n            overflow: 'visible',\r\n            textOverflow: 'unset'\r\n          },\r\n          '& .MuiChip-icon': {\r\n            fontSize: { xs: '14px', sm: '16px' },\r\n            marginLeft: { xs: '6px', sm: '8px' },\r\n            marginRight: { xs: '0px', sm: '2px' }\r\n          },\r\n          '&:hover': {\r\n            transform: 'scale(1.02)',\r\n            boxShadow: 2\r\n          }\r\n        }}\r\n      />\r\n    )\r\n  }\r\n\r\n  // Show as dropdown when open\r\n  return (\r\n    <FormControl size=\"small\" sx={{ minWidth: 120 }}>\r\n      <Select\r\n        value={currentStatus}\r\n        onChange={(e) => handleStatusSelect(e.target.value)}\r\n        onClose={() => setIsOpen(false)}\r\n        open={isOpen}\r\n        size=\"small\"\r\n        autoFocus\r\n        sx={{\r\n          height: '32px',\r\n          fontSize: '0.75rem',\r\n          '& .MuiSelect-select': {\r\n            padding: '4px 8px',\r\n            display: 'flex',\r\n            alignItems: 'center',\r\n            gap: 1\r\n          }\r\n        }}\r\n      >\r\n        {statusOptions.map((option) => (\r\n          <MenuItem\r\n            key={option.value}\r\n            value={option.value}\r\n            onClick={() => handleStatusSelect(option.value)}\r\n          >\r\n            <ListItemIcon sx={{ minWidth: '20px !important' }}>\r\n              <i className={`${option.icon} text-sm`} />\r\n            </ListItemIcon>\r\n            <Typography variant=\"body2\" sx={{ fontSize: '0.75rem' }}>\r\n              {option.label}\r\n            </Typography>\r\n          </MenuItem>\r\n        ))}\r\n      </Select>\r\n    </FormControl>\r\n  )\r\n}\r\n\r\nconst columnHelper = createColumnHelper()\r\n\r\nconst ApplyJobTable = () => {\r\n  // States\r\n  const [rowSelection, setRowSelection] = useState({})\r\n  const [data, setData] = useState([])\r\n  const [filteredData, setFilteredData] = useState([])\r\n  const [globalFilter, setGlobalFilter] = useState('')\r\n  const [detailsModalOpen, setDetailsModalOpen] = useState(false)\r\n  const [resumeModalOpen, setResumeModalOpen] = useState(false)\r\n  const [selectedApplicant, setSelectedApplicant] = useState(null)\r\n  const [loading, setLoading] = useState(true)\r\n  const [error, setError] = useState(null)\r\n  const [actionMenuAnchor, setActionMenuAnchor] = useState(null)\r\n  const [selectedApplicationId, setSelectedApplicationId] = useState(null)\r\n\r\n  // Hooks\r\n  const { lang: locale } = useParams()\r\n\r\n  // Load job applications from backend\r\n  const loadJobApplications = async () => {\r\n    try {\r\n      console.log('🔄 Loading job applications from backend...')\r\n      setLoading(true)\r\n      setError(null)\r\n\r\n      const applications = await fetchJobApplications()\r\n      console.log('✅ Fetched applications:', applications.length, 'items')\r\n      console.log('📋 Sample application:', applications[0])\r\n\r\n      // Ensure all applications have a status field (default to 'pending' if missing)\r\n      const applicationsWithStatus = applications.map(application => ({\r\n        ...application,\r\n        status: application.status || 'pending'\r\n      }))\r\n\r\n      setData(applicationsWithStatus)\r\n      setFilteredData(applicationsWithStatus)\r\n    } catch (err) {\r\n      console.error('❌ Error loading job applications:', err)\r\n      setError('Failed to load job applications. Please try again.')\r\n    } finally {\r\n      setLoading(false)\r\n    }\r\n  }\r\n\r\n  // Load job applications on component mount\r\n  useEffect(() => {\r\n    loadJobApplications()\r\n  }, [])\r\n\r\n  const handleViewDetails = (applicantData) => {\r\n    setSelectedApplicant(applicantData)\r\n    setDetailsModalOpen(true)\r\n  }\r\n\r\n  const handleViewResume = (applicantData) => {\r\n    setSelectedApplicant(applicantData)\r\n    setResumeModalOpen(true)\r\n  }\r\n\r\n  const handleStatusChange = async (applicationId, newStatus) => {\r\n    console.log('Updating status for application:', applicationId, 'to:', newStatus)\r\n\r\n    try {\r\n      // Call API to update status in backend\r\n      await updateJobApplicationStatus(applicationId, newStatus)\r\n      console.log('Job application status updated in backend successfully')\r\n\r\n      // Update local state only after successful backend update\r\n      setData(prevData =>\r\n        prevData.map(item =>\r\n          item.id === applicationId\r\n            ? { ...item, status: newStatus }\r\n            : item\r\n        )\r\n      )\r\n      setFilteredData(prevData =>\r\n        prevData.map(item =>\r\n          item.id === applicationId\r\n            ? { ...item, status: newStatus }\r\n            : item\r\n        )\r\n      )\r\n\r\n      console.log('Job application status updated in frontend successfully')\r\n    } catch (error) {\r\n      console.error('Failed to update job application status:', error)\r\n      alert('Failed to update status. Please try again.')\r\n    }\r\n  }\r\n\r\n\r\n\r\n  const handleDeleteApplication = async (applicationId) => {\r\n    try {\r\n      // Find the application to get their name for confirmation\r\n      const application = data.find(item => item.id === applicationId)\r\n      const applicantName = application?.fullName || 'this application'\r\n\r\n      // Show confirmation dialog\r\n      const confirmed = window.confirm(\r\n        `Are you sure you want to delete ${applicantName}'s application?\\n\\nThis action cannot be undone and will permanently remove the application from the database.`\r\n      )\r\n\r\n      if (!confirmed) {\r\n        return\r\n      }\r\n\r\n      // Call backend API to delete application\r\n      await deleteJobApplication(applicationId)\r\n\r\n      // Remove application from local state\r\n      setData(prevData => prevData.filter(item => item.id !== applicationId))\r\n      setFilteredData(prevData => prevData.filter(item => item.id !== applicationId))\r\n\r\n      // Clear selection if deleted application was selected\r\n      setRowSelection(prevSelection => {\r\n        const newSelection = { ...prevSelection }\r\n        delete newSelection[applicationId]\r\n        return newSelection\r\n      })\r\n\r\n      // Show success message\r\n      alert(`${applicantName}'s application has been deleted successfully!`)\r\n\r\n    } catch (error) {\r\n      console.error('Error deleting application:', error)\r\n      alert('Failed to delete application. Please try again.')\r\n    }\r\n  }\r\n\r\n  const handleDownloadResume = async (applicantData) => {\r\n    try {\r\n      if (!applicantData.resume) {\r\n        alert('No resume file found for this applicant.')\r\n        return\r\n      }\r\n\r\n      console.log('Downloading resume for:', applicantData.fullName)\r\n      console.log('Resume path:', applicantData.resume)\r\n\r\n      await downloadResume(applicantData.resume, applicantData.fullName)\r\n      alert('Resume downloaded successfully!')\r\n    } catch (error) {\r\n      console.error('Error downloading resume:', error)\r\n\r\n      let errorMessage = 'Failed to download resume. '\r\n      if (error.message.includes('404') || error.message.includes('not found')) {\r\n        errorMessage += 'The resume file was not found on the server.'\r\n      } else if (error.message.includes('network')) {\r\n        errorMessage += 'Please check your internet connection.'\r\n      } else {\r\n        errorMessage += 'Please try again or contact support.'\r\n      }\r\n\r\n      alert(errorMessage)\r\n    }\r\n  }\r\n\r\n  const handleActionMenuOpen = (event, applicationId) => {\r\n    setActionMenuAnchor(event.currentTarget)\r\n    setSelectedApplicationId(applicationId)\r\n  }\r\n\r\n  const handleActionMenuClose = () => {\r\n    setActionMenuAnchor(null)\r\n    setSelectedApplicationId(null)\r\n  }\r\n\r\n  const columns = useMemo(\r\n    () => [\r\n      {\r\n        id: 'select',\r\n        header: ({ table }) => (\r\n          <Checkbox\r\n            {...{\r\n              checked: table.getIsAllRowsSelected(),\r\n              indeterminate: table.getIsSomeRowsSelected(),\r\n              onChange: table.getToggleAllRowsSelectedHandler()\r\n            }}\r\n          />\r\n        ),\r\n        cell: ({ row }) => (\r\n          <Checkbox\r\n            {...{\r\n              checked: row.getIsSelected(),\r\n              disabled: !row.getCanSelect(),\r\n              indeterminate: row.getIsSomeSelected(),\r\n              onChange: row.getToggleSelectedHandler()\r\n            }}\r\n          />\r\n        )\r\n      },\r\n      columnHelper.accessor('fullName', {\r\n        header: 'Applicant',\r\n        cell: ({ row }) => (\r\n          <div className='flex items-center gap-4'>\r\n            <CustomAvatar\r\n              variant='rounded'\r\n              color='primary'\r\n              skin='light'\r\n              size={34}\r\n            >\r\n              {row.original.fullName?.charAt(0)?.toUpperCase()}\r\n            </CustomAvatar>\r\n            <div className='flex flex-col'>\r\n              <Typography color='text.primary' className='font-medium' style={{ fontSize: '1.1rem' }}>\r\n                {row.original.fullName}\r\n              </Typography>\r\n              <Typography variant='body1' color='text.primary' className='font-medium' style={{ fontSize: '1rem', letterSpacing: '1px' }}>\r\n                {row.original.email}\r\n              </Typography>\r\n            </div>\r\n          </div>\r\n        )\r\n      }),\r\n      columnHelper.accessor('position', {\r\n        header: 'Position Applied',\r\n        cell: ({ row }) => (\r\n          <Typography className='capitalize' color='text.primary'>\r\n            {row.original.position || 'Not specified'}\r\n          </Typography>\r\n        )\r\n      }),\r\n      columnHelper.accessor('experience', {\r\n        header: 'Experience',\r\n        cell: ({ row }) => (\r\n          <Typography color='text.primary'>\r\n            {row.original.experience || 'Not specified'}\r\n          </Typography>\r\n        )\r\n      }),\r\n      columnHelper.accessor('appliedDate', {\r\n        header: 'Applied Date',\r\n        cell: ({ row }) => {\r\n          const formatDateTime = (dateString) => {\r\n            if (!dateString) return 'Not available'\r\n\r\n            try {\r\n              // Handle both ISO string and already formatted dates\r\n              const date = new Date(dateString)\r\n              if (isNaN(date.getTime())) return dateString // Return original if invalid date\r\n\r\n              // Format: MM/DD/YYYY HH:MM AM/PM (using actual submission time from database)\r\n              const dateOptions = {\r\n                month: '2-digit',\r\n                day: '2-digit',\r\n                year: 'numeric'\r\n              }\r\n              const timeOptions = {\r\n                hour: '2-digit',\r\n                minute: '2-digit',\r\n                hour12: true\r\n              }\r\n\r\n              const formattedDate = date.toLocaleDateString('en-US', dateOptions)\r\n              const formattedTime = date.toLocaleTimeString('en-US', timeOptions)\r\n\r\n              return `${formattedDate} ${formattedTime}`\r\n            } catch (error) {\r\n              return dateString // Return original if formatting fails\r\n            }\r\n          }\r\n\r\n          return (\r\n            <div className='flex flex-col'>\r\n              <Typography color='text.primary' style={{ fontSize: '0.95rem', lineHeight: '1.2' }}>\r\n                {formatDateTime(row.original.appliedDate || row.original.createdAt)}\r\n              </Typography>\r\n            </div>\r\n          )\r\n        }\r\n      }),\r\n      columnHelper.accessor('status', {\r\n        header: 'Status',\r\n        cell: ({ row }) => {\r\n          const status = row.original.status || 'pending'\r\n\r\n          return (\r\n            <div className='flex items-center justify-start gap-1 min-w-[110px] sm:min-w-[120px] pr-3'>\r\n              <StatusDropdown\r\n                currentStatus={status}\r\n                onStatusChange={handleStatusChange}\r\n                applicationId={row.original.id}\r\n              />\r\n            </div>\r\n          )\r\n        },\r\n        enableSorting: false\r\n      }),\r\n      columnHelper.accessor('action', {\r\n        header: 'Action',\r\n        cell: ({ row }) => (\r\n          <div className='flex items-center gap-1'>\r\n            {/* Delete */}\r\n            <IconButton\r\n              onClick={() => handleDeleteApplication(row.original.id)}\r\n              title=\"Delete Application\"\r\n              size='small'\r\n              sx={{\r\n                color: 'text.secondary',\r\n                '&:hover': {\r\n                  color: 'error.main',\r\n                  backgroundColor: 'error.light',\r\n                  transform: 'scale(1.1)'\r\n                },\r\n                transition: 'all 0.2s ease-in-out'\r\n              }}\r\n            >\r\n              <i className='tabler-trash' />\r\n            </IconButton>\r\n\r\n            {/* View Details */}\r\n            <IconButton\r\n              onClick={() => handleViewDetails(row.original)}\r\n              title=\"View Details\"\r\n              size='small'\r\n              sx={{\r\n                color: 'text.secondary',\r\n                '&:hover': {\r\n                  color: 'info.main',\r\n                  backgroundColor: 'info.light',\r\n                  transform: 'scale(1.1)'\r\n                },\r\n                transition: 'all 0.2s ease-in-out'\r\n              }}\r\n            >\r\n              <i className='tabler-eye' />\r\n            </IconButton>\r\n\r\n            {/* Three Dots Menu */}\r\n            <IconButton\r\n              onClick={(e) => handleActionMenuOpen(e, row.original.id)}\r\n              title=\"More Actions\"\r\n              size='small'\r\n              sx={{\r\n                color: 'text.secondary',\r\n                '&:hover': {\r\n                  color: 'primary.main',\r\n                  backgroundColor: 'primary.light',\r\n                  transform: 'scale(1.1)'\r\n                },\r\n                transition: 'all 0.2s ease-in-out'\r\n              }}\r\n            >\r\n              <i className='tabler-dots-vertical' />\r\n            </IconButton>\r\n          </div>\r\n        ),\r\n        enableSorting: false\r\n      })\r\n    ],\r\n    [data, filteredData]\r\n  )\r\n\r\n  const table = useReactTable({\r\n    data: filteredData,\r\n    columns,\r\n    filterFns: {\r\n      fuzzy: fuzzyFilter\r\n    },\r\n    state: {\r\n      rowSelection,\r\n      globalFilter\r\n    },\r\n    initialState: {\r\n      pagination: {\r\n        pageSize: 10\r\n      }\r\n    },\r\n    enableRowSelection: true,\r\n    globalFilterFn: fuzzyFilter,\r\n    onRowSelectionChange: setRowSelection,\r\n    getCoreRowModel: getCoreRowModel(),\r\n    onGlobalFilterChange: setGlobalFilter,\r\n    getFilteredRowModel: getFilteredRowModel(),\r\n    getSortedRowModel: getSortedRowModel(),\r\n    getPaginationRowModel: getPaginationRowModel(),\r\n    getFacetedRowModel: getFacetedRowModel(),\r\n    getFacetedUniqueValues: getFacetedUniqueValues(),\r\n    getFacetedMinMaxValues: getFacetedMinMaxValues()\r\n  })\r\n\r\n  // Get selected rows data\r\n  const getSelectedRowsData = () => {\r\n    const selectedRows = table.getFilteredSelectedRowModel().rows\r\n    return selectedRows.map(row => row.original)\r\n  }\r\n\r\n  // PDF Export function with detailed user information\r\n  const exportSelectedToPDF = () => {\r\n    const selectedData = getSelectedRowsData()\r\n\r\n    console.log('=== PDF EXPORT DEBUG ===')\r\n    console.log('Selected applications count:', selectedData.length)\r\n    console.log('Selected data:', selectedData)\r\n    console.log('Sample application data:', selectedData[0])\r\n\r\n    if (selectedData.length === 0) {\r\n      alert('Please select at least one application to export.')\r\n      return\r\n    }\r\n\r\n    try {\r\n      const doc = new jsPDF()\r\n      let yPosition = 20\r\n\r\n      // Add title\r\n      doc.setFontSize(20)\r\n      doc.setTextColor(40, 40, 40)\r\n      doc.text('CAM Transport - Job Applications Export', 20, yPosition)\r\n      yPosition += 15\r\n\r\n      // Add export info\r\n      doc.setFontSize(12)\r\n      doc.setTextColor(100, 100, 100)\r\n      doc.text(`Export Date: ${new Date().toLocaleDateString()}`, 20, yPosition)\r\n      yPosition += 8\r\n      doc.text(`Selected Applications: ${selectedData.length}`, 20, yPosition)\r\n      yPosition += 20\r\n\r\n      // Process each selected application\r\n      selectedData.forEach((app, index) => {\r\n        // Check if we need a new page\r\n        if (yPosition > 250) {\r\n          doc.addPage()\r\n          yPosition = 20\r\n        }\r\n\r\n        // Application header\r\n        doc.setFontSize(16)\r\n        doc.setTextColor(41, 128, 185)\r\n        doc.setFont(undefined, 'bold')\r\n        doc.text(`${index + 1}. ${app.fullName || 'Unknown Applicant'}`, 20, yPosition)\r\n        yPosition += 12\r\n\r\n        // Draw a line under the name\r\n        doc.setDrawColor(41, 128, 185)\r\n        doc.line(20, yPosition - 2, 190, yPosition - 2)\r\n        yPosition += 8\r\n\r\n        // Personal Information Section\r\n        doc.setFontSize(12)\r\n        doc.setTextColor(0, 0, 0)\r\n        doc.setFont(undefined, 'bold')\r\n        doc.text('Personal Information:', 20, yPosition)\r\n        yPosition += 8\r\n\r\n        doc.setFontSize(10)\r\n        doc.setFont(undefined, 'normal')\r\n\r\n        const personalInfo = [\r\n          `Full Name: ${app.fullName || 'Not provided'}`,\r\n          `Email: ${app.email || 'Not provided'}`,\r\n          `Phone: ${app.phone || app.phoneNumber || 'Not provided'}`,\r\n          `Date of Birth: ${app.dob || 'Not provided'}`,\r\n          `Address: ${app.address || 'Not provided'}`\r\n        ]\r\n\r\n        personalInfo.forEach(info => {\r\n          doc.text(info, 25, yPosition)\r\n          yPosition += 6\r\n        })\r\n\r\n        yPosition += 5\r\n\r\n        // Job Information Section\r\n        doc.setFontSize(12)\r\n        doc.setFont(undefined, 'bold')\r\n        doc.text('Job Information:', 20, yPosition)\r\n        yPosition += 8\r\n\r\n        doc.setFontSize(10)\r\n        doc.setFont(undefined, 'normal')\r\n\r\n        const jobInfo = [\r\n          `Position Applied: ${app.position || 'Not specified'}`,\r\n          `Specific Driving Role: ${app.specificDrivingRole || 'Not specified'}`,\r\n          `Specific Non-Driving Role: ${app.specificNonDrivingRole || 'Not specified'}`,\r\n          `Employment Type: ${app.employmentType || app.employment_type || 'Not specified'}`,\r\n          `Experience: ${app.experience || 'Not specified'}`,\r\n          `Preferred Start Date: ${app.preferredStartDate || 'Not specified'}`,\r\n          `Willing to Relocate: ${app.relocate || 'Not specified'}`,\r\n          `Commercial License: ${app.commercialLicense || 'Not specified'}`,\r\n          `Other Job Details: ${app.otherJob || 'Not provided'}`\r\n        ]\r\n\r\n        jobInfo.forEach(info => {\r\n          doc.text(info, 25, yPosition)\r\n          yPosition += 6\r\n        })\r\n\r\n        yPosition += 5\r\n\r\n        // Application Details Section\r\n        doc.setFontSize(12)\r\n        doc.setFont(undefined, 'bold')\r\n        doc.text('Application Details:', 20, yPosition)\r\n        yPosition += 8\r\n\r\n        doc.setFontSize(10)\r\n        doc.setFont(undefined, 'normal')\r\n\r\n        // Format date with time for PDF (using actual submission time from database)\r\n        const formatDateTimeForPDF = (dateString) => {\r\n          if (!dateString) return 'Not available'\r\n          try {\r\n            // Parse the actual timestamp from database (createdAt field)\r\n            const date = new Date(dateString)\r\n            if (isNaN(date.getTime())) return dateString\r\n\r\n            const dateOptions = { month: '2-digit', day: '2-digit', year: 'numeric' }\r\n            const timeOptions = { hour: '2-digit', minute: '2-digit', hour12: true }\r\n\r\n            const formattedDate = date.toLocaleDateString('en-US', dateOptions)\r\n            const formattedTime = date.toLocaleTimeString('en-US', timeOptions)\r\n\r\n            return `${formattedDate} ${formattedTime}`\r\n          } catch (error) {\r\n            return dateString\r\n          }\r\n        }\r\n\r\n        const applicationDetails = [\r\n          `Applied Date: ${formatDateTimeForPDF(app.appliedDate || app.createdAt)}`,\r\n          `Current Status: ${statusConfig[app.status || 'pending']?.label || 'Pending'}`,\r\n          `Work Reason: ${app.workReason || 'Not provided'}`,\r\n          `Reference: ${app.reference || 'Not provided'}`,\r\n          `Other Reference: ${app.otherReference || 'Not provided'}`,\r\n          `Resume: ${app.resume ? 'Uploaded' : 'Not uploaded'}`\r\n        ]\r\n\r\n        applicationDetails.forEach(info => {\r\n          doc.text(info, 25, yPosition)\r\n          yPosition += 6\r\n        })\r\n\r\n        // Add separator line between applications\r\n        yPosition += 10\r\n        doc.setDrawColor(200, 200, 200)\r\n        doc.line(20, yPosition, 190, yPosition)\r\n        yPosition += 15\r\n      })\r\n\r\n      // Save the PDF\r\n      const fileName = `CAM_Transport_Job_Applications_${new Date().toISOString().split('T')[0]}.pdf`\r\n      doc.save(fileName)\r\n\r\n      alert(`Successfully exported ${selectedData.length} application(s) with full details to ${fileName}`)\r\n\r\n    } catch (error) {\r\n      console.error('PDF Export Error:', error)\r\n      alert('Error generating PDF. Please try again.')\r\n    }\r\n  }\r\n\r\n\r\n\r\n  // Show loading state\r\n  if (loading) {\r\n    return (\r\n      <Card>\r\n        <CardHeader title='Job Applications' className='pbe-4' />\r\n        <CardContent className='flex justify-center items-center py-8'>\r\n          <div className='flex flex-col items-center gap-4'>\r\n            <CircularProgress />\r\n            <Typography variant='body2' color='text.secondary'>\r\n              Loading job applications...\r\n            </Typography>\r\n          </div>\r\n        </CardContent>\r\n      </Card>\r\n    )\r\n  }\r\n\r\n  // Show error state\r\n  if (error) {\r\n    return (\r\n      <Card>\r\n        <CardHeader title='Job Applications' className='pbe-4' />\r\n        <CardContent>\r\n          <Alert severity='error' className='mb-4'>\r\n            {error}\r\n          </Alert>\r\n          <div className='flex gap-2'>\r\n            <Button\r\n              variant='contained'\r\n              onClick={loadJobApplications}\r\n              startIcon={<i className='tabler-refresh' />}\r\n            >\r\n              Retry\r\n            </Button>\r\n            <Button\r\n              variant='outlined'\r\n              onClick={() => window.location.reload()}\r\n              startIcon={<i className='tabler-reload' />}\r\n            >\r\n              Reload Page\r\n            </Button>\r\n          </div>\r\n        </CardContent>\r\n      </Card>\r\n    )\r\n  }\r\n\r\n  // Show empty state\r\n  if (!data || data.length === 0) {\r\n    return (\r\n      <Card>\r\n        <CardHeader title='Job Applications' className='pbe-4' />\r\n        <CardContent className='flex justify-center items-center py-8'>\r\n          <div className='flex flex-col items-center gap-4 text-center'>\r\n            <i className='tabler-briefcase text-6xl text-textSecondary' />\r\n            <Typography variant='h6' color='text.secondary'>\r\n              No job applications found\r\n            </Typography>\r\n            <Typography variant='body2' color='text.secondary'>\r\n              Job applications submitted through the website will appear here.\r\n            </Typography>\r\n          </div>\r\n        </CardContent>\r\n      </Card>\r\n    )\r\n  }\r\n\r\n  return (\r\n    <>\r\n      <Card>\r\n        <CardHeader\r\n          title={\r\n            <div className='flex items-center gap-3'>\r\n              <span>Job Applications</span>\r\n              {data.length > 0 && (\r\n                <Chip\r\n                  label={`${data.length} application${data.length !== 1 ? 's' : ''}`}\r\n                  color='primary'\r\n                  variant='tonal'\r\n                  size='small'\r\n                />\r\n              )}\r\n              {Object.keys(rowSelection).length > 0 && (\r\n                <Chip\r\n                  label={`${Object.keys(rowSelection).length} selected`}\r\n                  color='secondary'\r\n                  variant='filled'\r\n                  size='small'\r\n                  icon={<i className='tabler-check' />}\r\n                />\r\n              )}\r\n            </div>\r\n          }\r\n          className='pbe-4'\r\n        />\r\n        <TableFilters setData={setFilteredData} tableData={data} />\r\n        <div className='flex justify-between flex-col items-start md:flex-row md:items-center p-6 border-bs gap-4'>\r\n          <CustomTextField\r\n            select\r\n            value={table.getState().pagination.pageSize}\r\n            onChange={e => table.setPageSize(Number(e.target.value))}\r\n            className='max-sm:is-full sm:is-[70px]'\r\n          >\r\n            <MenuItem value='10'>10</MenuItem>\r\n            <MenuItem value='25'>25</MenuItem>\r\n            <MenuItem value='50'>50</MenuItem>\r\n          </CustomTextField>\r\n          <div className='flex flex-col sm:flex-row max-sm:is-full items-start sm:items-center gap-4'>\r\n            <DebouncedInput\r\n              value={globalFilter ?? ''}\r\n              onChange={value => setGlobalFilter(String(value))}\r\n              placeholder='Search Job Applications'\r\n              className='max-sm:is-full'\r\n            />\r\n\r\n            {Object.keys(rowSelection).length > 0 ? (\r\n              <Button\r\n                color='warning'\r\n                variant='outlined'\r\n                size='small'\r\n                startIcon={<i className='tabler-x' />}\r\n                onClick={() => setRowSelection({})}\r\n                className='max-sm:is-full'\r\n              >\r\n                Clear Selection\r\n              </Button>\r\n            ) : (\r\n              <Button\r\n                color='info'\r\n                variant='outlined'\r\n                size='small'\r\n                startIcon={<i className='tabler-check-all' />}\r\n                onClick={() => {\r\n                  const allRowIds = {}\r\n                  table.getRowModel().rows.forEach(row => {\r\n                    allRowIds[row.original.id] = true\r\n                  })\r\n                  setRowSelection(allRowIds)\r\n                }}\r\n                className='max-sm:is-full'\r\n              >\r\n                Select All\r\n              </Button>\r\n            )}\r\n\r\n            <Button\r\n              color='secondary'\r\n              variant='tonal'\r\n              startIcon={<i className='tabler-file-type-pdf' />}\r\n              onClick={exportSelectedToPDF}\r\n              disabled={Object.keys(rowSelection).length === 0}\r\n              className='max-sm:is-full'\r\n              sx={{\r\n                '&:disabled': {\r\n                  opacity: 0.5,\r\n                  cursor: 'not-allowed'\r\n                }\r\n              }}\r\n            >\r\n              Export PDF ({Object.keys(rowSelection).length})\r\n            </Button>\r\n            <IconButton\r\n              color='primary'\r\n              onClick={loadJobApplications}\r\n              disabled={loading}\r\n              title={loading ? 'Loading...' : 'Refresh Data'}\r\n              sx={{\r\n                border: '1px solid',\r\n                borderColor: 'primary.main',\r\n                '&:hover': {\r\n                  backgroundColor: 'primary.light',\r\n                  transform: 'scale(1.05)'\r\n                },\r\n                transition: 'all 0.2s ease-in-out'\r\n              }}\r\n            >\r\n              <i className={`tabler-refresh ${loading ? 'animate-spin' : ''}`} />\r\n            </IconButton>\r\n          </div>\r\n        </div>\r\n        <div className='overflow-x-auto'>\r\n          <table className={tableStyles.table}>\r\n            <thead>\r\n              {table.getHeaderGroups().map(headerGroup => (\r\n                <tr key={headerGroup.id}>\r\n                  {headerGroup.headers.map(header => (\r\n                    <th key={header.id}>\r\n                      {header.isPlaceholder ? null : (\r\n                        <>\r\n                          <div\r\n                            className={classnames({\r\n                              'flex items-center': header.column.getIsSorted(),\r\n                              'cursor-pointer select-none': header.column.getCanSort()\r\n                            })}\r\n                            onClick={header.column.getToggleSortingHandler()}\r\n                          >\r\n                            {flexRender(header.column.columnDef.header, header.getContext())}\r\n                            {{\r\n                              asc: <i className='tabler-chevron-up text-xl' />,\r\n                              desc: <i className='tabler-chevron-down text-xl' />\r\n                            }[header.column.getIsSorted()] ?? null}\r\n                          </div>\r\n                        </>\r\n                      )}\r\n                    </th>\r\n                  ))}\r\n                </tr>\r\n              ))}\r\n            </thead>\r\n            {table.getFilteredRowModel().rows.length === 0 ? (\r\n              <tbody>\r\n                <tr>\r\n                  <td colSpan={table.getVisibleFlatColumns().length} className='text-center'>\r\n                    No data available\r\n                  </td>\r\n                </tr>\r\n              </tbody>\r\n            ) : (\r\n              <tbody>\r\n                {table\r\n                  .getRowModel()\r\n                  .rows.slice(0, table.getState().pagination.pageSize)\r\n                  .map(row => {\r\n                    return (\r\n                      <tr key={row.id} className={classnames({ selected: row.getIsSelected() })}>\r\n                        {row.getVisibleCells().map(cell => (\r\n                          <td key={cell.id}>{flexRender(cell.column.columnDef.cell, cell.getContext())}</td>\r\n                        ))}\r\n                      </tr>\r\n                    )\r\n                  })}\r\n              </tbody>\r\n            )}\r\n          </table>\r\n        </div>\r\n        <TablePagination\r\n          component={() => <TablePaginationComponent table={table} />}\r\n          count={table.getFilteredRowModel().rows.length}\r\n          rowsPerPage={table.getState().pagination.pageSize}\r\n          page={table.getState().pagination.pageIndex}\r\n          onPageChange={(_, page) => {\r\n            table.setPageIndex(page)\r\n          }}\r\n        />\r\n      </Card>\r\n\r\n      <ApplicantDetailsModal\r\n        open={detailsModalOpen}\r\n        onClose={() => setDetailsModalOpen(false)}\r\n        applicantData={selectedApplicant}\r\n      />\r\n\r\n      <ResumeViewerModal\r\n        open={resumeModalOpen}\r\n        onClose={() => setResumeModalOpen(false)}\r\n        applicantData={selectedApplicant}\r\n      />\r\n\r\n      {/* Action Menu */}\r\n      <Menu\r\n        anchorEl={actionMenuAnchor}\r\n        open={Boolean(actionMenuAnchor)}\r\n        onClose={handleActionMenuClose}\r\n        anchorOrigin={{\r\n          vertical: 'bottom',\r\n          horizontal: 'right',\r\n        }}\r\n        transformOrigin={{\r\n          vertical: 'top',\r\n          horizontal: 'right',\r\n        }}\r\n      >\r\n        <MenuItem\r\n          onClick={() => {\r\n            const selectedApp = data.find(app => app.id === selectedApplicationId)\r\n            if (selectedApp) {\r\n              handleDownloadResume(selectedApp)\r\n            }\r\n            handleActionMenuClose()\r\n          }}\r\n          sx={{\r\n            display: 'flex',\r\n            alignItems: 'center',\r\n            gap: 2,\r\n            '&:hover': {\r\n              backgroundColor: 'primary.light',\r\n              color: 'primary.main'\r\n            }\r\n          }}\r\n        >\r\n          <i className='tabler-download text-lg' />\r\n          <Typography variant='body2'>Download Resume</Typography>\r\n        </MenuItem>\r\n      </Menu>\r\n    </>\r\n  )\r\n}\r\n\r\nexport default ApplyJobTable\r\n"], "names": [], "mappings": ";;;;AAEA,gBAAgB;AAChB;AAEA,eAAe;AACf;AACA;AAEA,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA,sBAAsB;AACtB;AACA;AACA;AAAA;AAaA,oBAAoB;AACpB;AACA;AACA;AACA;AACA;AACA;AAEA,aAAa;AACb;AACA,0BAA0B;AAC1B;AAEA,cAAc;AACd;AAEA,eAAe;AACf;AAEA,gBAAgB;AAChB;;;AAhEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkEA,MAAM,cAAc,CAAC,KAAK,UAAU,OAAO;IACzC,gBAAgB;IAChB,MAAM,WAAW,CAAA,GAAA,mLAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,QAAQ,CAAC,WAAW;IAElD,0BAA0B;IAC1B,QAAQ;QACN;IACF;IAEA,+CAA+C;IAC/C,OAAO,SAAS,MAAM;AACxB;AAEA,MAAM,iBAAiB,CAAC,EAAE,OAAO,YAAY,EAAE,QAAQ,EAAE,WAAW,GAAG,EAAE,GAAG,OAAO;;IACjF,SAAS;IACT,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,SAAS;QACX;mCAAG;QAAC;KAAa;IACjB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM,UAAU;oDAAW;oBACzB,SAAS;gBACX;mDAAG;YAEH;4CAAO,IAAM,aAAa;;QAC1B,uDAAuD;QACzD;mCAAG;QAAC;KAAM;IAEV,qBAAO,6LAAC,mJAAA,CAAA,UAAe;QAAE,GAAG,KAAK;QAAE,OAAO;QAAO,UAAU,CAAA,IAAK,SAAS,EAAE,MAAM,CAAC,KAAK;;;;;;AACzF;GAjBM;KAAA;AAmBN,qBAAqB;AACrB,uBAAuB;AACvB,MAAM,eAAe;IACnB,SAAS;QACP,OAAO;QACP,OAAO;QACP,MAAM;QACN,aAAa;IACf;IACA,WAAW;QACT,OAAO;QACP,OAAO;QACP,MAAM;QACN,aAAa;IACf;IACA,WAAW;QACT,OAAO;QACP,OAAO;QACP,MAAM;QACN,aAAa;IACf;AACF;AAEA,8BAA8B;AAC9B,MAAM,gBAAgB;IACpB;QAAE,OAAO;QAAW,OAAO;QAAW,OAAO;QAAW,MAAM;IAAe;IAC7E;QAAE,OAAO;QAAW,OAAO;QAAW,OAAO;QAAQ,MAAM;IAAa;IACxE;QAAE,OAAO;QAAa,OAAO;QAAa,OAAO;QAAW,MAAM;IAAe;CAClF;AAED,4BAA4B;AAC5B,MAAM,iBAAiB,CAAC,EAAE,aAAa,EAAE,cAAc,EAAE,aAAa,EAAE;;IACtE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,SAAS,YAAY,CAAC,cAAc,IAAI,aAAa,OAAO;IAElE,MAAM,qBAAqB,CAAC;QAC1B,QAAQ,GAAG,CAAC,oCAAoC,WAAW,mBAAmB;QAC9E,eAAe,eAAe;QAC9B,UAAU;IACZ;IAEA,IAAI,CAAC,QAAQ;QACX,kCAAkC;QAClC,qBACE,6LAAC,oJAAA,CAAA,UAAI;YACH,oBAAM,6LAAC;gBAAE,WAAW,GAAG,OAAO,IAAI,CAAC,mBAAmB,CAAC;;;;;;YACvD,OAAO,OAAO,KAAK;YACnB,OAAO,OAAO,KAAK;YACnB,SAAQ;YACR,MAAK;YACL,WAAU;YACV,SAAS,IAAM,UAAU;YACzB,OAAM;YACN,IAAI;gBACF,QAAQ;oBAAE,IAAI;oBAAQ,IAAI;gBAAO;gBACjC,OAAO;oBAAE,IAAI;oBAAS,IAAI;gBAAQ;gBAClC,UAAU;oBAAE,IAAI;oBAAS,IAAI;gBAAQ;gBACrC,UAAU;oBAAE,IAAI;oBAAS,IAAI;gBAAQ;gBACrC,UAAU;oBAAE,IAAI;oBAAW,IAAI;gBAAS;gBACxC,QAAQ;gBACR,YAAY;gBACZ,oBAAoB;oBAClB,SAAS;wBAAE,IAAI;wBAAS,IAAI;oBAAQ;oBACpC,UAAU;wBAAE,IAAI;wBAAU,IAAI;oBAAU;oBACxC,YAAY;oBACZ,YAAY;oBACZ,UAAU;oBACV,cAAc;gBAChB;gBACA,mBAAmB;oBACjB,UAAU;wBAAE,IAAI;wBAAQ,IAAI;oBAAO;oBACnC,YAAY;wBAAE,IAAI;wBAAO,IAAI;oBAAM;oBACnC,aAAa;wBAAE,IAAI;wBAAO,IAAI;oBAAM;gBACtC;gBACA,WAAW;oBACT,WAAW;oBACX,WAAW;gBACb;YACF;;;;;;IAGN;IAEA,6BAA6B;IAC7B,qBACE,6LAAC,kKAAA,CAAA,UAAW;QAAC,MAAK;QAAQ,IAAI;YAAE,UAAU;QAAI;kBAC5C,cAAA,6LAAC,wJAAA,CAAA,UAAM;YACL,OAAO;YACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;YAClD,SAAS,IAAM,UAAU;YACzB,MAAM;YACN,MAAK;YACL,SAAS;YACT,IAAI;gBACF,QAAQ;gBACR,UAAU;gBACV,uBAAuB;oBACrB,SAAS;oBACT,SAAS;oBACT,YAAY;oBACZ,KAAK;gBACP;YACF;sBAEC,cAAc,GAAG,CAAC,CAAC,uBAClB,6LAAC,4JAAA,CAAA,UAAQ;oBAEP,OAAO,OAAO,KAAK;oBACnB,SAAS,IAAM,mBAAmB,OAAO,KAAK;;sCAE9C,6LAAC,oKAAA,CAAA,UAAY;4BAAC,IAAI;gCAAE,UAAU;4BAAkB;sCAC9C,cAAA,6LAAC;gCAAE,WAAW,GAAG,OAAO,IAAI,CAAC,QAAQ,CAAC;;;;;;;;;;;sCAExC,6LAAC,gKAAA,CAAA,UAAU;4BAAC,SAAQ;4BAAQ,IAAI;gCAAE,UAAU;4BAAU;sCACnD,OAAO,KAAK;;;;;;;mBARV,OAAO,KAAK;;;;;;;;;;;;;;;AAe7B;IA1FM;MAAA;AA4FN,MAAM,eAAe,CAAA,GAAA,wKAAA,CAAA,qBAAkB,AAAD;AAEtC,MAAM,gBAAgB;;IACpB,SAAS;IACT,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IAClD,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACnC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACnD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnE,QAAQ;IACR,MAAM,EAAE,MAAM,MAAM,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEjC,qCAAqC;IACrC,MAAM,sBAAsB;QAC1B,IAAI;YACF,QAAQ,GAAG,CAAC;YACZ,WAAW;YACX,SAAS;YAET,MAAM,eAAe,MAAM,CAAA,GAAA,4HAAA,CAAA,uBAAoB,AAAD;YAC9C,QAAQ,GAAG,CAAC,2BAA2B,aAAa,MAAM,EAAE;YAC5D,QAAQ,GAAG,CAAC,0BAA0B,YAAY,CAAC,EAAE;YAErD,gFAAgF;YAChF,MAAM,yBAAyB,aAAa,GAAG,CAAC,CAAA,cAAe,CAAC;oBAC9D,GAAG,WAAW;oBACd,QAAQ,YAAY,MAAM,IAAI;gBAChC,CAAC;YAED,QAAQ;YACR,gBAAgB;QAClB,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,qCAAqC;YACnD,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,2CAA2C;IAC3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR;QACF;kCAAG,EAAE;IAEL,MAAM,oBAAoB,CAAC;QACzB,qBAAqB;QACrB,oBAAoB;IACtB;IAEA,MAAM,mBAAmB,CAAC;QACxB,qBAAqB;QACrB,mBAAmB;IACrB;IAEA,MAAM,qBAAqB,OAAO,eAAe;QAC/C,QAAQ,GAAG,CAAC,oCAAoC,eAAe,OAAO;QAEtE,IAAI;YACF,uCAAuC;YACvC,MAAM,CAAA,GAAA,4HAAA,CAAA,6BAA0B,AAAD,EAAE,eAAe;YAChD,QAAQ,GAAG,CAAC;YAEZ,0DAA0D;YAC1D,QAAQ,CAAA,WACN,SAAS,GAAG,CAAC,CAAA,OACX,KAAK,EAAE,KAAK,gBACR;wBAAE,GAAG,IAAI;wBAAE,QAAQ;oBAAU,IAC7B;YAGR,gBAAgB,CAAA,WACd,SAAS,GAAG,CAAC,CAAA,OACX,KAAK,EAAE,KAAK,gBACR;wBAAE,GAAG,IAAI;wBAAE,QAAQ;oBAAU,IAC7B;YAIR,QAAQ,GAAG,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4CAA4C;YAC1D,MAAM;QACR;IACF;IAIA,MAAM,0BAA0B,OAAO;QACrC,IAAI;YACF,0DAA0D;YAC1D,MAAM,cAAc,KAAK,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;YAClD,MAAM,gBAAgB,aAAa,YAAY;YAE/C,2BAA2B;YAC3B,MAAM,YAAY,OAAO,OAAO,CAC9B,CAAC,gCAAgC,EAAE,cAAc,8GAA8G,CAAC;YAGlK,IAAI,CAAC,WAAW;gBACd;YACF;YAEA,yCAAyC;YACzC,MAAM,CAAA,GAAA,4HAAA,CAAA,uBAAoB,AAAD,EAAE;YAE3B,sCAAsC;YACtC,QAAQ,CAAA,WAAY,SAAS,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;YACxD,gBAAgB,CAAA,WAAY,SAAS,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;YAEhE,sDAAsD;YACtD,gBAAgB,CAAA;gBACd,MAAM,eAAe;oBAAE,GAAG,aAAa;gBAAC;gBACxC,OAAO,YAAY,CAAC,cAAc;gBAClC,OAAO;YACT;YAEA,uBAAuB;YACvB,MAAM,GAAG,cAAc,6CAA6C,CAAC;QAEvE,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,MAAM;QACR;IACF;IAEA,MAAM,uBAAuB,OAAO;QAClC,IAAI;YACF,IAAI,CAAC,cAAc,MAAM,EAAE;gBACzB,MAAM;gBACN;YACF;YAEA,QAAQ,GAAG,CAAC,2BAA2B,cAAc,QAAQ;YAC7D,QAAQ,GAAG,CAAC,gBAAgB,cAAc,MAAM;YAEhD,MAAM,CAAA,GAAA,4HAAA,CAAA,iBAAc,AAAD,EAAE,cAAc,MAAM,EAAE,cAAc,QAAQ;YACjE,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAE3C,IAAI,eAAe;YACnB,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,UAAU,MAAM,OAAO,CAAC,QAAQ,CAAC,cAAc;gBACxE,gBAAgB;YAClB,OAAO,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,YAAY;gBAC5C,gBAAgB;YAClB,OAAO;gBACL,gBAAgB;YAClB;YAEA,MAAM;QACR;IACF;IAEA,MAAM,uBAAuB,CAAC,OAAO;QACnC,oBAAoB,MAAM,aAAa;QACvC,yBAAyB;IAC3B;IAEA,MAAM,wBAAwB;QAC5B,oBAAoB;QACpB,yBAAyB;IAC3B;IAEA,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;0CACpB,IAAM;gBACJ;oBACE,IAAI;oBACJ,MAAM;0DAAE,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,4JAAA,CAAA,UAAQ;gCAEL,SAAS,MAAM,oBAAoB;gCACnC,eAAe,MAAM,qBAAqB;gCAC1C,UAAU,MAAM,+BAA+B;;;;;;;oBAIrD,IAAI;0DAAE,CAAC,EAAE,GAAG,EAAE,iBACZ,6LAAC,4JAAA,CAAA,UAAQ;gCAEL,SAAS,IAAI,aAAa;gCAC1B,UAAU,CAAC,IAAI,YAAY;gCAC3B,eAAe,IAAI,iBAAiB;gCACpC,UAAU,IAAI,wBAAwB;;;;;;;gBAI9C;gBACA,aAAa,QAAQ,CAAC,YAAY;oBAChC,QAAQ;oBACR,IAAI;0DAAE,CAAC,EAAE,GAAG,EAAE,iBACZ,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,gJAAA,CAAA,UAAY;wCACX,SAAQ;wCACR,OAAM;wCACN,MAAK;wCACL,MAAM;kDAEL,IAAI,QAAQ,CAAC,QAAQ,EAAE,OAAO,IAAI;;;;;;kDAErC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,gKAAA,CAAA,UAAU;gDAAC,OAAM;gDAAe,WAAU;gDAAc,OAAO;oDAAE,UAAU;gDAAS;0DAClF,IAAI,QAAQ,CAAC,QAAQ;;;;;;0DAExB,6LAAC,gKAAA,CAAA,UAAU;gDAAC,SAAQ;gDAAQ,OAAM;gDAAe,WAAU;gDAAc,OAAO;oDAAE,UAAU;oDAAQ,eAAe;gDAAM;0DACtH,IAAI,QAAQ,CAAC,KAAK;;;;;;;;;;;;;;;;;;;gBAK7B;gBACA,aAAa,QAAQ,CAAC,YAAY;oBAChC,QAAQ;oBACR,IAAI;0DAAE,CAAC,EAAE,GAAG,EAAE,iBACZ,6LAAC,gKAAA,CAAA,UAAU;gCAAC,WAAU;gCAAa,OAAM;0CACtC,IAAI,QAAQ,CAAC,QAAQ,IAAI;;;;;;;gBAGhC;gBACA,aAAa,QAAQ,CAAC,cAAc;oBAClC,QAAQ;oBACR,IAAI;0DAAE,CAAC,EAAE,GAAG,EAAE,iBACZ,6LAAC,gKAAA,CAAA,UAAU;gCAAC,OAAM;0CACf,IAAI,QAAQ,CAAC,UAAU,IAAI;;;;;;;gBAGlC;gBACA,aAAa,QAAQ,CAAC,eAAe;oBACnC,QAAQ;oBACR,IAAI;0DAAE,CAAC,EAAE,GAAG,EAAE;4BACZ,MAAM;iFAAiB,CAAC;oCACtB,IAAI,CAAC,YAAY,OAAO;oCAExB,IAAI;wCACF,qDAAqD;wCACrD,MAAM,OAAO,IAAI,KAAK;wCACtB,IAAI,MAAM,KAAK,OAAO,KAAK,OAAO,WAAW,kCAAkC;;wCAE/E,8EAA8E;wCAC9E,MAAM,cAAc;4CAClB,OAAO;4CACP,KAAK;4CACL,MAAM;wCACR;wCACA,MAAM,cAAc;4CAClB,MAAM;4CACN,QAAQ;4CACR,QAAQ;wCACV;wCAEA,MAAM,gBAAgB,KAAK,kBAAkB,CAAC,SAAS;wCACvD,MAAM,gBAAgB,KAAK,kBAAkB,CAAC,SAAS;wCAEvD,OAAO,GAAG,cAAc,CAAC,EAAE,eAAe;oCAC5C,EAAE,OAAO,OAAO;wCACd,OAAO,WAAW,sCAAsC;;oCAC1D;gCACF;;4BAEA,qBACE,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,gKAAA,CAAA,UAAU;oCAAC,OAAM;oCAAe,OAAO;wCAAE,UAAU;wCAAW,YAAY;oCAAM;8CAC9E,eAAe,IAAI,QAAQ,CAAC,WAAW,IAAI,IAAI,QAAQ,CAAC,SAAS;;;;;;;;;;;wBAI1E;;gBACF;gBACA,aAAa,QAAQ,CAAC,UAAU;oBAC9B,QAAQ;oBACR,IAAI;0DAAE,CAAC,EAAE,GAAG,EAAE;4BACZ,MAAM,SAAS,IAAI,QAAQ,CAAC,MAAM,IAAI;4BAEtC,qBACE,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,eAAe;oCACf,gBAAgB;oCAChB,eAAe,IAAI,QAAQ,CAAC,EAAE;;;;;;;;;;;wBAItC;;oBACA,eAAe;gBACjB;gBACA,aAAa,QAAQ,CAAC,UAAU;oBAC9B,QAAQ;oBACR,IAAI;0DAAE,CAAC,EAAE,GAAG,EAAE,iBACZ,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC,gKAAA,CAAA,UAAU;wCACT,OAAO;8EAAE,IAAM,wBAAwB,IAAI,QAAQ,CAAC,EAAE;;wCACtD,OAAM;wCACN,MAAK;wCACL,IAAI;4CACF,OAAO;4CACP,WAAW;gDACT,OAAO;gDACP,iBAAiB;gDACjB,WAAW;4CACb;4CACA,YAAY;wCACd;kDAEA,cAAA,6LAAC;4CAAE,WAAU;;;;;;;;;;;kDAIf,6LAAC,gKAAA,CAAA,UAAU;wCACT,OAAO;8EAAE,IAAM,kBAAkB,IAAI,QAAQ;;wCAC7C,OAAM;wCACN,MAAK;wCACL,IAAI;4CACF,OAAO;4CACP,WAAW;gDACT,OAAO;gDACP,iBAAiB;gDACjB,WAAW;4CACb;4CACA,YAAY;wCACd;kDAEA,cAAA,6LAAC;4CAAE,WAAU;;;;;;;;;;;kDAIf,6LAAC,gKAAA,CAAA,UAAU;wCACT,OAAO;8EAAE,CAAC,IAAM,qBAAqB,GAAG,IAAI,QAAQ,CAAC,EAAE;;wCACvD,OAAM;wCACN,MAAK;wCACL,IAAI;4CACF,OAAO;4CACP,WAAW;gDACT,OAAO;gDACP,iBAAiB;gDACjB,WAAW;4CACb;4CACA,YAAY;wCACd;kDAEA,cAAA,6LAAC;4CAAE,WAAU;;;;;;;;;;;;;;;;;;oBAInB,eAAe;gBACjB;aACD;yCACD;QAAC;QAAM;KAAa;IAGtB,MAAM,QAAQ,CAAA,GAAA,yLAAA,CAAA,gBAAa,AAAD,EAAE;QAC1B,MAAM;QACN;QACA,WAAW;YACT,OAAO;QACT;QACA,OAAO;YACL;YACA;QACF;QACA,cAAc;YACZ,YAAY;gBACV,UAAU;YACZ;QACF;QACA,oBAAoB;QACpB,gBAAgB;QAChB,sBAAsB;QACtB,iBAAiB,CAAA,GAAA,wKAAA,CAAA,kBAAe,AAAD;QAC/B,sBAAsB;QACtB,qBAAqB,CAAA,GAAA,wKAAA,CAAA,sBAAmB,AAAD;QACvC,mBAAmB,CAAA,GAAA,wKAAA,CAAA,oBAAiB,AAAD;QACnC,uBAAuB,CAAA,GAAA,wKAAA,CAAA,wBAAqB,AAAD;QAC3C,oBAAoB,CAAA,GAAA,wKAAA,CAAA,qBAAkB,AAAD;QACrC,wBAAwB,CAAA,GAAA,wKAAA,CAAA,yBAAsB,AAAD;QAC7C,wBAAwB,CAAA,GAAA,wKAAA,CAAA,yBAAsB,AAAD;IAC/C;IAEA,yBAAyB;IACzB,MAAM,sBAAsB;QAC1B,MAAM,eAAe,MAAM,2BAA2B,GAAG,IAAI;QAC7D,OAAO,aAAa,GAAG,CAAC,CAAA,MAAO,IAAI,QAAQ;IAC7C;IAEA,qDAAqD;IACrD,MAAM,sBAAsB;QAC1B,MAAM,eAAe;QAErB,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,gCAAgC,aAAa,MAAM;QAC/D,QAAQ,GAAG,CAAC,kBAAkB;QAC9B,QAAQ,GAAG,CAAC,4BAA4B,YAAY,CAAC,EAAE;QAEvD,IAAI,aAAa,MAAM,KAAK,GAAG;YAC7B,MAAM;YACN;QACF;QAEA,IAAI;YACF,MAAM,MAAM,IAAI,sJAAA,CAAA,UAAK;YACrB,IAAI,YAAY;YAEhB,YAAY;YACZ,IAAI,WAAW,CAAC;YAChB,IAAI,YAAY,CAAC,IAAI,IAAI;YACzB,IAAI,IAAI,CAAC,2CAA2C,IAAI;YACxD,aAAa;YAEb,kBAAkB;YAClB,IAAI,WAAW,CAAC;YAChB,IAAI,YAAY,CAAC,KAAK,KAAK;YAC3B,IAAI,IAAI,CAAC,CAAC,aAAa,EAAE,IAAI,OAAO,kBAAkB,IAAI,EAAE,IAAI;YAChE,aAAa;YACb,IAAI,IAAI,CAAC,CAAC,uBAAuB,EAAE,aAAa,MAAM,EAAE,EAAE,IAAI;YAC9D,aAAa;YAEb,oCAAoC;YACpC,aAAa,OAAO,CAAC,CAAC,KAAK;gBACzB,8BAA8B;gBAC9B,IAAI,YAAY,KAAK;oBACnB,IAAI,OAAO;oBACX,YAAY;gBACd;gBAEA,qBAAqB;gBACrB,IAAI,WAAW,CAAC;gBAChB,IAAI,YAAY,CAAC,IAAI,KAAK;gBAC1B,IAAI,OAAO,CAAC,WAAW;gBACvB,IAAI,IAAI,CAAC,GAAG,QAAQ,EAAE,EAAE,EAAE,IAAI,QAAQ,IAAI,qBAAqB,EAAE,IAAI;gBACrE,aAAa;gBAEb,6BAA6B;gBAC7B,IAAI,YAAY,CAAC,IAAI,KAAK;gBAC1B,IAAI,IAAI,CAAC,IAAI,YAAY,GAAG,KAAK,YAAY;gBAC7C,aAAa;gBAEb,+BAA+B;gBAC/B,IAAI,WAAW,CAAC;gBAChB,IAAI,YAAY,CAAC,GAAG,GAAG;gBACvB,IAAI,OAAO,CAAC,WAAW;gBACvB,IAAI,IAAI,CAAC,yBAAyB,IAAI;gBACtC,aAAa;gBAEb,IAAI,WAAW,CAAC;gBAChB,IAAI,OAAO,CAAC,WAAW;gBAEvB,MAAM,eAAe;oBACnB,CAAC,WAAW,EAAE,IAAI,QAAQ,IAAI,gBAAgB;oBAC9C,CAAC,OAAO,EAAE,IAAI,KAAK,IAAI,gBAAgB;oBACvC,CAAC,OAAO,EAAE,IAAI,KAAK,IAAI,IAAI,WAAW,IAAI,gBAAgB;oBAC1D,CAAC,eAAe,EAAE,IAAI,GAAG,IAAI,gBAAgB;oBAC7C,CAAC,SAAS,EAAE,IAAI,OAAO,IAAI,gBAAgB;iBAC5C;gBAED,aAAa,OAAO,CAAC,CAAA;oBACnB,IAAI,IAAI,CAAC,MAAM,IAAI;oBACnB,aAAa;gBACf;gBAEA,aAAa;gBAEb,0BAA0B;gBAC1B,IAAI,WAAW,CAAC;gBAChB,IAAI,OAAO,CAAC,WAAW;gBACvB,IAAI,IAAI,CAAC,oBAAoB,IAAI;gBACjC,aAAa;gBAEb,IAAI,WAAW,CAAC;gBAChB,IAAI,OAAO,CAAC,WAAW;gBAEvB,MAAM,UAAU;oBACd,CAAC,kBAAkB,EAAE,IAAI,QAAQ,IAAI,iBAAiB;oBACtD,CAAC,uBAAuB,EAAE,IAAI,mBAAmB,IAAI,iBAAiB;oBACtE,CAAC,2BAA2B,EAAE,IAAI,sBAAsB,IAAI,iBAAiB;oBAC7E,CAAC,iBAAiB,EAAE,IAAI,cAAc,IAAI,IAAI,eAAe,IAAI,iBAAiB;oBAClF,CAAC,YAAY,EAAE,IAAI,UAAU,IAAI,iBAAiB;oBAClD,CAAC,sBAAsB,EAAE,IAAI,kBAAkB,IAAI,iBAAiB;oBACpE,CAAC,qBAAqB,EAAE,IAAI,QAAQ,IAAI,iBAAiB;oBACzD,CAAC,oBAAoB,EAAE,IAAI,iBAAiB,IAAI,iBAAiB;oBACjE,CAAC,mBAAmB,EAAE,IAAI,QAAQ,IAAI,gBAAgB;iBACvD;gBAED,QAAQ,OAAO,CAAC,CAAA;oBACd,IAAI,IAAI,CAAC,MAAM,IAAI;oBACnB,aAAa;gBACf;gBAEA,aAAa;gBAEb,8BAA8B;gBAC9B,IAAI,WAAW,CAAC;gBAChB,IAAI,OAAO,CAAC,WAAW;gBACvB,IAAI,IAAI,CAAC,wBAAwB,IAAI;gBACrC,aAAa;gBAEb,IAAI,WAAW,CAAC;gBAChB,IAAI,OAAO,CAAC,WAAW;gBAEvB,6EAA6E;gBAC7E,MAAM,uBAAuB,CAAC;oBAC5B,IAAI,CAAC,YAAY,OAAO;oBACxB,IAAI;wBACF,6DAA6D;wBAC7D,MAAM,OAAO,IAAI,KAAK;wBACtB,IAAI,MAAM,KAAK,OAAO,KAAK,OAAO;wBAElC,MAAM,cAAc;4BAAE,OAAO;4BAAW,KAAK;4BAAW,MAAM;wBAAU;wBACxE,MAAM,cAAc;4BAAE,MAAM;4BAAW,QAAQ;4BAAW,QAAQ;wBAAK;wBAEvE,MAAM,gBAAgB,KAAK,kBAAkB,CAAC,SAAS;wBACvD,MAAM,gBAAgB,KAAK,kBAAkB,CAAC,SAAS;wBAEvD,OAAO,GAAG,cAAc,CAAC,EAAE,eAAe;oBAC5C,EAAE,OAAO,OAAO;wBACd,OAAO;oBACT;gBACF;gBAEA,MAAM,qBAAqB;oBACzB,CAAC,cAAc,EAAE,qBAAqB,IAAI,WAAW,IAAI,IAAI,SAAS,GAAG;oBACzE,CAAC,gBAAgB,EAAE,YAAY,CAAC,IAAI,MAAM,IAAI,UAAU,EAAE,SAAS,WAAW;oBAC9E,CAAC,aAAa,EAAE,IAAI,UAAU,IAAI,gBAAgB;oBAClD,CAAC,WAAW,EAAE,IAAI,SAAS,IAAI,gBAAgB;oBAC/C,CAAC,iBAAiB,EAAE,IAAI,cAAc,IAAI,gBAAgB;oBAC1D,CAAC,QAAQ,EAAE,IAAI,MAAM,GAAG,aAAa,gBAAgB;iBACtD;gBAED,mBAAmB,OAAO,CAAC,CAAA;oBACzB,IAAI,IAAI,CAAC,MAAM,IAAI;oBACnB,aAAa;gBACf;gBAEA,0CAA0C;gBAC1C,aAAa;gBACb,IAAI,YAAY,CAAC,KAAK,KAAK;gBAC3B,IAAI,IAAI,CAAC,IAAI,WAAW,KAAK;gBAC7B,aAAa;YACf;YAEA,eAAe;YACf,MAAM,WAAW,CAAC,+BAA+B,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC;YAC/F,IAAI,IAAI,CAAC;YAET,MAAM,CAAC,sBAAsB,EAAE,aAAa,MAAM,CAAC,qCAAqC,EAAE,UAAU;QAEtG,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;YACnC,MAAM;QACR;IACF;IAIA,qBAAqB;IACrB,IAAI,SAAS;QACX,qBACE,6LAAC,oJAAA,CAAA,UAAI;;8BACH,6LAAC,gKAAA,CAAA,UAAU;oBAAC,OAAM;oBAAmB,WAAU;;;;;;8BAC/C,6LAAC,kKAAA,CAAA,UAAW;oBAAC,WAAU;8BACrB,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,4KAAA,CAAA,UAAgB;;;;;0CACjB,6LAAC,gKAAA,CAAA,UAAU;gCAAC,SAAQ;gCAAQ,OAAM;0CAAiB;;;;;;;;;;;;;;;;;;;;;;;IAO7D;IAEA,mBAAmB;IACnB,IAAI,OAAO;QACT,qBACE,6LAAC,oJAAA,CAAA,UAAI;;8BACH,6LAAC,gKAAA,CAAA,UAAU;oBAAC,OAAM;oBAAmB,WAAU;;;;;;8BAC/C,6LAAC,kKAAA,CAAA,UAAW;;sCACV,6LAAC,sJAAA,CAAA,UAAK;4BAAC,UAAS;4BAAQ,WAAU;sCAC/B;;;;;;sCAEH,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,wJAAA,CAAA,UAAM;oCACL,SAAQ;oCACR,SAAS;oCACT,yBAAW,6LAAC;wCAAE,WAAU;;;;;;8CACzB;;;;;;8CAGD,6LAAC,wJAAA,CAAA,UAAM;oCACL,SAAQ;oCACR,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;oCACrC,yBAAW,6LAAC;wCAAE,WAAU;;;;;;8CACzB;;;;;;;;;;;;;;;;;;;;;;;;IAOX;IAEA,mBAAmB;IACnB,IAAI,CAAC,QAAQ,KAAK,MAAM,KAAK,GAAG;QAC9B,qBACE,6LAAC,oJAAA,CAAA,UAAI;;8BACH,6LAAC,gKAAA,CAAA,UAAU;oBAAC,OAAM;oBAAmB,WAAU;;;;;;8BAC/C,6LAAC,kKAAA,CAAA,UAAW;oBAAC,WAAU;8BACrB,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;;;;;;0CACb,6LAAC,gKAAA,CAAA,UAAU;gCAAC,SAAQ;gCAAK,OAAM;0CAAiB;;;;;;0CAGhD,6LAAC,gKAAA,CAAA,UAAU;gCAAC,SAAQ;gCAAQ,OAAM;0CAAiB;;;;;;;;;;;;;;;;;;;;;;;IAO7D;IAEA,qBACE;;0BACE,6LAAC,oJAAA,CAAA,UAAI;;kCACH,6LAAC,gKAAA,CAAA,UAAU;wBACT,qBACE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;8CAAK;;;;;;gCACL,KAAK,MAAM,GAAG,mBACb,6LAAC,oJAAA,CAAA,UAAI;oCACH,OAAO,GAAG,KAAK,MAAM,CAAC,YAAY,EAAE,KAAK,MAAM,KAAK,IAAI,MAAM,IAAI;oCAClE,OAAM;oCACN,SAAQ;oCACR,MAAK;;;;;;gCAGR,OAAO,IAAI,CAAC,cAAc,MAAM,GAAG,mBAClC,6LAAC,oJAAA,CAAA,UAAI;oCACH,OAAO,GAAG,OAAO,IAAI,CAAC,cAAc,MAAM,CAAC,SAAS,CAAC;oCACrD,OAAM;oCACN,SAAQ;oCACR,MAAK;oCACL,oBAAM,6LAAC;wCAAE,WAAU;;;;;;;;;;;;;;;;;wBAK3B,WAAU;;;;;;kCAEZ,6LAAC,wJAAA,CAAA,UAAY;wBAAC,SAAS;wBAAiB,WAAW;;;;;;kCACnD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,mJAAA,CAAA,UAAe;gCACd,MAAM;gCACN,OAAO,MAAM,QAAQ,GAAG,UAAU,CAAC,QAAQ;gCAC3C,UAAU,CAAA,IAAK,MAAM,WAAW,CAAC,OAAO,EAAE,MAAM,CAAC,KAAK;gCACtD,WAAU;;kDAEV,6LAAC,4JAAA,CAAA,UAAQ;wCAAC,OAAM;kDAAK;;;;;;kDACrB,6LAAC,4JAAA,CAAA,UAAQ;wCAAC,OAAM;kDAAK;;;;;;kDACrB,6LAAC,4JAAA,CAAA,UAAQ;wCAAC,OAAM;kDAAK;;;;;;;;;;;;0CAEvB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,OAAO,gBAAgB;wCACvB,UAAU,CAAA,QAAS,gBAAgB,OAAO;wCAC1C,aAAY;wCACZ,WAAU;;;;;;oCAGX,OAAO,IAAI,CAAC,cAAc,MAAM,GAAG,kBAClC,6LAAC,wJAAA,CAAA,UAAM;wCACL,OAAM;wCACN,SAAQ;wCACR,MAAK;wCACL,yBAAW,6LAAC;4CAAE,WAAU;;;;;;wCACxB,SAAS,IAAM,gBAAgB,CAAC;wCAChC,WAAU;kDACX;;;;;6DAID,6LAAC,wJAAA,CAAA,UAAM;wCACL,OAAM;wCACN,SAAQ;wCACR,MAAK;wCACL,yBAAW,6LAAC;4CAAE,WAAU;;;;;;wCACxB,SAAS;4CACP,MAAM,YAAY,CAAC;4CACnB,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,CAAA;gDAC/B,SAAS,CAAC,IAAI,QAAQ,CAAC,EAAE,CAAC,GAAG;4CAC/B;4CACA,gBAAgB;wCAClB;wCACA,WAAU;kDACX;;;;;;kDAKH,6LAAC,wJAAA,CAAA,UAAM;wCACL,OAAM;wCACN,SAAQ;wCACR,yBAAW,6LAAC;4CAAE,WAAU;;;;;;wCACxB,SAAS;wCACT,UAAU,OAAO,IAAI,CAAC,cAAc,MAAM,KAAK;wCAC/C,WAAU;wCACV,IAAI;4CACF,cAAc;gDACZ,SAAS;gDACT,QAAQ;4CACV;wCACF;;4CACD;4CACc,OAAO,IAAI,CAAC,cAAc,MAAM;4CAAC;;;;;;;kDAEhD,6LAAC,gKAAA,CAAA,UAAU;wCACT,OAAM;wCACN,SAAS;wCACT,UAAU;wCACV,OAAO,UAAU,eAAe;wCAChC,IAAI;4CACF,QAAQ;4CACR,aAAa;4CACb,WAAW;gDACT,iBAAiB;gDACjB,WAAW;4CACb;4CACA,YAAY;wCACd;kDAEA,cAAA,6LAAC;4CAAE,WAAW,CAAC,eAAe,EAAE,UAAU,iBAAiB,IAAI;;;;;;;;;;;;;;;;;;;;;;;kCAIrE,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAM,WAAW,+IAAA,CAAA,UAAW,CAAC,KAAK;;8CACjC,6LAAC;8CACE,MAAM,eAAe,GAAG,GAAG,CAAC,CAAA,4BAC3B,6LAAC;sDACE,YAAY,OAAO,CAAC,GAAG,CAAC,CAAA,uBACvB,6LAAC;8DACE,OAAO,aAAa,GAAG,qBACtB;kEACE,cAAA,6LAAC;4DACC,WAAW,CAAA,GAAA,sIAAA,CAAA,UAAU,AAAD,EAAE;gEACpB,qBAAqB,OAAO,MAAM,CAAC,WAAW;gEAC9C,8BAA8B,OAAO,MAAM,CAAC,UAAU;4DACxD;4DACA,SAAS,OAAO,MAAM,CAAC,uBAAuB;;gEAE7C,CAAA,GAAA,yLAAA,CAAA,aAAU,AAAD,EAAE,OAAO,MAAM,CAAC,SAAS,CAAC,MAAM,EAAE,OAAO,UAAU;gEAC5D;oEACC,mBAAK,6LAAC;wEAAE,WAAU;;;;;;oEAClB,oBAAM,6LAAC;wEAAE,WAAU;;;;;;gEACrB,CAAC,CAAC,OAAO,MAAM,CAAC,WAAW,GAAG,IAAI;;;;;;;;mDAdjC,OAAO,EAAE;;;;;2CAFb,YAAY,EAAE;;;;;;;;;;gCAyB1B,MAAM,mBAAmB,GAAG,IAAI,CAAC,MAAM,KAAK,kBAC3C,6LAAC;8CACC,cAAA,6LAAC;kDACC,cAAA,6LAAC;4CAAG,SAAS,MAAM,qBAAqB,GAAG,MAAM;4CAAE,WAAU;sDAAc;;;;;;;;;;;;;;;yDAM/E,6LAAC;8CACE,MACE,WAAW,GACX,IAAI,CAAC,KAAK,CAAC,GAAG,MAAM,QAAQ,GAAG,UAAU,CAAC,QAAQ,EAClD,GAAG,CAAC,CAAA;wCACH,qBACE,6LAAC;4CAAgB,WAAW,CAAA,GAAA,sIAAA,CAAA,UAAU,AAAD,EAAE;gDAAE,UAAU,IAAI,aAAa;4CAAG;sDACpE,IAAI,eAAe,GAAG,GAAG,CAAC,CAAA,qBACzB,6LAAC;8DAAkB,CAAA,GAAA,yLAAA,CAAA,aAAU,AAAD,EAAE,KAAK,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,KAAK,UAAU;mDAAhE,KAAK,EAAE;;;;;2CAFX,IAAI,EAAE;;;;;oCAMnB;;;;;;;;;;;;;;;;;kCAKV,6LAAC,0KAAA,CAAA,UAAe;wBACd,WAAW,kBAAM,6LAAC,iJAAA,CAAA,UAAwB;gCAAC,OAAO;;;;;;wBAClD,OAAO,MAAM,mBAAmB,GAAG,IAAI,CAAC,MAAM;wBAC9C,aAAa,MAAM,QAAQ,GAAG,UAAU,CAAC,QAAQ;wBACjD,MAAM,MAAM,QAAQ,GAAG,UAAU,CAAC,SAAS;wBAC3C,cAAc,CAAC,GAAG;4BAChB,MAAM,YAAY,CAAC;wBACrB;;;;;;;;;;;;0BAIJ,6LAAC,iKAAA,CAAA,UAAqB;gBACpB,MAAM;gBACN,SAAS,IAAM,oBAAoB;gBACnC,eAAe;;;;;;0BAGjB,6LAAC,6JAAA,CAAA,UAAiB;gBAChB,MAAM;gBACN,SAAS,IAAM,mBAAmB;gBAClC,eAAe;;;;;;0BAIjB,6LAAC,oJAAA,CAAA,UAAI;gBACH,UAAU;gBACV,MAAM,QAAQ;gBACd,SAAS;gBACT,cAAc;oBACZ,UAAU;oBACV,YAAY;gBACd;gBACA,iBAAiB;oBACf,UAAU;oBACV,YAAY;gBACd;0BAEA,cAAA,6LAAC,4JAAA,CAAA,UAAQ;oBACP,SAAS;wBACP,MAAM,cAAc,KAAK,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;wBAChD,IAAI,aAAa;4BACf,qBAAqB;wBACvB;wBACA;oBACF;oBACA,IAAI;wBACF,SAAS;wBACT,YAAY;wBACZ,KAAK;wBACL,WAAW;4BACT,iBAAiB;4BACjB,OAAO;wBACT;oBACF;;sCAEA,6LAAC;4BAAE,WAAU;;;;;;sCACb,6LAAC,gKAAA,CAAA,UAAU;4BAAC,SAAQ;sCAAQ;;;;;;;;;;;;;;;;;;;AAKtC;IAr1BM;;QAeqB,qIAAA,CAAA,YAAS;QAmVpB,yLAAA,CAAA,gBAAa;;;MAlWvB;uCAu1BS", "debugId": null}}]}